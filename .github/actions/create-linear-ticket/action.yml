name: "Create Linear Ticket"
description: "Creates a Linear ticket with customizable properties"

inputs:
  linear-access-token:
    description: "Linear OAuth access token for creating tickets"
    required: true
  team-key:
    description: "Linear team key (e.g., 'INF', 'BRA')"
    required: true
  title:
    description: "Ticket title"
    required: true
  description:
    description: "Ticket description (supports markdown)"
    required: true
  priority:
    description: "Ticket priority (0=No priority, 1=Urgent, 2=High, 3=Medium, 4=Low)"
    required: false

outputs:
  ticket-url:
    description: "URL of the created Linear ticket"
  ticket-id:
    description: "ID of the created Linear ticket"
  ticket-number:
    description: "Number of the created Linear ticket"

runs:
  using: "composite"
  steps:
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: "24"

    - name: Install dependencies
      run: npm install
      shell: bash

    - name: Create Linear ticket
      uses: actions/github-script@v7
      with:
        script: |
          const { LinearClient } = require('@linear/sdk');

          const linearAccessToken = '${{ inputs.linear-access-token }}';
          const teamKey = '${{ inputs.team-key }}';
          const title = '${{ inputs.title }}';
          const description = `${{ inputs.description }}`;
          const priority = '${{ inputs.priority }}' ? parseInt('${{ inputs.priority }}') : null;

          try {
            // Initialize Linear client
            const linear = new LinearClient({ accessToken: linearAccessToken });

            // Get the team
            const teams = await linear.teams();
            const team = teams.nodes.find(t => t.key === teamKey);

            if (!team) {
              core.setFailed(`Team with key '${teamKey}' not found in Linear`);
              return;
            }

            // Prepare ticket input
            const ticketInput = {
              teamId: team.id,
              title: title,
              description: description
            };

            // Add priority if provided
            if (priority !== null) {
              ticketInput.priority = priority;
            }

            // Create the ticket
            const ticket = await linear.issueCreate(ticketInput);

            if (ticket) {
              console.log(`✅ Created Linear ticket: ${ticket.title}`);
              console.log(`🔗 Ticket URL: ${ticket.url}`);
              console.log(`📋 Ticket #${ticket.number}`);
              core.setOutput('ticket-url', ticket.url);
              core.setOutput('ticket-id', ticket.id);
              core.setOutput('ticket-number', ticket.number);
            } else {
              core.setFailed('Failed to create Linear ticket');
            }

          } catch (error) {
            console.error('Error creating Linear ticket:', error);
            core.setFailed(`Error creating Linear ticket: ${error.message}`);
          }
