name: On PR Merge
on:
  pull_request:
    types: [closed]

permissions:
  contents: read
  pull-requests: write

jobs:
  create-linear-ticket:
    if: github.event.pull_request.merged == true
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 1 # Minimal checkout, just for the action files
          sparse-checkout: |
            .github

      - name: Check for modified Terraform files
        id: check-files
        uses: actions/github-script@v7
        env:
          PULL_REQUEST_NUMBER: ${{ github.event.pull_request.number }}
        with:
          script: |
            const files = await github.paginate(
              github.rest.pulls.listFiles,
              {
                owner: context.repo.owner,
                repo: context.repo.repo,
                pull_number: process.env.PULL_REQUEST_NUMBER
              }
            );

            const terraformFiles = [
              "api/app.py",
              "api/merge-base.yaml",
              "scripts/merge_base.py",
              "scripts/merge_vpc.py",
              "scripts/inject_js_api.py"
            ];

            const modified = files
              .map(f => f.filename)
              .filter(f => terraformFiles.includes(f));

            core.setOutput('terraform_reminder_needed', modified.length > 0 ? 'true' : 'false');
            core.setOutput('modified_files', modified.join(','));

            // Format files as a bulleted list for the description
            const formattedFiles = modified.map(file => `- \`${file}\``).join('\n');
            core.setOutput('modified_files_formatted', formattedFiles);

      - name: Create Linear ticket
        id: create-ticket
        if: steps.check-files.outputs.terraform_reminder_needed == 'true'
        uses: ./.github/actions/create-linear-ticket
        with:
          linear-access-token: ${{ secrets.LINEAR_ACCESS_TOKEN }}
          team-key: "INF"
          title: "Terraform Update Required: ${{ github.event.pull_request.title }}"
          description: |
            ## Terraform Update Required

            This ticket was automatically created due to changes in the following files:

            ${{ steps.check-files.outputs.modified_files_formatted }}

            **Related PR:** [PR #${{ github.event.pull_request.number }}](${{ github.event.pull_request.html_url }})

            **Action Required:**
            Please ensure you either:
            1. Update the corresponding [Terraform module](https://github.com/braintrustdata/terraform-aws-braintrust-data-plane), OR
            2. Verify that no Terraform changes are needed for these modifications

      - name: Comment on PR about Linear ticket
        if: steps.check-files.outputs.terraform_reminder_needed == 'true' && steps.create-ticket.outputs.ticket-url
        uses: actions/github-script@v7
        env:
          PULL_REQUEST_NUMBER: ${{ github.event.pull_request.number }}
        with:
          script: |
            const ticketUrl = '${{ steps.create-ticket.outputs.ticket-url }}';
            const ticketNumber = '${{ steps.create-ticket.outputs.ticket-number }}';
            const formattedFiles = '${{ steps.check-files.outputs.modified_files_formatted }}';
            const comment = `## 🔧 Terraform Update Ticket Created

            A Linear ticket has been automatically created for the infrastructure changes in this PR.

            **Files Modified:**
            ${formattedFiles}

            **Linear Ticket:** [${ticketNumber}](${ticketUrl})

            The ticket has been assigned to the INF team for review and action.`;

            await github.rest.issues.createComment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: process.env.PULL_REQUEST_NUMBER,
              body: comment
            });

            console.log('✅ Added comment to PR about Linear ticket creation');
