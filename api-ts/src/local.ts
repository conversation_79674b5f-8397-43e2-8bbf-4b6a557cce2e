import argparse from "argparse";
import { initializeEnv, TS_API_HOST, TS_API_PORT } from "./env";

// This has to be done before the API is initialized
import "./instrumentation/setup_otel";
import { getLogger, initPinoLogger } from "./instrumentation/logger";
initPinoLogger();

import "./install_proxy_local";

import { app, healthServerApp, VERSION } from "./api";
import { startClickhouseEtlLoop } from "./clickhouse_etl";
import { startBrainstoreEtlLoop } from "./brainstore/backfill";
import { startAutomationCronLoop } from "./cron/worker";
import { start as startBillingCronLoop } from "./telemetry/cron/start";

initializeEnv(true);
startClickhouseEtlLoop();
startBrainstoreEtlLoop();
startAutomationCronLoop();
startBillingCronLoop();

const host = TS_API_HOST;
const port = TS_API_PORT;

const healthServerHost = process.env.TS_API_HEALTHSERVER_HOST;
const healthServerPort = process.env.TS_API_HEALTHSERVER_PORT
  ? parseInt(process.env.TS_API_HEALTHSERVER_PORT)
  : undefined;

function main() {
  const parser = new argparse.ArgumentParser({
    description: "Braintrust API",
  });
  parser.add_argument("-v", "--version", {
    action: "version",
    version: VERSION,
  });

  const pino = getLogger();
  app.listen(port, host, () => {
    pino.info(`[server]: Server is running at http://${host}:${port}`);
  });

  if (healthServerHost && healthServerPort) {
    healthServerApp.listen(healthServerPort, healthServerHost, () => {
      pino.info(
        `[health-server]: Health server is running at http://${healthServerHost}:${healthServerPort}`,
      );
    });
  }
}

main();
