// This has to be done before the API is initialized
import "../../instrumentation/setup_otel";
import { initPinoLogger } from "../../instrumentation/logger";
initPinoLogger();

import * as crypto from "crypto";
import express, { Request, Response } from "express";
import fs from "fs";
// https://stackoverflow.com/questions/73308289/typescript-error-converting-a-native-fetch-body-webstream-to-a-node-stream

import { CacheKeyOptions } from "@braintrust/proxy";

import { getTestProxyServerConfig } from "@braintrust/local/dev";
import { initializeEnv, parseBooleanEnv } from "../../env";
import { setProxyConfiguration } from "../proxy";
import { installProxyEndpoints, corsMiddleware } from "../server";
import { handleServerError } from "../server-error-handler";
import { ingest, reset, retrieve } from "./mock-telemetry";
import { z } from "zod";
import bodyParser from "body-parser";
import {
  aggregate,
  clearEnvTelemetryOverride,
  flushStore,
  setEnvTelemetryOverride,
  updateStore,
} from "../../telemetry/aggregation";
import { AggregatedEvent } from "../../telemetry/types";

const MAX_BODY_SIZE = "100mb";

initializeEnv(true);

const installTelemetryEndpoints = ({ app }: { app: express.Application }) => {
  const jsonParser = bodyParser.json({
    type: () => true,
    limit: MAX_BODY_SIZE,
  });

  app.get("/events", (req, res) => {
    const { response, status } = retrieve();
    res.status(status).json(response);
  });

  app.delete("/events", (req, res) => {
    const { response, status } = reset();
    res.status(status).json(response);
  });

  app.post("/events", express.json(), (req, res, next) => {
    const params = z
      .object({
        debug: z
          .string()
          .optional()
          .transform((v) => v === "true"),
        backfill_id: z.string().optional(),
      })
      .parse(req.params);

    const { response, status } = ingest(req.body.events, {
      debug: params.debug ?? false,
      backfill_id: params.backfill_id ?? "",
    });
    res.status(status).json(response);
  });

  app.post("/events/:orgId/aggregate", jsonParser, async (req, res) => {
    const { original, aggregated } = await aggregate(req.body.events);

    await updateStore({
      events: Object.values(aggregated),
      token: "fake-test-token",
    });

    const flushed = await flushStore({
      onlyOrgId: req.params.orgId,
      includeCurrentWindow: true,
    });

    res.status(200).json({
      original,
      aggregated: Object.values(flushed).reduce(
        (acc: AggregatedEvent[], { events }) => [...acc, ...events],
        [],
      ),
    });
  });

  app.delete("/events/:orgId/env", jsonParser, async (req, res) => {
    await clearEnvTelemetryOverride(req.params.orgId);
    res.status(200).json({});
  });

  app.post("/events/:orgId/env", jsonParser, async (req, res) => {
    const { TELEMETRY_ENABLE_AGGREGATION } = z
      .object({
        TELEMETRY_ENABLE_AGGREGATION: z.preprocess(
          (x) => (typeof x === "string" ? parseBooleanEnv(x) : undefined),
          z.boolean().optional(),
        ),
      })
      .parse(req.body);

    if (typeof TELEMETRY_ENABLE_AGGREGATION === "boolean") {
      await setEnvTelemetryOverride(
        req.params.orgId,
        TELEMETRY_ENABLE_AGGREGATION ? "true" : "",
      );
    }

    res.status(200).json({});
  });

  app.delete("/events/:orgId", (req, res, next) => {
    const { response, status } = reset({ orgId: req.params.orgId });
    res.status(status).json(response);
  });

  app.get("/events/:orgId", (req, res) => {
    const { response, status } = retrieve({ orgId: req.params.orgId });
    res.status(status).json(response);
  });
};

(async () => {
  const SERVER_CONFIG = await getTestProxyServerConfig();
  const CACHE_PATH = SERVER_CONFIG.cachePath;
  const CACHE_KEY_OPTIONS: CacheKeyOptions = {
    excludeAuthToken: true,
    excludeOrgName: true,
  };
  const DEFAULT_HEADERS = process.env.TEST_PROXY_DEFAULT_HEADERS
    ? JSON.parse(process.env.TEST_PROXY_DEFAULT_HEADERS)
    : { "x-bt-use-cache": "always" };

  let _cache: Record<string, string> | null = null;
  const _fetchedKeys: Set<string> = new Set<string>();
  async function loadCache(): Promise<Record<string, string>> {
    if (_cache !== null) {
      return _cache;
    }
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
    _cache = JSON.parse(
      await fs.promises.readFile(CACHE_PATH, { encoding: "utf-8" }),
    ) as Record<string, string>;
    return _cache;
  }

  async function dumpCache(opts?: { includeOnlyFetchedKeys?: boolean }) {
    if (_cache === null) {
      return;
    }
    const dumpObj = opts?.includeOnlyFetchedKeys
      ? Object.fromEntries(
          Object.entries(_cache).filter(([k, _v]) => _fetchedKeys.has(k)),
        )
      : _cache;
    await fs.promises.writeFile(
      CACHE_PATH,
      JSON.stringify(dumpObj, null, 2).concat("\n"),
      { encoding: "utf-8" },
    );
  }

  async function cacheGet(
    _encryptionKey: string,
    key: string,
  ): Promise<string | null> {
    const cache = await loadCache();
    const result = cache[key];
    if (result) {
      _fetchedKeys.add(key);

      // Remove content-encoding. This is a hack for back-compat
      const resultData = JSON.parse(result);
      if (resultData.headers && resultData.headers["content-encoding"]) {
        delete resultData.headers["content-encoding"];
        return JSON.stringify(resultData);
      }
    }
    return result ?? null;
  }

  async function cachePut(
    _encryptionKey: string,
    key: string,
    value: string,
  ): Promise<void> {
    const cache = await loadCache();
    cache[key] = value;
  }

  setProxyConfiguration({
    cacheGet,
    cachePut,
    defaultHeaders: DEFAULT_HEADERS,
    digest: async (message: string) => {
      return crypto.createHash("md5").update(message).digest("hex");
    },
    cacheKeyOptions: CACHE_KEY_OPTIONS,
  });

  const app = express();
  app.use(corsMiddleware);

  installProxyEndpoints({
    app,
    proxyServerPort: SERVER_CONFIG.port,
    proxyPrefix: "/v1",
  });

  installTelemetryEndpoints({ app });

  app.get("/proxy/dump-cache", async (_req, res, next) => {
    await dumpCache().catch(next);
    res.send(`Wrote testing cache to ${CACHE_PATH}`);
  });

  app.get("/proxy/dump-cache-only-fetched", async (_req, res, next) => {
    await dumpCache({ includeOnlyFetchedKeys: true }).catch(next);
    res.send(`Wrote testing cache (only fetched keys) to ${CACHE_PATH}`);
  });

  app.listen(SERVER_CONFIG.port, SERVER_CONFIG.host, () => {
    console.log(
      `[server]: Server is running at http://${SERVER_CONFIG.host}:${SERVER_CONFIG.port}`,
    );
  });

  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  app.use((err: unknown, req: Request, res: Response, _next: any) => {
    handleServerError(err, req, res);
  });
})().catch(console.error);
