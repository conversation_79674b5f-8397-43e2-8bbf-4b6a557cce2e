import { z } from "zod";
import { BaseEventSchema } from "./types";

export const FunctionInvokedEventSchema = BaseEventSchema(
  z.literal("FunctionInvokedEvent"),
  z.object({
    org_id: z.string(),
    xact_id: z.string(),
    app_origin: z.string(),
    proxy_url: z.string(),
    project_id: z.string(),
    row_id: z.string(),
    span_id: z.string(),
    root_span_id: z.string(),
    span_parents_csv: z.string().nullish(),
    mode: z.string().optional(),
    timeout_ms: z.number().optional(),
    messages_bytes: z.number(),
    input_bytes: z.number(),
    function_id: z.string(),
    function_type: z.string().nullish(),
    function_data_type: z.string(),
    function_data_bytes: z.number(),
    prompt_data_prompt_type: z.string().optional(),
    prompt_data_bytes: z.number(),
    invoke_method_type: z.string().optional(),
    duration_ms: z.number(),
  }),
).describe("DEPRECATED see AggregatedFunctionInvokedEventSchema");

export type FunctionInvokedEvent = z.infer<typeof FunctionInvokedEventSchema>;

export const FunctionInvokedAggregatedEventSchema = BaseEventSchema(
  z.literal("FunctionInvokedAggregatedEvent"),
  z.object({
    // group by
    org_id: z.string(),

    // stats
    count: z.number(),

    // NOTE: scope reduction: in the future we could group by project_id, and
    //   aggregate various props by _min, _max, _mean, _sum_squares if we needed to use billing
    //   as an analytical store
  }),
);

export type FunctionInvokedAggregatedEvent = z.infer<
  typeof FunctionInvokedAggregatedEventSchema
>;

export const LogInsertedEventSchema = BaseEventSchema(
  z.literal("LogInsertedEvent"),
  z.object({
    object_type: z.string(),
    object_id: z.string(),
    row_id: z.string(),
    org_id: z.string(),
    log_bytes: z.number(),
    project_id: z.string().nullish(),
    xact_id: z.string(),
    span_attributes_type: z.string().nullish(),
    span_id: z.string().nullish(),
    root_span_id: z.string().nullish(),
    span_parents_csv: z.string().nullish(),
    metrics_count: z.number(),
    scores_count: z.number(),
  }),
).describe("DEPRECATED see LogInsertedAggregatedEventSchema");

export type LogInsertedEvent = z.infer<typeof LogInsertedEventSchema>;

export const LogInsertedAggregatedEventSchema = BaseEventSchema(
  z.literal("LogInsertedAggregatedEvent"),
  z.object({
    // group by
    org_id: z.string(),

    // stats accumulated
    count: z.number(),
    log_bytes: z.number(),
    metrics_count: z.number(),
    scores_count: z.number(),
  }),
);

export type LogInsertedAggregatedEvent = z.infer<
  typeof LogInsertedAggregatedEventSchema
>;
