import { z } from "zod";

const upgrade_modal_opened = z.object({ orgName: z.string() });
const signup = z.object({
  source: z.enum(["in_app", "cta_button", "logged_out_playground"]),
});
const signin = z.object({
  source: z.enum(["in_app", "cta_button", "logged_out_playground"]),
});

const projectCreateEntryPoints = [
  "projectsSidebarDropdown",
  "projectsHomePageNewProjectButton",
  "projectsHomePageEmptyState",
  "onboardingFlow",
] as const;

export type ProjectCreateEntryPoint = (typeof projectCreateEntryPoints)[number];

const projectCreateAttempt = z.object({
  projectName: z.string(),
  entryPoint: z.enum(projectCreateEntryPoints).optional(),
});

const projectCreateAbandon = z.object({
  projectName: z.string(),
  reason: z.enum(["error", "closed"]),
  entryPoint: z.enum(projectCreateEntryPoints).optional(),
});

const projectCreate = z.object({
  projectName: z.string(),
  projectId: z.string(),
  entryPoint: z.enum(projectCreateEntryPoints).optional(),
});

export const eventSchemas = {
  upgrade_modal_opened,
  signup,
  signin,
  projectCreateAttempt,
  projectCreate,
  projectCreateAbandon,
} as const;

type EventSchemas = typeof eventSchemas;
export type EventName = keyof EventSchemas;
export type EventProps<K extends EventName> = z.infer<EventSchemas[K]>;
