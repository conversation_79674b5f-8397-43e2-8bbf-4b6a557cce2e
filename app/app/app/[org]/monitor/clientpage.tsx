"use client";

import { parseAsString, parseAs<PERSON>tring<PERSON>iteral, useQueryState } from "nuqs";
import { useSearchParams } from "next/navigation";
import {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";

import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import { MainContentWrapper } from "#/ui/layout/main-content-wrapper";
import { useAnalytics } from "#/ui/use-analytics";
import { useDebouncedCallback } from "#/utils/useDebouncedCallback";

import { TimeRangeSelect } from "./time-controls/time-range-select";

import { MonitorCards } from "./monitor-cards";
import { GroupBySelect, NONE_GROUP } from "./group-by-select";
import { RowTypeSelect } from "./row-type-select";
import { type getProjectSummary, type ProjectSummary } from "../org-actions";
import { useOrg } from "#/utils/user";
import { Body<PERSON>rapper } from "../../body-wrapper";
import { isEmpty } from "@braintrust/core";
import { AccessFailed } from "#/ui/access-failed";
import {
  useViewOperations,
  type ViewParams,
  type View,
} from "#/utils/view/use-view-generic";
import {
  monitorViewOptionsSchema,
  type ViewData,
  type ViewOptions,
} from "@braintrust/core/typespecs";
import { useSessionToken } from "#/utils/auth/session-token";
import { ViewDropdown } from "#/ui/views/view-dropdown";
import { CreateViewDialog } from "#/ui/views/create-view-dialog";
import { RenameViewDialog } from "#/ui/views/rename-view-dialog";
import { DeleteViewDialog } from "#/ui/views/delete-view-dialog";
import {
  rowTypeToViewType,
  viewTypeToRowType,
  createViewAutosaveHandler,
} from "./utils";
import { useQueryFunc } from "#/utils/react-query";
import type { getRecentExperimentIds } from "#/app/api/actions/getRecentExperimentIds/action";
import { useTimeControls } from "./time-controls/use-time-controls";
import { LiveButton } from "#/ui/live-button";
import { useViewState } from "#/utils/view/use-view";
import { useEntityStorage } from "#/lib/clientDataStorage";

export interface Params {
  org: string;
  project: string;
}

export default function ClientPage({
  projectSummary,
}: {
  params: Params;
  projectSummary: ProjectSummary[];
}) {
  const { projectId, orgName } = useContext(ProjectContext);

  useAnalytics({
    page: projectId
      ? {
          category: "monitor",
          props: { project_id: projectId },
        }
      : null,
  });

  if (isEmpty(orgName)) {
    return <AccessFailed objectType="Organization" objectName={orgName} />;
  }

  return <MonitorPageContent serverProjectSummary={projectSummary} />;
}

const viewTypeEnum = monitorViewOptionsSchema.shape.type
  .unwrap()
  .unwrap().options;

function MonitorPageContent({
  serverProjectSummary,
}: {
  serverProjectSummary: ProjectSummary[];
}) {
  const {
    frameStart,
    frameStop,
    rangeValue,
    spanType,
    setExplicitTimeFrame,
    setTimeSpan,
    timeSpan,
    timeFrame,
    chartTimeFrame,
    loadMonitorView,
    startTime,
    timeBucket,
    isLive,
    pause,
    play,
  } = useTimeControls();

  const [viewParam, setViewParam] = useQueryState("v", parseAsString);
  const searchParams = useSearchParams();

  const { name: orgName, id: orgId, api_url: apiUrl } = useOrg();

  const viewParams: ViewParams = useMemo(
    () => ({
      objectType: "org_project",
      objectId: orgId ?? "", //TODO: Handle case where orgId is not set
      viewType: "monitor",
    }),
    [orgId],
  );

  const { getOrRefreshToken } = useSessionToken();

  const queryKey = useMemo(
    () => [
      `views`,
      viewParams.objectType,
      viewParams.objectId,
      viewParams.viewType,
    ],
    [viewParams.objectType, viewParams.objectId, viewParams.viewType],
  );
  const getViewsArgs = useMemo(
    () => ({
      apiUrl,
      getOrRefreshToken,
      viewParams,
    }),
    [apiUrl, getOrRefreshToken, viewParams],
  );

  const {
    views,
    isLoading: isLoadingViews,
    isUpdating: isUpdatingViews,
    createViewAsync,
    updateView,
    deleteView,
  } = useViewOperations({
    queryKey,
    viewParams,
    getViewsArgs,
  });

  const allDataView = useMemo(
    () => ({ id: null, builtin: true, name: "All data" }),
    [],
  );
  const allViews = useMemo(
    () => [allDataView, ...(views ?? [])],
    [allDataView, views],
  );

  const selectedView = useMemo(
    () => allViews.find((v) => v.name === viewParam) ?? allDataView,
    [allViews, viewParam, allDataView],
  );

  const pageIdentifier = `org-monitor-${orgId}`;

  // Get the user's default view preference from localStorage
  const [defaultViewName] = useEntityStorage({
    entityType: "tables",
    entityIdentifier: pageIdentifier,
    key: "defaultView",
  });

  const [projectId, setProjectId, resetProjectId] = useViewState({
    pageIdentifier,
    viewParams,
    entityStorageKey: {
      entityType: "monitorChart",
      identifier: pageIdentifier,
      key: "projectId",
    },
    queryParamState: {
      queryKey: "projectId",
      parser: parseAsString.withDefault(""),
    },
    viewField: "projectId",
  });

  const [rowType, setRowType, resetRowType] = useViewState({
    pageIdentifier,
    viewParams,
    entityStorageKey: {
      entityType: "monitorChart",
      identifier: pageIdentifier,
      key: "rowType",
    },
    queryParamState: {
      queryKey: "type",
      parser: parseAsStringLiteral(viewTypeEnum).withDefault("project"),
    },
    viewField: "type",
  });

  const [groupBy, setGroupBy, resetGroupBy] = useViewState({
    pageIdentifier,
    viewParams,
    entityStorageKey: {
      entityType: "monitorChart",
      identifier: pageIdentifier,
      key: "groupBy",
    },
    queryParamState: {
      queryKey: "groupBy",
      parser: parseAsString.withDefault(""),
    },
    viewField: "groupBy",
  });

  const loadView = useCallback(
    (view: View | null, initialize?: boolean) => {
      if (view?.options) {
        // This page should only receive monitor views
        if ("viewType" in view.options && view.options.viewType === "monitor") {
          loadMonitorView(view);
        } else {
          console.warn("Received non-monitor view on monitor page:", view);
        }
      }

      resetProjectId(view, initialize);
      resetRowType(view, initialize);
      resetGroupBy(view, initialize);

      if (view?.builtin) {
        setViewParam(null);
      } else {
        setViewParam(view?.name ?? null);
      }
    },
    [setViewParam, loadMonitorView, resetProjectId, resetGroupBy, resetRowType],
  );

  const loadViewRef = useRef(loadView);
  useEffect(() => {
    loadViewRef.current = loadView;
  }, [loadView]);

  // Create view options from current state
  const currentViewOptions: ViewOptions = useMemo(
    () => ({
      viewType: "monitor" as const,
      options: {
        spanType,
        rangeValue,
        frameStart,
        frameEnd: frameStop,
        projectId,
        type: rowType,
        groupBy,
      },
    }),
    [spanType, rangeValue, frameStart, frameStop, projectId, rowType, groupBy],
  );

  // Create view data from current state (for monitor views, this is typically minimal)
  const currentViewData: ViewData = useMemo(
    () => ({
      search: null, // Monitor views don't typically have complex search
    }),
    [],
  );

  // Function to handle autosaving of current view
  const autosaveHandler = useMemo(
    () =>
      createViewAutosaveHandler({
        selectedView,
        updateView,
        currentViewData,
        currentViewOptions,
      }),
    [selectedView, updateView, currentViewData, currentViewOptions],
  );

  const handleViewAutosave = useDebouncedCallback(autosaveHandler, 1500);

  // autosave when chart timespan changes
  useEffect(() => {
    let timingOptions = undefined;
    if (typeof timeSpan === "string") {
      timingOptions = {
        spanType: "range" as const,
        rangeValue: timeSpan,
        frameStart: "",
        frameEnd: "",
      };
    } else if (timeSpan.start && timeSpan.end) {
      timingOptions = {
        spanType: "frame" as const,
        frameStart: timeSpan.start + "",
        frameEnd: timeSpan.end + "",
        rangeValue: "",
      };
    }
    handleViewAutosave({
      options: {
        ...timingOptions,
        groupBy: groupBy ?? "",
        type: rowType,
      },
    });
  }, [timeSpan, handleViewAutosave, groupBy, rowType]);

  const onBrush = useCallback(
    (v: [number, number] | null) => {
      if (!v) {
        return;
      }
      setExplicitTimeFrame(Math.round(v[0]), Math.round(v[1]));
    },
    [setExplicitTimeFrame],
  );

  const { data: projects, isLoading: isLoadingProjects } = useQueryFunc<
    typeof getProjectSummary
  >({
    fName: "getProjectSummary",
    args: { org_name: orgName },
    serverData: serverProjectSummary,
  });

  const allProjectIds = useMemo(() => {
    return projects?.map((project) => project.project_id) ?? [];
  }, [projects]);

  const projectIds = useMemo(() => {
    return projectId ? [projectId] : allProjectIds;
  }, [projectId, allProjectIds]);

  const { data: recentExperimentIds, isLoading: isLoadingExperiments } =
    useQueryFunc<typeof getRecentExperimentIds>({
      fName: "getRecentExperimentIds",
      args: { project_id: projectId ?? "", timeFrame },
      queryOptions: {
        enabled: !!projectId && rowType === "experiment",
      },
    });

  const experimentIds = useMemo(
    () => recentExperimentIds ?? [],
    [recentExperimentIds],
  );

  // Track if we've done the initial load
  const hasInitializedView = useRef(false);

  // Handle view parameter loading on page initialization
  useEffect(() => {
    if (isLoadingViews || hasInitializedView.current) {
      return;
    }

    // Handle case where URL parameter exists
    if (viewParam !== null) {
      const viewToLoad = allViews.find((v) => v.name === viewParam);
      if (viewToLoad) {
        loadView(viewToLoad, true);
      } else {
        // View doesn't exist, clear URL and load builtin view
        setViewParam(null);
        loadView(null, true);
      }
      hasInitializedView.current = true;
      return;
    }

    // No URL view parameter - check for other URL params and default view
    const hasAnyUrlParams = searchParams && searchParams.toString().length > 0;

    if (!hasAnyUrlParams && defaultViewName) {
      const defaultViewToLoad = allViews.find(
        (v) => v.name === defaultViewName,
      );
      loadView(defaultViewToLoad || null, true);
    } else {
      loadView(null, true);
    }

    hasInitializedView.current = true;
  }, [
    viewParam,
    isLoadingViews,
    searchParams,
    allViews,
    loadView,
    setViewParam,
    allDataView,
    defaultViewName,
  ]);

  // View picker state
  const [isCreateViewDialogOpen, setIsCreateViewDialogOpen] = useState(false);
  const [isRenameViewDialogOpen, setIsRenameViewDialogOpen] = useState(false);
  const [isDeleteViewDialogOpen, setIsDeleteViewDialogOpen] = useState(false);

  return (
    <BodyWrapper innerClassName="overflow-auto">
      <MainContentWrapper
        hideFooterSpacer
        className="h-auto min-h-[calc(100vh-45px)] pt-2"
      >
        <div className="flex items-center gap-2 pb-3 @container/controls">
          <div className="flex flex-1 items-center gap-2">
            <ViewDropdown
              views={allViews}
              selectedView={selectedView}
              viewNameOverride={
                isUpdatingViews ? (viewParam ?? undefined) : undefined
              }
              loadView={loadView}
              isLoadingViews={isLoadingViews}
              setCreateViewDialogOpen={setIsCreateViewDialogOpen}
              setRenameViewDialogOpen={setIsRenameViewDialogOpen}
              setDeleteViewDialogOpen={setIsDeleteViewDialogOpen}
              pageIdentifier={pageIdentifier}
            />
            <RowTypeSelect
              projects={projects}
              value={
                rowType === "project" && !projectId
                  ? "org_logs"
                  : viewTypeToRowType(rowType)
              }
              projectId={projectId}
              onChange={useCallback(
                (newValue, projectId) => {
                  setRowType(rowTypeToViewType(newValue));
                  setProjectId(projectId ?? "");
                  handleViewAutosave({
                    options: {
                      type: rowTypeToViewType(newValue),
                      projectId,
                    },
                  });
                },
                [setRowType, setProjectId, handleViewAutosave],
              )}
            />
            {projectId && (
              <GroupBySelect
                projectId={projectId}
                startTime={startTime}
                value={groupBy ?? NONE_GROUP}
                onChange={(newValue: string | null) => {
                  setGroupBy(newValue ?? "");
                  handleViewAutosave({
                    options: {
                      groupBy: newValue,
                    },
                  });
                }}
                from={rowType === "experiment" ? "experiment" : "project_logs"}
                experimentIds={experimentIds}
              />
            )}
          </div>
          <TimeRangeSelect
            timeSpan={timeSpan}
            chartTimeFrame={chartTimeFrame}
            isLive={isLive}
            onChange={setTimeSpan}
          />
          <LiveButton
            isLive={isLive}
            newRows={0}
            onPause={pause}
            onPlay={play}
            variant="border"
            onRefresh={async () => {}}
          />
        </div>
        <MonitorCards
          projectIds={projectIds}
          chartTimeFrame={chartTimeFrame}
          timeBucket={timeBucket}
          groupBy={groupBy ?? undefined}
          from={rowType === "experiment" ? "experiment" : "project_logs"}
          experimentIds={experimentIds}
          isLoadingFromIds={isLoadingProjects || isLoadingExperiments}
          onBrush={onBrush}
        />
      </MainContentWrapper>
      <CreateViewDialog
        isCreateViewDialogOpen={isCreateViewDialogOpen}
        setIsCreateDatasetDialogOpen={setIsCreateViewDialogOpen}
        createView={useCallback(
          async (name: string) => {
            const newView = await createViewAsync({
              name,
              viewData: currentViewData,
              options: currentViewOptions,
            });
            // Switch to the newly created view
            if (newView) {
              loadView(newView);
            }
            return newView;
          },
          [createViewAsync, currentViewData, currentViewOptions, loadView],
        )}
      />
      <RenameViewDialog
        isRenameViewDialogOpen={isRenameViewDialogOpen}
        setIsRenameViewDialogOpen={setIsRenameViewDialogOpen}
        view={selectedView}
        renameView={useCallback(
          ({ name }: { name: string }) => {
            updateView({
              viewId: selectedView.id!,
              name,
              viewData: currentViewData,
              options: currentViewOptions,
            });
            setViewParam(name);
          },
          [
            updateView,
            selectedView.id,
            currentViewData,
            currentViewOptions,
            setViewParam,
          ],
        )}
      />
      <DeleteViewDialog
        isDeleteViewDialogOpen={isDeleteViewDialogOpen}
        setIsDeleteViewDialogOpen={setIsDeleteViewDialogOpen}
        view={selectedView}
        deleteView={useCallback(
          () => deleteView(selectedView.id!),
          [deleteView, selectedView.id],
        )}
      />
    </BodyWrapper>
  );
}
