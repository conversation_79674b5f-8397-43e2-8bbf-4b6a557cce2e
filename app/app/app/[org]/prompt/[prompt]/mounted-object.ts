import type { DataObjectType } from "#/utils/btapi/btapi";
import {
  CreatedField,
  DatasetIdField,
  ProjectIdField,
  IdField,
  TransactionIdField,
  useDBQuery,
  useParquetView,
  type UpdateLog,
  useDuckDB,
  dbQuery,
  MetadataField,
} from "#/utils/duckdb";
import {
  type SetStateAction,
  type Dispatch,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from "react";
import { type DML, useMutableObject } from "#/utils/mutable-object";
import { type SavingState } from "#/ui/saving";
import { DuckDBSchemaHints, parseObjectJSON } from "#/utils/schema";
import { IS_MERGE_FIELD } from "@braintrust/core";
import { type TypeMap, Field, Schema, Utf8 } from "apache-arrow";
import { ProjectContext } from "../../p/[project]/projectContext";
import { newId } from "braintrust";
import { type Roster } from "#/utils/realtime-data";
import { SINGLETON_DATASET_ID } from "../../p/[project]/playgrounds/[playground]/playx/stream";
import { doubleQuote, singleQuote } from "#/utils/sql-utils";
import {
  createTempTable,
  useCleanupTempTables,
} from "#/utils/queries/useTempDuckTable";
import { useQuery } from "@tanstack/react-query";
import {
  ComputedMetricFields,
  makeComparisonKeySQL,
} from "@braintrust/local/query";
import { datasetSpanTypeInfoExpr } from "../../p/[project]/experiments/[experiment]/(queries)/useExperiment";
import { type AsyncDuckDBConnection } from "@duckdb/duckdb-wasm";
import { fetchStructure } from "#/utils/queries/schema";
import { type Clause } from "#/utils/search/search";

export const METRIC_FIELDS = [...ComputedMetricFields, "start", "end"];

type TableScanProps = {
  scoreNames: string[];
  comparisonKey: string | null;
  filters?: Clause<"filter">[];
};

export interface MountedObjectParams {
  objectType?: DataObjectType;
  objectId?: string;
  objectIdReady?: boolean;
  projectId?: string;
  setSavingState: Dispatch<SetStateAction<SavingState>>;
  disableCache?: boolean;
  defaultScan?: string;
  tableScanProps?: TableScanProps;
}

export const SINGLETON_DATASET_SCAN = `
  SELECT
    ${singleQuote(SINGLETON_DATASET_ID)} as id,
    ${singleQuote(SINGLETON_DATASET_ID)} as span_id,
    ${singleQuote(SINGLETON_DATASET_ID)} as root_span_id,
    ${singleQuote(SINGLETON_DATASET_ID)} as dataset_id,
    TRUE as is_root,
    NULL as input,
    NULL as expected,
    NULL as metadata,
    NULL as tags,
    '0' as _xact_id,
    null as origin,
    now() AS created
`;

export type LoadedObject = {
  id: string;
  scan: string;
  schema: Schema<TypeMap>;
  refreshed: number;
  channel: () => UpdateLog | null;
  roster: Roster;
};

function normalizeObjectQuery(
  objectType?: DataObjectType,
  scan?: string | null,
): string | null {
  if (!objectType || !scan) {
    return null;
  }

  let projection = null;
  switch (objectType) {
    case "dataset":
      projection = `
        input,
        expected,
        metadata,
      `;
      break;
    case "experiment":
    case "prompt_session":
      throw new Error(`Unsupported object type ${objectType}`);
  }

  return `SELECT
    id,
    "${CreatedField}",
    "${TransactionIdField}",
    ${projection}

    FROM (
      ${scan}
    )
    ORDER BY "${CreatedField}" DESC, "${IdField}" DESC
  `;
}

function getJsonStructureQuery(
  objectType: DataObjectType | null,
  scan: string | null,
  schema: Schema | null,
) {
  if (!objectType || !scan) return null;

  const fields: { name: string; alias?: string }[] = [];

  switch (objectType) {
    case "dataset":
      fields.push(
        ...DuckDBSchemaHints.dataset
          .map((name) => ({
            name,
          }))
          .filter(({ name }) => schema?.fields.find((f) => f.name === name)),
      );
      break;
    case "experiment":
    case "prompt_session":
      throw new Error(`Unsupported object type: ${objectType}`);
  }

  const fieldsToSelect = fields
    .map(({ name, alias }) => {
      return `json_group_structure(json(${name})) as "${alias || name}"`;
    })
    .join(",");

  return `SELECT ${fieldsToSelect} FROM (${scan})`;
}

export function useMountedObject({
  objectType: objectTypeProp,
  objectId,
  objectIdReady,
  setSavingState,
  disableCache,
  defaultScan = SINGLETON_DATASET_SCAN,
  tableScanProps,
}: MountedObjectParams) {
  const objectType = objectTypeProp || "dataset";
  const { scan, schema, channel, refreshed, roster } = useParquetView({
    objectType,
    search: objectId,
    disableCache,
  });

  const duck = useDuckDB();
  const { data, isLoading } = useQuery({
    queryKey: [
      "mountedObject",
      objectType,
      {
        objectId,
        scan,
        schema,
        refreshed,
        channel,
        roster,
        defaultScan,
      },
      tableScanProps,
    ],
    queryFn: async ({ signal }) => {
      const conn = await duck!.connect();

      const baseScan = objectId ? scan! : defaultScan;
      let baseTableScan = baseScan;
      let meta;
      if (tableScanProps) {
        const { query, meta: _meta } = await makeTableScan({
          conn,
          signal,
          scan: baseScan,
          ...tableScanProps,
        });
        baseTableScan = query;
        meta = _meta;
      }
      const tableNameHashKey = `mounted_object_${objectType}`;
      const tempTableName = await createTempTable(conn, signal, {
        tableNameHashKey,
        scan: baseTableScan,
      });

      if (!tempTableName) {
        throw new Error("Failed to create temp table");
      }

      const tableScan = `(SELECT * FROM ${doubleQuote(tempTableName)})`;
      const [result, count] = await Promise.all([
        dbQuery(
          conn,
          signal,
          tableScan ? `SELECT * FROM (${tableScan}) LIMIT 0` : null,
        ),
        dbQuery(
          conn,
          signal,
          `SELECT COUNT(1) as count FROM (${baseTableScan})`,
        ).then((r) => {
          const count = r?.get(0)?.count;
          return count ? Number(count) : 0;
        }),
      ]);
      if (!result) {
        throw new Error("Failed to query temp table");
      }

      return {
        loadedObject: {
          id: objectId ?? SINGLETON_DATASET_ID,
          scan: tableScan,
          schema: result.schema,
          refreshed: objectId ? refreshed : 1,
          channel,
          roster,
          count,
        },
        tempTableName,
        meta,
      };
    },
    throwOnError: false,
    staleTime: Infinity,
    // never cache this query, since the data is tied to the same table name
    gcTime: 0,
    enabled:
      !!duck && ((!!scan && refreshed > 0) || (!objectId && objectIdReady)),
  });
  useCleanupTempTables("mounted-object", data?.tempTableName);

  const [loadedRefreshed, setLoadedRefreshed] = useState(0);
  useEffect(() => {
    setLoadedRefreshed((prev) =>
      !data?.loadedObject?.refreshed ? 0 : prev + 1,
    );
  }, [data]);

  const loadedObject: LoadedObject & { count: number } = useMemo(() => {
    return data?.loadedObject
      ? {
          ...data.loadedObject,
          refreshed: loadedRefreshed,
        }
      : {
          id: "",
          scan: "",
          schema: new Schema([]),
          refreshed: 0,
          channel: (): UpdateLog | null => null,
          roster: [],
          count: 0,
        };
  }, [data?.loadedObject, loadedRefreshed]);

  const isLoaded = objectId === data?.loadedObject?.id;
  const { data: loadedData, hasLoaded: hasLoadedData } = useDBQuery(
    normalizeObjectQuery(
      objectTypeProp,
      objectIdReady && isLoaded ? data?.loadedObject?.scan : null,
    ),
    [loadedObject.refreshed],
  );

  const { data: jsonStructureTable, hasLoaded: hasLoadedJsonStructureTable } =
    useDBQuery(
      getJsonStructureQuery(objectType, isLoaded ? scan : null, schema),
      [loadedObject.refreshed],
    );

  const jsonStructure = useMemo(
    () =>
      parseObjectJSON(
        "dataset",
        jsonStructureTable?.toArray()?.[0].toJSON() ?? {},
      ),
    [jsonStructureTable],
  );

  const {
    upsert,
    prepareUpdates: prepareUpdatesRaw,
    prepareDeletes: prepareDeletesRaw,
    prepareUpserts: prepareUpsertsRaw,
    update: updateRaw,
    commentOn,
    deleteComment,
  } = useMutableObject({
    scan,
    channel,
    objectType,
    setSavingState,
  });

  const normalizeRow = useCallback(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
    (row: any) => {
      return {
        ...row,
        [`${objectType}_id`]: objectId,
      };
    },
    [objectId, objectType],
  );

  const dml: DML = useMemo(
    () => ({
      upsert,
      prepareUpdates: async (
        // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
        rows: any[],
        // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
        updates: { path: string[]; newValue: any }[],
      ) => await prepareUpdatesRaw(rows.map(normalizeRow), updates),
      // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
      prepareDeletes: async (rows: any[]) =>
        await prepareDeletesRaw(rows.map(normalizeRow)),
      // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
      prepareUpserts: async (rows: any[]) =>
        await prepareUpsertsRaw(rows.map(normalizeRow)),
      update: async (
        // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
        rows: any[],
        // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
        updates: { path: string[]; newValue: any }[],
      ) => await updateRaw(rows.map(normalizeRow), updates),
      commentOn,
      deleteComment,
    }),
    [
      upsert,
      commentOn,
      deleteComment,
      prepareUpdatesRaw,
      normalizeRow,
      prepareDeletesRaw,
      prepareUpsertsRaw,
      updateRaw,
    ],
  );

  const projectContext = useContext(ProjectContext);
  const projectId = projectContext.projectId;

  const addRow = useCallback(
    async (onOptimisticUpdate: () => void) => {
      const id = newId();
      await dml.upsert(
        await dml.prepareUpserts([
          {
            [DatasetIdField]: objectId,
            [ProjectIdField]: projectId,
            [IS_MERGE_FIELD]: false,
            [IdField]: id,
          },
        ]),
        {
          onOptimisticUpdate,
        },
      );
      return id;
    },
    [objectId, projectId, dml],
  );

  return {
    data: loadedData,
    dml,
    jsonStructure,
    hasLoaded: hasLoadedData && hasLoadedJsonStructureTable,
    addRow,
    loadedObject,
    isLoading: isLoading || !isLoaded,
    tableScanMeta: data?.meta,
  };
}

// Hack for playgrounds to stabilize the score schema since we use placeholder data to show dataset columns
// and we need the schema to have score values to display the proper columns.
// Since the placeholder data fn can't be async, we set up this query so that the schema that we get
// can be used as a spanSummarySchema with scores
async function makeTableScan({
  conn,
  signal,
  scan,
  scoreNames,
  comparisonKey,
  filters,
}: {
  conn: AsyncDuckDBConnection;
  signal: AbortSignal;
  scan: string;
} & TableScanProps) {
  // Generated metadata fields here so that table grouping is stable while running the playground.
  // Maybe in the future we could do a full objectSchemaFieldsQuery but this should be good enough for now.
  const metadataFields = await fetchStructure(
    conn,
    signal,
    scan,
    new Schema([new Field(MetadataField, new Utf8())]),
    [MetadataField],
  );

  // stabilize these columns for list view
  const spanTypeInfoExpr = datasetSpanTypeInfoExpr({});
  const scoresStructExpr =
    scoreNames.length > 0
      ? `struct_pack(${scoreNames.map((s) => `${doubleQuote(s)} := NULL::DOUBLE`).join(",")})`
      : "NULL";
  const metricsStructExpr = `struct_pack(${METRIC_FIELDS.map((f) => `"${f}" := NULL::DOUBLE`).join(",")})`;
  return {
    query: `SELECT *,
          ${spanTypeInfoExpr} as span_type_info,
          NULL::JSON as output,
          ${scoresStructExpr} as scores,
          ${metricsStructExpr} as metrics,
          NULL::text AS playground_row_id,
          NULL::text AS playground_xact_id,
          NULL as error,
          COALESCE(MD5(${makeComparisonKeySQL({
            comparisonKey,
          })}), '<empty>') as comparison_key
        FROM (${scan}) base
        WHERE ${
          filters?.length
            ? filters.map((f) => f.btql?.sql.toPlainStringQuery()).join(" AND ")
            : "true"
        }`,
    meta: {
      scoreNames,
      metadataFields: metadataFields[0] || [],
    },
  };
}
