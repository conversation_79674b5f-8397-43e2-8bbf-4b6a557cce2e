import type Orb from "orb-billing";
import Link from "next/link";
import { ChartNoAxesColumn, CreditCard, Receipt } from "lucide-react";
import { cn } from "#/utils/classnames";
import { But<PERSON>, buttonVariants } from "#/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "#/ui/dropdown-menu";
import { isEnterprisePlan, isFreePlan, isProPlan } from "./plans";

function formatDate(date: string | null): string {
  if (!date) return "unknown";
  return new Date(date).toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });
}

function CurrentSubscriptionSummary({
  currentSubscription,
  paymentMethodSummary,
  invoices,
}: {
  currentSubscription: Orb.Subscription;
  paymentMethodSummary: {
    last4: string;
    brand: string;
    exp_month: number;
    exp_year: number;
  } | null;
  invoices: Orb.Invoice[] | null;
}) {
  const planId = currentSubscription.plan.id;
  const planName = currentSubscription.plan.name;
  const status = currentSubscription.status;
  const startDate = formatDate(currentSubscription.start_date);
  const renewsOn = formatDate(
    currentSubscription.current_billing_period_end_date,
  );

  let subscriptionStatusMessage = isFreePlan(planId)
    ? "This organization is currently on the free plan"
    : `This organization is currently on the ${planName} plan and the subscription is ${status}`;
  const isEnterprise = isEnterprisePlan(currentSubscription.plan);
  if (isEnterprise) {
    subscriptionStatusMessage = `This organization is currently on an Enterprise contract`;
  }

  const customerPortalUrl = currentSubscription.customer.portal_url;

  const isPro = isProPlan(planId);

  return (
    <div className="mb-12">
      <h2 className="mb-4 text-lg font-semibold">Current plan</h2>
      <div className="rounded-md border p-6">
        <p className="mb-1 text-base">{subscriptionStatusMessage}</p>
        {isPro && (
          <p className="mb-1 text-sm text-primary-600">
            This plan was activated on {startDate} and renews on {renewsOn}.
          </p>
        )}
        {paymentMethodSummary && (
          <p className="my-4 font-mono text-sm text-primary-600">
            {paymentMethodSummary.brand && (
              <span className="capitalize">{paymentMethodSummary.brand}</span>
            )}{" "}
            **** {paymentMethodSummary.last4}
            {paymentMethodSummary.exp_month &&
              paymentMethodSummary.exp_year && (
                <span className="ml-2">
                  Exp: {String(paymentMethodSummary.exp_month).padStart(2, "0")}
                  /{String(paymentMethodSummary.exp_year).slice(-2)}
                </span>
              )}
          </p>
        )}

        <div className="flex gap-2">
          {customerPortalUrl && (
            <Link
              target="_blank"
              href={customerPortalUrl}
              className={cn(
                buttonVariants({
                  size: "sm",
                }),
              )}
            >
              <ChartNoAxesColumn className="size-3" />
              View usage
            </Link>
          )}
          {invoices?.length && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  size="sm"
                  Icon={Receipt}
                  iconClassName="size-3"
                  isDropdown
                >
                  Invoices
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-80">
                <DropdownMenuLabel>Recent invoices</DropdownMenuLabel>
                {invoices.map((invoice) => (
                  <DropdownMenuItem key={invoice.id} className="p-0">
                    <Link
                      href={invoice.hosted_invoice_url || "#"}
                      target="_blank"
                      className="flex w-full flex-col gap-1 p-2 text-xs"
                    >
                      <div className="flex items-center justify-between">
                        <span className="font-medium">
                          {invoice.invoice_number ||
                            `Invoice ${invoice.id.slice(-8)}`}
                        </span>
                        <span className="tabular-nums">
                          {new Intl.NumberFormat("en-US", {
                            style: "currency",
                            currency:
                              invoice.currency === "credits"
                                ? "USD"
                                : invoice.currency,
                          }).format(parseFloat(invoice.amount_due))}
                        </span>
                      </div>
                      <div className="flex items-center justify-between text-primary-500">
                        <span>{formatDate(invoice.invoice_date)}</span>
                        <span className="capitalize">{invoice.status}</span>
                      </div>
                    </Link>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          )}
          {paymentMethodSummary && (
            <Link
              href="billing/payment"
              className={cn(buttonVariants({ size: "sm" }))}
            >
              <CreditCard className="size-3" />
              Update payment method
            </Link>
          )}
        </div>
      </div>
    </div>
  );
}

export { CurrentSubscriptionSummary };
