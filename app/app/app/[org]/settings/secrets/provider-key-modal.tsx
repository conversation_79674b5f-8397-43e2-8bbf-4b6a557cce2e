"use client";

import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle } from "#/ui/dialog";
import {
  AISecretTypes,
  AzureMetadataSchema,
  BaseMetadataSchema,
  BedrockMetadataSchema,
  ModelSchema,
  ModelEndpointType,
  ModelFormats,
  type ModelSpec,
  OpenAIMetadataSchema,
  PromptInputs,
  VertexMetadataSchema,
  AzureEntraSecretSchema,
  type AzureEntraSecret,
  DatabricksMetadataSchema,
  DatabricksOAuthSecretSchema,
  type DatabricksOAuthSecret,
  MistralMetadataSchema,
} from "@braintrust/proxy/schema";
import React, {
  type Dispatch,
  type SetStateAction,
  useCallback,
  useEffect,
  useMemo,
  useReducer,
  useState,
} from "react";
import { z, type ZodError } from "zod";
import {
  type FieldValues,
  type Path,
  type UseFieldArrayRemove,
  type UseFormProps,
  useFieldArray,
  useForm,
  useFormContext,
} from "react-hook-form";
import { <PERSON>, FormField, FormMessage } from "#/ui/form";
import {
  checkValidateAndParse,
  customModelArrayToObject,
  CustomModelFormArraySchema,
  CustomModelFormSchema,
  customModelObjectToArray,
  getDisplayName,
  getPlaceholderName,
  getProviderIcon,
  providerReadableName,
  testKey,
} from "./utils";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "#/ui/dropdown-menu";
import { Button } from "#/ui/button";
import { AlertTriangle, ChevronDown, Trash2 } from "lucide-react";
import { zodResolver } from "@hookform/resolvers/zod";
import CustomModelForm, { DefaultCustomModel } from "./custom-model-form";
import { toast } from "sonner";
import { Input } from "#/ui/input";
import {
  type TextEditorValue,
  UpdateableDataTextEditor,
} from "#/ui/data-text-editor";
import { zodErrorToString } from "#/utils/validation";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "#/ui/tabs";
import { type AISecret } from "@braintrust/core/typespecs";
import { Switch } from "#/ui/switch";
import { ProviderFormField } from "./provider-form-field";
import { ExternalLink } from "#/ui/link";
import TextArea from "#/ui/text-area";
import { RadioGroup, RadioGroupItem } from "#/ui/radio";
import { TestKeyButton } from "./test-key-button";
import { modelsByProvider } from "#/ui/prompts/models";
import { cn } from "#/utils/classnames";

export type ModalType = "DEFAULT" | "CUSTOM";

const HeaderFormSchema = z.object({
  name: z.string().min(1, "Name cannot be empty"),
  value: z.string().min(1, "Value cannot be empty"),
});
const HeaderFormArraySchema = z.array(HeaderFormSchema).nullish();

const vertexModelsHeaderSchema = z.object({
  customModels: CustomModelFormArraySchema.nullish(),
  additionalHeaders: HeaderFormArraySchema,
});

const customModelsArraySchema = z.array(CustomModelFormSchema).nullish();

const modelsHeaderSchema = z.object({
  customModels: customModelsArraySchema,
  additionalHeaders: HeaderFormArraySchema,
});

const formSchemas = {
  openai: OpenAIMetadataSchema.omit({
    customModels: true,
    models: true,
    additionalHeaders: true,
  }).merge(modelsHeaderSchema),
  azure: AzureMetadataSchema.omit({
    customModels: true,
    models: true,
    additionalHeaders: true,
  }).merge(modelsHeaderSchema),
  bedrock: BedrockMetadataSchema.omit({
    customModels: true,
    models: true,
    additionalHeaders: true,
  }).merge(modelsHeaderSchema),
  vertex: VertexMetadataSchema.omit({
    authType: true,
    customModels: true,
    models: true,
    additionalHeaders: true,
  }).merge(vertexModelsHeaderSchema),
  databricks: DatabricksMetadataSchema.omit({
    customModels: true,
    models: true,
    additionalHeaders: true,
  }).merge(modelsHeaderSchema),
  mistral: MistralMetadataSchema.omit({
    customModels: true,
    models: true,
    additionalHeaders: true,
  }).merge(modelsHeaderSchema),
  base: BaseMetadataSchema.omit({
    customModels: true,
    models: true,
    additionalHeaders: true,
  }).merge(modelsHeaderSchema),
};

type VertexAuthType = z.infer<typeof VertexMetadataSchema.shape.authType>;
type AzureAuthType = z.infer<typeof AzureMetadataSchema.shape.auth_type>;
type DatabricksAuthType = z.infer<
  typeof DatabricksMetadataSchema.shape.auth_type
>;

type ErrorState = {
  name?: React.ReactNode;
  secret?: React.ReactNode;
  other?: React.ReactNode;
};

type ErrorAction =
  | { type: "SET_NAME_ERROR"; message: React.ReactNode }
  | { type: "SET_SECRET_ERROR"; message: React.ReactNode }
  | { type: "VALIDATE_SECRET_ERROR"; message: React.ReactNode }
  | { type: "SET_OTHER_ERROR"; message: React.ReactNode }
  | { type: "CLEAR_ERRORS" };

function errorReducer(state: ErrorState, action: ErrorAction): ErrorState {
  switch (action.type) {
    case "SET_NAME_ERROR":
      return { ...state, name: action.message };
    case "SET_SECRET_ERROR":
      return { ...state, secret: action.message };
    case "VALIDATE_SECRET_ERROR":
      return { ...state, secret: action.message };
    case "SET_OTHER_ERROR":
      return { ...state, other: action.message };
    case "CLEAR_ERRORS":
      return {};
    default:
      return state;
  }
}

const EncryptionMessage = ({ row }: { row?: AISecret }) => (
  <div className="text-xs text-primary-500">
    This secret will be encrypted at rest using{" "}
    <a
      href="https://en.wikipedia.org/wiki/Transparent_data_encryption"
      target="_blank"
      className="text-accent-600"
    >
      Transparent Data Encryption
    </a>{" "}
    with a{" "}
    <a
      href="https://libsodium.gitbook.io/doc/secret-key_cryptography/aead"
      target="_blank"
      className="text-accent-600"
    >
      unique 256-bit key and nonce
    </a>
    .{" "}
    {row?.preview_secret && (
      <>
        The previous secret value may be cached for 60 seconds after updating.
      </>
    )}
  </div>
);

const SecretInput = ({
  value,
  onChange,
  secretSet,
  setSecretSet,
  placeholder = "Enter secret",
  className,
}: {
  value: string;
  onChange: (value: string) => void;
  secretSet: boolean;
  setSecretSet: (value: boolean) => void;
  placeholder?: string;
  className?: string;
}) => (
  <Input
    className={`h-8 ${className ?? ""}`}
    placeholder={placeholder}
    type="password"
    value={value}
    onChange={(e) => {
      onChange(e.target.value);
      setSecretSet(true);
    }}
    onKeyDown={(e) => {
      const regex = /^[A-Za-z0-9`~!@#$%^&*()\_\-\+={}[\]\|\:;"'<>,.?\/}]{1}$/;
      if (!secretSet && (e.key === "Backspace" || regex.test(e.key))) {
        onChange("");
      }
    }}
  />
);

function obscurePrivateKey(raw: string): string {
  try {
    const obj = JSON.parse(raw);
    if (obj && typeof obj.private_key === "string") {
      obj.private_key = "********";
    }
    return JSON.stringify(obj, null, 2);
  } catch (_) {
    return raw;
  }
}

const ServiceAccountKeyInput = ({
  value,
  onChange,
  setSecretSet,
  placeholder = "Paste service account key here",
  className,
}: {
  value: string;
  onChange: (value: string) => void;
  setSecretSet: (value: boolean) => void;
  placeholder?: string;
  className?: string;
}) => (
  <TextArea
    value={obscurePrivateKey(value)}
    onChange={(e) => {
      onChange(e.target.value);
      setSecretSet(true);
    }}
    className={`min-h-[150px] font-mono text-sm ${className ?? ""}`}
    placeholder={placeholder}
  />
);

export function ProviderKeyModal({
  row,
  modalType,
  setModalType,
  setOpened,
  setKey,
  existingSecrets,
}: {
  row?: AISecret;
  modalType?: ModalType;
  setModalType: Dispatch<SetStateAction<ModalType | undefined>>;
  setOpened: (opened: boolean) => void;
  setKey: (args: {
    type: string | null | undefined;
    name: string;
    value?: string;
    metadata?: Record<string, unknown>;
  }) => Promise<void>;
  existingSecrets?: AISecret[] | null;
}) {
  const allowedTypes: ModelEndpointType[] = ModelEndpointType.filter(
    (t) => t !== "js",
  );

  const [nameValue, setNameValue] = useState(row?.name || "");
  const [secretValue, setSecretValue] = useState(
    row?.preview_secret ? "*".repeat(64) : "",
  );
  const [vertexAuthType, setVertexAuthType] =
    useState<VertexAuthType>("access_token");
  const [azureAuthType, setAzureAuthType] = useState<AzureAuthType>("api_key");
  const [databricksAuthType, setDatabricksAuthType] =
    useState<DatabricksAuthType>("pat");
  const [secretSet, setSecretSet] = useState(false);

  const [type, setType] = useState<string>(ModelEndpointType[0]);
  const [errorState, dispatchErrorState] = useReducer(errorReducer, {});
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (row?.type) {
      setType(row.type);
    }
  }, [row?.type]);

  useEffect(() => {
    if (row?.name) {
      setNameValue(row.name);
    }
  }, [row?.name]);

  useEffect(() => {
    if (row?.preview_secret) {
      setSecretValue("*".repeat(64));
    }
  }, [row?.preview_secret]);

  useEffect(() => {
    if (row?.type === "vertex" && row?.metadata?.authType) {
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
      setVertexAuthType(row.metadata.authType as VertexAuthType);
    }
    if (row?.type === "azure" && row?.metadata?.auth_type) {
      setAzureAuthType(
        AzureMetadataSchema.shape.auth_type.parse(row.metadata.auth_type),
      );
    }
    if (row?.type === "databricks" && row?.metadata?.auth_type) {
      setDatabricksAuthType(
        DatabricksMetadataSchema.shape.auth_type.parse(row.metadata.auth_type),
      );
    }
  }, [row?.type, row?.metadata?.authType, row?.metadata?.auth_type]);

  useEffect(() => {
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
    if (!Object.values(AISecretTypes).includes(type as ModelEndpointType)) {
      setModalType("CUSTOM");
    }
  }, [type, setModalType]);

  const onSubmit = async (data: FieldValues, bypassValidation?: boolean) => {
    setLoading(true);
    dispatchErrorState({ type: "CLEAR_ERRORS" });

    const currentConfiguredWithName = existingSecrets?.find(
      (secret) => secret.name === nameValue && secret.type === type,
    );
    if (row?.name !== nameValue && currentConfiguredWithName) {
      dispatchErrorState({
        type: "SET_NAME_ERROR",
        message:
          "A provider with this name already exists. Use a unique name or edit the existing provider.",
      });
      setLoading(false);
      return;
    }

    if (!nameValue || !secretValue) {
      !nameValue &&
        dispatchErrorState({
          type: "SET_NAME_ERROR",
          message: "Name cannot be empty",
        });
      !secretValue &&
        dispatchErrorState({
          type: "SET_SECRET_ERROR",
          message: "Secret cannot be empty",
        });
      setLoading(false);
      return;
    }

    const customModelsObj: { [key: string]: ModelSpec } | undefined = data[
      "customModels"
    ]?.length
      ? customModelArrayToObject(
          CustomModelFormArraySchema.parse(data["customModels"]),
        )
      : undefined;

    const additionalHeaders = data["additionalHeaders"]?.length
      ? Object.fromEntries(
          (HeaderFormArraySchema.parse(data["additionalHeaders"]) || []).map(
            ({ name, value }) => [name, value],
          ),
        )
      : undefined;

    if (!bypassValidation && secretSet) {
      try {
        const resp = await testKey({
          provider: type,
          api_key: secretValue,
          model:
            (customModelsObj ? Object.keys(customModelsObj)[0] : undefined) ??
            modelsByProvider[type][0].modelName,
        });
        if (!resp?.ok) {
          const responseBody = await resp?.json();
          const errorMessage =
            responseBody.error?.message ??
            responseBody.error ??
            responseBody.message ??
            responseBody.detail ??
            `${resp?.status}: ${resp?.statusText}`;
          dispatchErrorState({
            type: "VALIDATE_SECRET_ERROR",
            message: (
              <div className="flex flex-col gap-2">
                Error saving API key - {errorMessage ?? resp?.statusText}
                <div>
                  <Button
                    variant="link"
                    className="justify-left text-xs"
                    size="inline"
                    onClick={() => onSubmit(data, true)}
                  >
                    Save anyway
                  </Button>
                </div>
              </div>
            ),
          });
          setLoading(false);
          return;
        }
      } catch (err) {
        dispatchErrorState({
          type: "VALIDATE_SECRET_ERROR",
          message: (
            <div className="flex flex-col gap-2">
              {`Error validating API key: ${err ?? JSON.stringify(err, null, 2)}`}
              <div>
                <Button
                  variant="link"
                  className="justify-left text-xs"
                  size="inline"
                  onClick={() => onSubmit(data, true)}
                >
                  Save anyway
                </Button>
              </div>
            </div>
          ),
        });
        setLoading(false);
        return;
      }
    }
    try {
      await setKey({
        type,
        name: nameValue,
        value: secretSet ? secretValue : undefined,
        metadata:
          modalType === "CUSTOM"
            ? {
                ...data,
                ...(type === "vertex" ? { authType: vertexAuthType } : {}),
                ...(type === "azure" ? { auth_type: azureAuthType } : {}),
                ...(type === "databricks"
                  ? { auth_type: databricksAuthType }
                  : {}),
                customModels: customModelsObj,
                additionalHeaders,
              }
            : undefined,
      });
      setModalType(undefined);
      setType(ModelEndpointType[0]);
      setNameValue("");
      setSecretValue("");
      setSecretSet(false);
    } catch (error) {
      dispatchErrorState({
        type: "SET_OTHER_ERROR",
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
        message: (error as Error).message || `Failed to update ${row?.name}`,
      });
      toast.error(`Failed to create secret`, {
        description: `${error}`,
      });
      console.error("Failed to update key", row, error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog
      open={modalType !== undefined}
      onOpenChange={(open) => {
        setOpened(open);
        setType(ModelEndpointType[0]);
        setNameValue("");
        setSecretValue("");
        setSecretSet(false);
        setLoading(false);
        dispatchErrorState({ type: "CLEAR_ERRORS" });
      }}
    >
      <DialogContent className="w-full max-w-xl gap-4">
        <DialogHeader className="mb-4">
          <DialogTitle>
            Configure{" "}
            {!row
              ? "custom provider"
              : modalType === "DEFAULT"
                ? `${providerReadableName(type)} API key`
                : `${providerReadableName(type)} provider`}
          </DialogTitle>
        </DialogHeader>

        <ProviderFormField label="Provider">
          {modalType === "DEFAULT" ? (
            <div className="flex h-8 items-center gap-2 text-sm font-medium">
              {getProviderIcon(type, 18)}
              {providerReadableName(type)}
            </div>
          ) : (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  type="button"
                  size="sm"
                  className="w-full justify-between"
                  disabled={!!row}
                >
                  <div className="flex flex-row items-center gap-1">
                    {getProviderIcon(type, 16)}
                    {providerReadableName(type)}
                  </div>
                  <ChevronDown
                    size={12}
                    className="flex-none text-primary-500"
                  />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="start">
                {allowedTypes.map((type) => (
                  <DropdownMenuItem
                    key={type}
                    onSelect={() => {
                      setType(type);
                    }}
                  >
                    {getProviderIcon(type, 16)}
                    {providerReadableName(type)}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </ProviderFormField>

        <ProviderFormField label="Name" error={errorState.name}>
          {modalType === "DEFAULT" ? (
            <span className="flex h-8 items-center font-mono text-sm">
              {nameValue}
            </span>
          ) : (
            <Input
              disabled={!!row}
              className="h-8 flex-1"
              placeholder="Enter unique name"
              type="text"
              value={nameValue}
              onChange={(e) => setNameValue(e.target.value)}
            />
          )}
        </ProviderFormField>

        {type === "vertex" ? (
          <VertexAuthField
            vertexAuthType={vertexAuthType}
            setVertexAuthType={setVertexAuthType}
            secretValue={secretValue}
            setSecretValue={setSecretValue}
            secretSet={secretSet}
            setSecretSet={setSecretSet}
            errorState={errorState}
            row={row}
          />
        ) : type === "azure" ? (
          <AzureAuthField
            azureAuthType={azureAuthType}
            setAzureAuthType={setAzureAuthType}
            secretValue={secretValue}
            setSecretValue={setSecretValue}
            secretSet={secretSet}
            setSecretSet={setSecretSet}
            errorState={errorState}
            row={row}
          />
        ) : type === "databricks" ? (
          <DatabricksAuthField
            databricksAuthType={databricksAuthType}
            setDatabricksAuthType={setDatabricksAuthType}
            secretValue={secretValue}
            setSecretValue={setSecretValue}
            secretSet={secretSet}
            setSecretSet={setSecretSet}
            errorState={errorState}
            row={row}
          />
        ) : (
          <ProviderFormField
            label={SecretName[type] || "API key"}
            error={errorState.secret}
            helpText={<EncryptionMessage row={row} />}
          >
            <SecretInput
              value={secretValue}
              onChange={setSecretValue}
              secretSet={secretSet}
              setSecretSet={setSecretSet}
              placeholder={`Enter ${SecretName[type]?.toLocaleLowerCase() || "API key"}`}
              className="flex-1"
            />
          </ProviderFormField>
        )}

        {errorState.other && (
          <div className="whitespace-pre-wrap text-xs font-medium text-bad-600">
            {errorState.other}
          </div>
        )}
        <MetadataForm
          key={Object.keys(formSchemas).includes(type) ? type : "base"}
          currentProvider={type}
          onSubmit={onSubmit}
          formType={modalType}
          row={row}
          secret={secretValue}
          loading={loading}
        />
      </DialogContent>
    </Dialog>
  );
}

function MetadataForm(props: {
  currentProvider: string;
  onSubmit: (data: FieldValues) => Promise<void>;
  formType?: ModalType;
  row?: AISecret;
  secret: string;
  loading: boolean;
}) {
  const { currentProvider, formType, row, onSubmit, secret, loading } = props;
  let useFormProps: UseFormProps = {};

  const {
    customModels: _customModels,
    models: _models,
    ...rowMetadata
  } = row?.metadata || {};

  const defaultCustomModels =
    formType === "CUSTOM"
      ? [
          {
            modelName: "",
            format: ModelFormats[0],
            flavor: PromptInputs[0],
            multimodal: false,
            input_cost_per_mil_tokens: null,
            output_cost_per_mil_tokens: null,
            reasoning: false,
            reasoning_budget: false,
          },
        ]
      : undefined;

  switch (currentProvider) {
    case "openai":
      useFormProps = {
        resolver: zodResolver(formSchemas["openai"]),
        defaultValues: {
          api_base: undefined,
          organization_id: undefined,
          supportsStreaming: true,
          excludeDefaultModels: true,
          ...rowMetadata,
        },
      };
      return (
        <KeyForm
          formFields={formSchemas["openai"].keyof().options}
          schema={OpenAIMetadataSchema}
          {...useFormProps}
          formType={formType}
          row={row}
          onSubmit={onSubmit}
          currentProvider={currentProvider}
          secret={secret}
          loading={loading}
        />
      );
    case "azure":
      useFormProps = {
        resolver: zodResolver(formSchemas["azure"]),
        defaultValues: {
          api_base: undefined,
          api_version: "2023-07-01-preview",
          supportsStreaming: true,
          ...rowMetadata,
        },
      };
      return (
        <KeyForm
          formFields={
            formSchemas["azure"]
              .omit({ no_named_deployment: true, auth_type: true })
              .keyof().options
          }
          schema={AzureMetadataSchema.omit({ auth_type: true })}
          {...useFormProps}
          formType={formType}
          row={row}
          onSubmit={onSubmit}
          currentProvider={currentProvider}
          secret={secret}
          loading={loading}
        />
      );
    case "bedrock":
      useFormProps = {
        resolver: zodResolver(formSchemas["bedrock"]),
        defaultValues: {
          region: undefined,
          access_key: undefined,
          session_token: undefined,
          supportsStreaming: true,
          ...rowMetadata,
        },
      };
      return (
        <KeyForm
          formFields={formSchemas["bedrock"].keyof().options}
          schema={BedrockMetadataSchema}
          {...useFormProps}
          formType={formType}
          row={row}
          onSubmit={onSubmit}
          currentProvider={currentProvider}
          secret={secret}
          loading={loading}
        />
      );
    case "vertex":
      useFormProps = {
        resolver: zodResolver(formSchemas["vertex"]),
        defaultValues: {
          project: undefined,
          location: undefined,
          supportsStreaming: true,
          ...rowMetadata,
        },
      };
      return (
        <KeyForm
          formFields={formSchemas["vertex"].keyof().options}
          schema={VertexMetadataSchema.omit({ authType: true })}
          {...useFormProps}
          formType={formType}
          row={row}
          onSubmit={onSubmit}
          currentProvider={currentProvider}
          secret={secret}
          loading={loading}
        />
      );
    case "databricks":
      useFormProps = {
        resolver: zodResolver(formSchemas["databricks"]),
        defaultValues: {
          api_base: undefined,
          supportsStreaming: true,
          ...rowMetadata,
        },
      };
      return (
        <KeyForm
          formFields={
            formSchemas["databricks"].omit({ auth_type: true }).keyof().options
          }
          schema={DatabricksMetadataSchema.omit({ auth_type: true })}
          {...useFormProps}
          formType={formType}
          row={row}
          onSubmit={onSubmit}
          currentProvider={currentProvider}
          secret={secret}
          loading={loading}
        />
      );
    case "mistral":
      useFormProps = {
        resolver: zodResolver(formSchemas["mistral"]),
        defaultValues: {
          api_base: undefined,
          supportsStreaming: true,
          ...rowMetadata,
        },
      };
      return (
        <KeyForm
          formFields={formSchemas["mistral"].keyof().options}
          schema={MistralMetadataSchema}
          {...useFormProps}
          formType={formType}
          row={row}
          onSubmit={onSubmit}
          currentProvider={currentProvider}
          secret={secret}
          loading={loading}
        />
      );
    default:
      useFormProps = {
        resolver: zodResolver(formSchemas["base"]),
        defaultValues: {
          customModels: defaultCustomModels,
          supportsStreaming: true,
          ...rowMetadata,
        },
      };
      return (
        <KeyForm
          formFields={formSchemas["base"].keyof().options}
          schema={BaseMetadataSchema}
          {...useFormProps}
          formType={formType}
          row={row}
          onSubmit={onSubmit}
          currentProvider={currentProvider}
          secret={secret}
          loading={loading}
        />
      );
  }
}

const KeyForm = <T extends FieldValues>({
  formFields,
  onSubmit,
  formType,
  row,
  schema,
  currentProvider,
  secret,
  loading,
  ...useFormProps
}: UseFormProps & {
  formFields: Path<T>[];
  onSubmit: (data: FieldValues) => Promise<void>;
  formType?: ModalType;
  row?: AISecret;
  schema:
    | typeof OpenAIMetadataSchema
    | typeof AzureMetadataSchema
    | typeof BedrockMetadataSchema
    | typeof BaseMetadataSchema;
  currentProvider: string;
  secret: string;
  loading: boolean;
}) => {
  const form = useForm(useFormProps);

  const customModelsLength = useMemo(() => {
    if (row?.metadata?.customModels) {
      return Object.keys(z.record(ModelSchema).parse(row.metadata.customModels))
        .length;
    }
    return 0;
  }, [row?.metadata?.customModels]);

  const { control, handleSubmit, register, trigger, getValues, watch } = form;
  const apiBase = watch("api_base");
  const customModelsObj = watch("customModels");
  const [currentTab, setCurrentTab] = useState("default");
  const [textInput, setTextInput] = useState<TextEditorValue>(undefined);
  const [textInputError, setTextInputError] = useState("");

  const {
    fields: customModels,
    append: appendModel,
    replace: replaceModels,
    remove: removeModel,
  } = useFieldArray({ control, name: "customModels" });

  const {
    fields: additionalHeaders,
    append: addHeader,
    replace: replaceHeaders,
    remove: removeHeader,
  } = useFieldArray({ control, name: "additionalHeaders" });

  const inputFormFields = useMemo(
    () =>
      formFields.filter(
        (fieldName) =>
          ![
            "customModels",
            "models",
            "excludeDefaultModels",
            "additionalHeaders",
            "supportsStreaming",
          ].includes(fieldName),
      ),
    [formFields],
  );

  const submit = async (data: FieldValues) => {
    trigger();
    await onSubmit(data);
  };

  useEffect(() => {
    if (row && row.metadata) {
      if (row.metadata.customModels) {
        const existingModels = customModelObjectToArray(
          z.record(ModelSchema).parse(row.metadata.customModels),
        );
        replaceModels(existingModels);
      } else {
        replaceModels([]);
      }
    }
  }, [row, replaceModels]);

  useEffect(() => {
    if (row && row.metadata) {
      if (row.metadata.additionalHeaders) {
        const existingHeaders = Object.entries(
          z
            .record(z.string(), z.string())
            .parse(row.metadata.additionalHeaders),
        ).map(([name, value]) => ({ name, value }));
        replaceHeaders(existingHeaders);
      } else {
        replaceHeaders([]);
      }
    }
  }, [row, replaceHeaders]);

  const showTestKeyButton = useMemo(() => {
    return !!checkValidateAndParse(currentProvider);
  }, [currentProvider]);

  const defaultContent = (
    <>
      {inputFormFields.map((fieldName) => (
        <FormField
          key={fieldName}
          name={fieldName}
          control={control}
          render={({ field }) => (
            <ProviderFormField
              label={getDisplayName(field.name)}
              error={form.formState.errors[field.name] && <FormMessage />}
            >
              <Input
                type="text"
                className="h-8"
                placeholder={`Enter ${getPlaceholderName(field.name)}`}
                {...register(field.name)}
              />
            </ProviderFormField>
          )}
        />
      ))}
      <ProviderFormField label="Headers">
        <div className="flex flex-col gap-2">
          {additionalHeaders.length === 0 ? (
            <span className="flex h-8 items-center text-sm text-primary-500">
              No headers configured
            </span>
          ) : null}
          {additionalHeaders.map(({ id }, index) => (
            <HeaderForm key={id} index={index} remove={removeHeader} />
          ))}
        </div>
      </ProviderFormField>
      <ProviderFormField label="Models">
        <div className="flex flex-col gap-2">
          {customModels.length === 0 ? (
            <span className="flex h-8 items-center text-sm text-primary-500">
              No custom models configured
            </span>
          ) : null}
          {customModels.map(({ id }, index) => (
            <CustomModelForm
              key={id}
              index={index}
              remove={removeModel}
              isCreate={index >= customModelsLength}
              showLocations={currentProvider === "vertex"}
            />
          ))}
        </div>
      </ProviderFormField>

      {currentProvider === "azure" && (
        <FormField
          control={control}
          name="no_named_deployment"
          render={({ field }) => (
            <ProviderFormField label="" labelClassName="min-h-0">
              <input
                type="checkbox"
                {...register("no_named_deployment")}
                className="hidden"
              />
              <label className="flex items-center gap-2 text-xs">
                <Switch
                  className="scale-75"
                  checked={!field.value}
                  onCheckedChange={(checked) => field.onChange(!checked)}
                />
                Use the deployment name in the request path
              </label>
            </ProviderFormField>
          )}
        />
      )}
      <FormField
        control={control}
        name="supportsStreaming"
        render={({ field }) => (
          <ProviderFormField label="" labelClassName="min-h-0">
            <input
              type="checkbox"
              {...register("supportsStreaming")}
              className="hidden"
            />
            <label className="flex items-center gap-2 text-xs">
              <Switch
                className="scale-75"
                checked={!!field.value}
                onCheckedChange={field.onChange}
              />
              This endpoint supports streaming
            </label>
          </ProviderFormField>
        )}
      />
      <FormField
        control={control}
        name="excludeDefaultModels"
        render={({ field }) => (
          <ProviderFormField label="">
            <div className="flex items-center gap-1.5">
              <input
                type="checkbox"
                {...register("excludeDefaultModels")}
                className="hidden"
              />
              <label className="mb-0.5 flex items-baseline gap-2 text-xs">
                <Switch
                  className="translate-y-0.5 scale-75"
                  checked={!field.value}
                  onCheckedChange={(checked) => field.onChange(!checked)}
                />

                <span className="block text-pretty leading-[1.1rem]">
                  Include the{" "}
                  <ExternalLink
                    className="underline text-primary-900"
                    href="/docs/guides/proxy#list-of-supported-models-and-providers"
                  >
                    default registry
                  </ExternalLink>{" "}
                  of {providerReadableName(currentProvider)} models
                  <span className="block pt-1 text-xs leading-[1.1rem] text-primary-500">
                    <AlertTriangle className="mr-1 inline-block size-3 text-bad-600" />
                    If multiple providers provide the same model ID, requests
                    for that model ID will be{" "}
                    <ExternalLink
                      className="underline text-primary-600"
                      href="/docs/guides/proxy#load-balancing"
                    >
                      load balanced
                    </ExternalLink>{" "}
                    among all the providers for that model ID.
                  </span>
                </span>
              </label>
            </div>
          </ProviderFormField>
        )}
      />
    </>
  );

  const advancedContent = (
    <>
      <UpdateableDataTextEditor
        value={textInput}
        updateValue={async (v) => {
          // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
          const value = v as Record<string, unknown>;
          if (!value) {
            setTextInputError("");
            return "";
          }
          try {
            const customMetadata = schema.parse(value);
            form.reset();
            const {
              customModels: _ignore,
              additionalHeaders: _,
              ...rest
            } = customMetadata;
            if (value.customModels) {
              const customModelsArray = customModelObjectToArray(
                z.record(ModelSchema).parse(value.customModels),
              );
              replaceModels(customModelsArray);
            }
            if (value.additionalHeaders) {
              const headersArray = Object.entries(
                value.additionalHeaders || {},
              ).map(([name, value]) => ({ name, value }));
              replaceHeaders(headersArray);
            }
            Object.entries(rest).forEach(([key, val]) =>
              form.setValue(key, val),
            );
            setTextInputError("");
            setTextInput(customMetadata);
          } catch (err) {
            setTextInputError(
              "Invalid config:\n\n" +
                // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
                zodErrorToString(err as ZodError, 0, true),
            );
          }
          return "";
        }}
        rowId={""}
        xactId={""}
      />
      {textInputError && (
        <div className="mt-2 whitespace-pre-wrap text-xs font-medium text-bad-600">
          {textInputError}
        </div>
      )}
    </>
  );

  return (
    <Form {...form}>
      <form onSubmit={handleSubmit(submit)}>
        {formType === "CUSTOM" && (
          <Tabs
            value={currentTab}
            className="pt-5"
            onValueChange={(value) => {
              if (value === "advanced") {
                const {
                  customModels,
                  additionalHeaders,
                  auth_type: _auth_type,
                  ...rest
                } = getValues();
                const modelsArray = customModelsArraySchema.parse(customModels);
                const validModels = modelsArray?.filter(
                  (model) => !!model.modelName,
                );
                const modelsObj = validModels?.length
                  ? customModelArrayToObject(
                      CustomModelFormArraySchema.parse(validModels),
                    )
                  : null;
                const headersArray =
                  HeaderFormArraySchema.parse(additionalHeaders);
                const headersObj = headersArray?.length
                  ? Object.fromEntries(
                      headersArray.map(({ name, value }) => [name, value]),
                    )
                  : undefined;
                setTextInput({
                  ...rest,
                  additionalHeaders: headersObj,
                  customModels: modelsObj,
                });
              } else {
                setTextInputError("");
              }
              setCurrentTab(value);
            }}
          >
            <TabsList className="flex w-full">
              <TabsTrigger value="default" className="flex flex-1 gap-2">
                Form
              </TabsTrigger>
              <TabsTrigger value="advanced" className="flex flex-1 gap-2">
                Advanced
              </TabsTrigger>
            </TabsList>
            <TabsContent value="default" className="space-y-3 pt-3">
              {defaultContent}
            </TabsContent>
            <TabsContent value="advanced">{advancedContent}</TabsContent>
          </Tabs>
        )}

        <div
          className={cn(
            "flex gap-2 pt-4",
            showTestKeyButton ? "justify-between" : "justify-end",
          )}
        >
          {showTestKeyButton ? (
            <TestKeyButton
              apiBase={apiBase}
              provider={currentProvider}
              secret={secret}
              model={
                customModelsObj?.[0]?.modelName ||
                modelsByProvider?.[currentProvider]?.[0]?.modelName
              }
            />
          ) : null}
          <div className="flex justify-end gap-2">
            {formType === "CUSTOM" && currentTab === "default" && (
              <>
                <Button
                  size="sm"
                  onClick={() =>
                    addHeader({
                      name: "",
                      value: "",
                    })
                  }
                  type="button"
                >
                  Add a header
                </Button>
                <Button
                  size="sm"
                  onClick={() => appendModel(DefaultCustomModel)}
                  type="button"
                >
                  Add a model
                </Button>
              </>
            )}

            <Button
              variant="primary"
              isLoading={loading}
              disabled={loading}
              size="sm"
              type="submit"
            >
              {row?.preview_secret ? "Update" : "Save"}
            </Button>
          </div>
        </div>
      </form>
    </Form>
  );
};

const SecretName: Record<string, string> = {
  bedrock: "Secret access key",
  azure_entra: "Client secret",
};

const HeaderForm = ({
  index,
  remove,
}: {
  index: number;
  remove: UseFieldArrayRemove;
}) => {
  const { control, register } = useFormContext();

  return (
    <div className="flex gap-2">
      <FormField
        control={control}
        name={`additionalHeaders.${index}.name`}
        render={({ field }) => (
          <Input
            className="h-8"
            type="text"
            placeholder="Enter header"
            {...register(field.name)}
            required
          />
        )}
      />
      <FormField
        control={control}
        name={`additionalHeaders.${index}.value`}
        render={({ field }) => (
          <Input
            className="h-8"
            type="text"
            placeholder="Value"
            {...register(field.name)}
            required
          />
        )}
      />
      <Button
        onClick={(e) => {
          e.stopPropagation();
          remove(index);
        }}
        className="size-8 p-0"
        size="sm"
        title="Remove header"
        type="button"
      >
        <Trash2 className="size-3" />
      </Button>
    </div>
  );
};

const VertexAuthField = ({
  vertexAuthType,
  setVertexAuthType,
  secretValue,
  setSecretValue,
  secretSet,
  setSecretSet,
  errorState,
  row,
}: {
  vertexAuthType: VertexAuthType;
  setVertexAuthType: (value: VertexAuthType) => void;
  secretValue: string;
  setSecretValue: (value: string) => void;
  secretSet: boolean;
  setSecretSet: (value: boolean) => void;
  errorState: ErrorState;
  row?: AISecret;
}) => {
  const [accessTokenValue, setAccessTokenValue] = useState(
    vertexAuthType === "access_token" ? secretValue : "",
  );
  const [serviceAccountValue, setServiceAccountValue] = useState(
    vertexAuthType === "service_account_key" ? secretValue : "",
  );

  return (
    <ProviderFormField
      label="Authentication"
      error={errorState.secret}
      labelClassName="h-10"
    >
      <RadioGroup
        value={vertexAuthType}
        onValueChange={(value: string) => {
          // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
          const newType = value as VertexAuthType;
          setVertexAuthType(newType);
          setSecretValue(
            newType === "access_token" ? accessTokenValue : serviceAccountValue,
          );
        }}
        className="flex flex-col gap-4 pt-2"
      >
        <label className="flex items-baseline gap-3">
          <RadioGroupItem value="access_token" />
          <div>
            <div className="text-sm">Access Token</div>
            <div className="text-xs text-primary-600">
              Use a Vertex AI access token
            </div>
          </div>
        </label>

        <label className="flex items-baseline gap-3">
          <RadioGroupItem value="service_account_key" />
          <div>
            <div className="text-sm">Service Account Key</div>
            <div className="text-xs text-primary-600">
              Use a service account key JSON file
            </div>
          </div>
        </label>
      </RadioGroup>

      <div className="mt-4">
        {vertexAuthType === "access_token" ? (
          <div className="flex flex-col gap-1.5">
            <SecretInput
              value={secretValue}
              onChange={(newValue) => {
                setSecretValue(newValue);
                setAccessTokenValue(newValue);
                setServiceAccountValue("");
                setSecretSet(true);
              }}
              secretSet={secretSet}
              setSecretSet={setSecretSet}
              placeholder="Enter access token"
            />
            <EncryptionMessage row={row} />
          </div>
        ) : (
          <div className="flex flex-col gap-1.5">
            <ServiceAccountKeyInput
              value={secretValue}
              onChange={(newValue) => {
                setSecretValue(newValue);
                setServiceAccountValue(newValue);
                setAccessTokenValue("");
                setSecretSet(true);
              }}
              setSecretSet={setSecretSet}
              placeholder="Paste service account key here"
            />
            <EncryptionMessage row={row} />
          </div>
        )}
      </div>
    </ProviderFormField>
  );
};

const AzureAuthField = ({
  azureAuthType,
  setAzureAuthType,
  secretValue,
  setSecretValue,
  secretSet,
  setSecretSet,
  errorState,
  row,
}: {
  azureAuthType: AzureAuthType;
  setAzureAuthType: (value: AzureAuthType) => void;
  secretValue: string;
  setSecretValue: (value: string) => void;
  secretSet: boolean;
  setSecretSet: (value: boolean) => void;
  errorState: ErrorState;
  row?: AISecret;
}) => {
  const [entraApiValue, _setEntraApiValue] = useState<
    Partial<AzureEntraSecret>
  >({});

  useEffect(() => {
    if (azureAuthType === "entra_api") {
      _setEntraApiValue((prev) => {
        if (Object.keys(prev).length > 0) {
          return prev;
        }
        try {
          const parsed = JSON.parse(secretValue);
          if (AzureEntraSecretSchema.safeParse(parsed).success) {
            return parsed;
          }
        } catch {
          return {
            client_id: secretValue,
            client_secret: secretValue,
            tenant_id: secretValue,
            scope: secretValue,
          };
        }
      });
    }
  }, [azureAuthType, secretValue]);

  const setEntraApiValue = (value: Partial<AzureEntraSecret>) => {
    const fullSecret = { ...entraApiValue, ...value };
    _setEntraApiValue(fullSecret);
    if (AzureEntraSecretSchema.safeParse(fullSecret).success) {
      setSecretValue(JSON.stringify(fullSecret));
      setSecretSet(true);
    }
  };

  const checkResetInput = useCallback(
    (e: React.KeyboardEvent<HTMLInputElement>) => {
      const regex = /^[A-Za-z0-9`~!@#$%^&*()\_\-\+={}[\]\|\:;"'<>,.?\/}]{1}$/;
      if (!secretSet && (e.key === "Backspace" || regex.test(e.key))) {
        _setEntraApiValue({});
        setSecretValue("");
        setSecretSet(true);
      }
    },
    [secretSet, setSecretValue, setSecretSet],
  );

  return (
    <ProviderFormField
      label="Authentication"
      error={errorState.secret}
      labelClassName="h-10"
    >
      <RadioGroup
        value={azureAuthType}
        onValueChange={(value: string) => {
          const newType = AzureMetadataSchema.shape.auth_type.parse(value);
          setAzureAuthType(newType);
          if (!secretSet) {
            setSecretValue(
              newType !== row?.metadata?.auth_type ? "" : "*".repeat(64),
            );
          }
        }}
        className="flex flex-col gap-4 pt-2"
      >
        <label className="flex items-baseline gap-3">
          <RadioGroupItem value="api_key" />
          <div>
            <div className="text-sm">API Key</div>
            <div className="text-xs text-primary-600">
              Use an Azure OpenAI API key
            </div>
          </div>
        </label>

        <label className="flex items-baseline gap-3">
          <RadioGroupItem value="entra_api" />
          <div>
            <div className="text-sm">Entra API</div>
            <div className="text-xs text-primary-600">
              Use Microsoft Entra ID for authentication
            </div>
          </div>
        </label>
      </RadioGroup>

      <div className="mt-4">
        {azureAuthType === "api_key" ? (
          <SecretInput
            value={secretValue}
            onChange={setSecretValue}
            secretSet={secretSet}
            setSecretSet={setSecretSet}
            placeholder="Enter API key"
          />
        ) : (
          <div className="flex flex-col gap-2">
            <ProviderFormField label="Client ID" labelClassName="w-[30%]">
              <Input
                className="h-8"
                value={entraApiValue.client_id ?? ""}
                onChange={(e) =>
                  setEntraApiValue({ client_id: e.target.value })
                }
                placeholder="Enter client ID"
                onKeyDown={checkResetInput}
              />
            </ProviderFormField>
            <ProviderFormField label="Client secret" labelClassName="w-[30%]">
              <Input
                className="h-8"
                value={entraApiValue.client_secret ?? ""}
                onChange={(e) =>
                  setEntraApiValue({ client_secret: e.target.value })
                }
                placeholder="Enter client secret"
                type="password"
                onKeyDown={checkResetInput}
              />
            </ProviderFormField>
            <ProviderFormField label="Tenant ID" labelClassName="w-[30%]">
              <Input
                className="h-8"
                value={entraApiValue.tenant_id ?? ""}
                onChange={(e) =>
                  setEntraApiValue({ tenant_id: e.target.value })
                }
                placeholder="Enter tenant ID"
                onKeyDown={checkResetInput}
              />
            </ProviderFormField>
            <ProviderFormField label="Scope" labelClassName="w-[30%]">
              <Input
                className="h-8"
                value={entraApiValue.scope ?? ""}
                onChange={(e) => setEntraApiValue({ scope: e.target.value })}
                placeholder="Enter scope"
                onKeyDown={checkResetInput}
              />
            </ProviderFormField>
          </div>
        )}
      </div>
      <div className="mt-2">
        <EncryptionMessage row={row} />
      </div>
    </ProviderFormField>
  );
};

const DatabricksAuthField = ({
  databricksAuthType,
  setDatabricksAuthType,
  secretValue,
  setSecretValue,
  secretSet,
  setSecretSet,
  errorState,
  row,
}: {
  databricksAuthType: DatabricksAuthType;
  setDatabricksAuthType: (value: DatabricksAuthType) => void;
  secretValue: string;
  setSecretValue: (value: string) => void;
  secretSet: boolean;
  setSecretSet: (value: boolean) => void;
  errorState: ErrorState;
  row?: AISecret;
}) => {
  const [oauthApiValue, _setOauthApiValue] = useState<
    Partial<DatabricksOAuthSecret>
  >({});

  useEffect(() => {
    if (databricksAuthType === "service_principal_oauth") {
      _setOauthApiValue((prev) => {
        if (Object.keys(prev).length > 0) {
          return prev;
        }
        try {
          const parsed = JSON.parse(secretValue);
          if (DatabricksOAuthSecretSchema.safeParse(parsed).success) {
            return parsed;
          }
        } catch {
          return {
            client_id: secretValue,
            client_secret: secretValue,
          };
        }
      });
    }
  }, [databricksAuthType, secretValue]);

  const setOauthApiValue = (value: Partial<DatabricksOAuthSecret>) => {
    const fullSecret = { ...oauthApiValue, ...value };
    _setOauthApiValue(fullSecret);
    if (DatabricksOAuthSecretSchema.safeParse(fullSecret).success) {
      setSecretValue(JSON.stringify(fullSecret));
      setSecretSet(true);
    }
  };

  const checkResetInput = useCallback(
    (e: React.KeyboardEvent<HTMLInputElement>) => {
      const regex = /^[A-Za-z0-9`~!@#$%^&*()\_\-\+={}[\]\|\:;"'<>,.?\/}]{1}$/;
      if (!secretSet && (e.key === "Backspace" || regex.test(e.key))) {
        _setOauthApiValue({});
        setSecretValue("");
        setSecretSet(true);
      }
    },
    [secretSet, setSecretValue, setSecretSet],
  );

  return (
    <ProviderFormField
      label="Authentication"
      error={errorState.secret}
      labelClassName="h-10"
    >
      <RadioGroup
        value={databricksAuthType}
        onValueChange={(value: string) => {
          const newType = DatabricksMetadataSchema.shape.auth_type.parse(value);
          setDatabricksAuthType(newType);
          if (!secretSet) {
            setSecretValue(
              newType !== row?.metadata?.auth_type ? "" : "*".repeat(64),
            );
          }
        }}
        className="flex flex-col gap-4 pt-2"
      >
        <label className="flex items-baseline gap-3">
          <RadioGroupItem value="pat" />
          <div>
            <div className="text-sm">Personal Access Token</div>
            <div className="text-xs text-primary-600">
              Use a Databricks personal access token
            </div>
          </div>
        </label>

        <label className="flex items-baseline gap-3">
          <RadioGroupItem value="service_principal_oauth" />
          <div>
            <div className="text-sm">Service Principal OAuth</div>
            <div className="text-xs text-primary-600">
              Use OAuth credentials for a service principal
            </div>
          </div>
        </label>
      </RadioGroup>

      <div className="mt-4">
        {databricksAuthType === "pat" ? (
          <div className="flex flex-col gap-1.5">
            <SecretInput
              value={secretValue}
              onChange={setSecretValue}
              secretSet={secretSet}
              setSecretSet={setSecretSet}
              placeholder="Enter Personal Access Token"
            />
            <EncryptionMessage row={row} />
          </div>
        ) : (
          <div className="flex flex-col gap-2">
            <ProviderFormField
              label="Client ID"
              labelClassName="w-full justify-start"
              containerClassName="flex-col gap-0 w-full items-stretch"
            >
              <Input
                className="h-8 w-full"
                value={oauthApiValue.client_id ?? ""}
                onChange={(e) =>
                  setOauthApiValue({ client_id: e.target.value })
                }
                placeholder="Enter client ID"
                onKeyDown={checkResetInput}
              />
            </ProviderFormField>
            <ProviderFormField
              label="Client secret"
              labelClassName="w-full justify-start"
              containerClassName="flex-col gap-0 w-full items-stretch"
            >
              <Input
                className="h-8"
                value={oauthApiValue.client_secret ?? ""}
                onChange={(e) =>
                  setOauthApiValue({ client_secret: e.target.value })
                }
                placeholder="Enter client secret"
                type="password"
                onKeyDown={checkResetInput}
              />
            </ProviderFormField>
            <div className="mt-2">
              <EncryptionMessage row={row} />
            </div>
          </div>
        )}
      </div>
    </ProviderFormField>
  );
};
