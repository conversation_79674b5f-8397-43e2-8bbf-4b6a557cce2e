import { HeaderNavLink } from "#/ui/layout/header";
import { getProjectHref, HeaderMenu } from "#/ui/layout/header-menu";
import { useEffect, useMemo } from "react";
import { useOrg } from "#/utils/user";
import { decodeURIComponentPatched } from "#/utils/url";
import { useParams, usePathname } from "next/navigation";
import {
  Activity,
  Asterisk,
  Beaker,
  Bolt,
  BookOpen,
  ChartSpline,
  Clock,
  Database,
  Folder,
  Home,
  MessageCircle,
  PanelLeft,
  Percent,
  Route,
  Search,
  Settings2,
  Shapes,
  SlidersHorizontal,
} from "lucide-react";
import { Button, buttonVariants } from "#/ui/button";
import { getOrgLink, getOrgSettingsLink } from "./getOrgLink";
import { useAtom, useSetAtom } from "jotai";
import { cn } from "#/utils/classnames";
import { getProjectConfigurationLink } from "./p/[project]/getProjectLink";
import { useEntityStorage } from "#/lib/clientDataStorage";
import { useHotkeys } from "react-hotkeys-hook";
import {
  commandBarOpenAtom,
  sidenavStateAtom,
  useToggleSidenavState,
} from "./sidenav-state";
import Link from "next/link";
import { getPlaygroundsLink } from "./prompt/[prompt]/getPromptLink";
import { ProjectNavItem } from "./p/[project]/project-list-layout";
import { getExperimentsLink } from "./p/[project]/experiments/[experiment]/getExperimentLink";
import { getDatasetsLink } from "./p/[project]/datasets/[dataset]/getDatasetLink";
import { getProjectLink } from "./p/[project]/getProjectLink";
import { useFeatureFlags } from "#/lib/feature-flags";
import {
  isBTQLSandboxPage,
  isLogsPage,
  isMonitorPage,
  isOrgPage,
  isPlaygroundPage,
  isSettingsPage,
  useActivePage,
} from "./pathname-checker";
import { isDatasetPage } from "./pathname-checker";
import { isExperimentPage } from "./pathname-checker";
import { type getProjectSummary } from "./org-actions";
import { useQueryFunc } from "#/utils/react-query";
import { getProjectLogsLink } from "./p/[project]/logs/getProjectLogsLink";
import { BasicTooltip } from "#/ui/tooltip";
import { renderHotkey } from "#/utils/hotkeys";

const isPageWithWiderMinimumWidth = (pathname: string) => {
  return Boolean(
    isExperimentPage(pathname) ||
      isPlaygroundPage(pathname) ||
      isDatasetPage(pathname) ||
      isLogsPage(pathname),
  );
};

export const Sidenav = () => {
  const org = useOrg();
  const pathname = usePathname();
  const params = useParams<{ project: string; org?: string }>();
  const projectName = decodeURIComponentPatched(params?.project ?? "");
  const orgName = decodeURIComponentPatched(params?.org ?? "");

  const active = useActivePage();

  const [sidenavState, setSidenavState] = useAtom(sidenavStateAtom);

  const widerMinimumWidth = isPageWithWiderMinimumWidth(pathname ?? "");

  const [didUserCollapseSidenav, setDidUserCollapseSidenav] = useEntityStorage({
    entityType: "app",
    entityIdentifier: "app",
    key: "isSidenavCollapsed",
  });

  const [recentProjectIds, setRecentProjectIds] = useEntityStorage({
    entityType: "org",
    entityIdentifier: orgName,
    key: "recentProjectIds",
  });

  const { data: projects } = useQueryFunc<typeof getProjectSummary>({
    fName: "getProjectSummary",
    args: {
      org_name: orgName,
    },
  });

  const selectedOrLastUsedProject = useMemo(() => {
    if (!projects) return;
    const selectedProject = projects.find(
      (p) => p.project_name === projectName,
    );
    if (selectedProject) {
      return selectedProject;
    }

    // Try to find the most recent project that still exists
    if (recentProjectIds?.length) {
      for (const projectId of recentProjectIds) {
        const recentProject = projects.find((p) => p.project_id === projectId);
        if (recentProject) {
          return recentProject;
        }
      }
    }

    return projects[0];
  }, [projectName, projects, recentProjectIds]);

  useEffect(() => {
    if (!selectedOrLastUsedProject) {
      return;
    }

    // Update recent project IDs
    setRecentProjectIds((prev) => {
      const newList = [
        selectedOrLastUsedProject.project_id,
        ...(prev ?? []).filter(
          (id) => id !== selectedOrLastUsedProject.project_id,
        ),
      ].slice(0, 5); // Keep only the 5 most recent projects
      return newList;
    });
  }, [selectedOrLastUsedProject, setRecentProjectIds]);

  const closeIfFloating = () => {
    if (sidenavState !== "floating-open") return;
    setSidenavState("floating-closed");
  };

  const toggleSidenavState = useToggleSidenavState();

  useHotkeys("[", toggleSidenavState, { description: "Toggle sidenav" });

  useHotkeys(
    "Escape",
    () => {
      setSidenavState("floating-closed");
    },
    {
      enabled: sidenavState === "floating-open",
    },
  );

  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < (widerMinimumWidth ? 1440 : 1200)) {
        setSidenavState((s) =>
          s === "floating-open" ? "floating-open" : "floating-closed",
        );
      } else if (didUserCollapseSidenav) {
        setSidenavState("collapsed");
      } else {
        setSidenavState("docked");
      }
    };

    handleResize();

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, [setSidenavState, widerMinimumWidth, didUserCollapseSidenav]);

  const projectLink = getProjectLink({
    orgName,
    projectName: selectedOrLastUsedProject?.project_name ?? "",
  });

  const { flags, isLoading: isLoadingFlags } = useFeatureFlags();

  const setCommandBarOpen = useSetAtom(commandBarOpenAtom);

  const recentProjects = useMemo(() => {
    return recentProjectIds
      ?.filter(
        (projectId) => !!projects?.find((p) => p.project_id === projectId),
      )
      .slice(0, 3);
  }, [recentProjectIds, projects]);

  if (!org.id) {
    return null;
  }

  return (
    <>
      <div
        onClick={() => {
          setSidenavState("floating-closed");
        }}
        className={cn(
          "fixed inset-0 z-40 pointer-events-none bg-transparent transition-colors",
          {
            "bg-black/20 pointer-events-auto": sidenavState === "floating-open",
          },
        )}
      />
      <div
        className={cn(
          "sticky top-0 flex h-screen w-full max-w-56 flex-none flex-col bg-primary-50 transition-all opacity-100",
          {
            "-translate-x-full max-w-0 opacity-0 pointer-events-none scale-90":
              sidenavState === "collapsed" ||
              sidenavState === "floating-closed",
            "fixed z-50":
              sidenavState === "floating-open" ||
              sidenavState === "floating-closed",
            "shadow-lg border-r-0 dark:border-r border-primary-100":
              sidenavState === "floating-open",
          },
        )}
      >
        <div className="flex h-11 flex-none items-center px-2">
          <div className="flex-1 truncate">
            {org.name && org.id && (
              <HeaderMenu type="orgs" orgName={org.name} orgId={org.id} />
            )}
          </div>
          <BasicTooltip
            tooltipContent={
              <>
                Command
                <span className="ml-2.5 inline-block opacity-50">
                  {renderHotkey("Mod+k")}
                </span>
              </>
            }
          >
            <Button
              size="xs"
              variant="ghost"
              className="flex-none px-1 text-primary-400"
              onClick={() => {
                setCommandBarOpen(true);
                closeIfFloating();
              }}
            >
              <Search className="size-4" />
            </Button>
          </BasicTooltip>
          {sidenavState !== "floating-closed" &&
            sidenavState !== "floating-open" && (
              <BasicTooltip
                tooltipContent={
                  <>
                    Toggle navigation
                    <span className="ml-2.5 inline-block opacity-50">
                      {renderHotkey("[")}
                    </span>
                  </>
                }
              >
                <Button
                  size="xs"
                  variant="ghost"
                  className="flex-none px-1 text-primary-400"
                  onClick={() => {
                    setSidenavState("collapsed");
                    setDidUserCollapseSidenav(true);
                  }}
                >
                  <PanelLeft className="size-4" />
                </Button>
              </BasicTooltip>
            )}
        </div>
        <div className="flex flex-none flex-col gap-0.5 px-2 py-3">
          <HeaderNavLink
            href={getOrgLink({ orgName: org.name })}
            isActive={!!isOrgPage(pathname ?? "")}
            Icon={Folder}
            onClick={closeIfFloating}
          >
            Projects
          </HeaderNavLink>
          <HeaderNavLink
            href={`${getOrgLink({ orgName: org.name })}/monitor`}
            isActive={!!isMonitorPage(pathname ?? "")}
            Icon={ChartSpline}
            onClick={closeIfFloating}
          >
            Monitor
          </HeaderNavLink>
          <HeaderNavLink
            href={`${getOrgLink({ orgName: org.name })}/btql`}
            isActive={!!isBTQLSandboxPage(pathname ?? "")}
            Icon={Asterisk}
            onClick={closeIfFloating}
          >
            BTQL sandbox
          </HeaderNavLink>
          <HeaderNavLink
            href={getOrgSettingsLink({ orgName: org.name })}
            isActive={!!isSettingsPage(pathname ?? "")}
            Icon={SlidersHorizontal}
            onClick={closeIfFloating}
          >
            Settings
          </HeaderNavLink>
        </div>
        <div
          className={cn("flex flex-1 relative overflow-hidden", {
            invisible: !selectedOrLastUsedProject,
          })}
        >
          <div className="absolute inset-x-0 top-0 z-10 h-3 bg-gradient-to-b from-primary-50 to-transparent" />
          <div className="absolute inset-x-0 bottom-0 z-10 h-3 bg-gradient-to-t from-primary-50 to-transparent" />
          <div className="no-scrollbar flex flex-1 flex-col gap-0.5 overflow-auto p-2">
            <div className="px-2 text-xs text-primary-500">Project</div>
            <div className="mb-1 flex-none truncate">
              {org.name && org.id && (
                <HeaderMenu
                  type="projects"
                  orgName={org.name}
                  orgId={org.id}
                  selectedProject={selectedOrLastUsedProject}
                  fullWidth
                />
              )}
            </div>
            <ProjectNavItem
              href={getProjectLink({
                orgName,
                projectName: selectedOrLastUsedProject?.project_name ?? "",
              })}
              className="gap-2.5 text-[13px] hover:bg-primary-200"
              activeClassName="bg-primary-200"
              active={active === "project"}
              onClick={closeIfFloating}
            >
              <Home className="size-4 text-primary-500" />
              Overview
            </ProjectNavItem>
            <ProjectNavItem
              href={getProjectLogsLink({
                orgName,
                projectName: selectedOrLastUsedProject?.project_name ?? "",
              })}
              className="gap-2.5 text-[13px] hover:bg-primary-200"
              activeClassName="bg-primary-200"
              active={active === "logs"}
              onClick={closeIfFloating}
            >
              <Activity className="size-4 text-primary-500" />
              Logs
            </ProjectNavItem>
            <ProjectNavItem
              href={getPlaygroundsLink({
                orgName,
                projectName: selectedOrLastUsedProject?.project_name ?? "",
              })}
              className="gap-2.5 text-[13px] hover:bg-accent-100 dark:hover:bg-accent-100/50"
              activeClassName="bg-accent-100 dark:bg-accent-100/50"
              active={active === "playgrounds"}
              onClick={closeIfFloating}
            >
              <Shapes className="size-4 text-accent-500" />
              Playgrounds
            </ProjectNavItem>
            <ProjectNavItem
              href={getExperimentsLink({
                orgName,
                projectName: selectedOrLastUsedProject?.project_name ?? "",
              })}
              className="gap-2.5 text-[13px] hover:bg-comparison-100 dark:hover:bg-comparison-100/50"
              activeClassName="bg-comparison-100 dark:bg-comparison-100/50"
              active={active === "experiments"}
              onClick={closeIfFloating}
            >
              <Beaker className="size-4 text-comparison-500" />
              Experiments
            </ProjectNavItem>
            <ProjectNavItem
              href={getDatasetsLink({
                orgName,
                projectName: selectedOrLastUsedProject?.project_name ?? "",
              })}
              className="gap-2.5 text-[13px] hover:bg-fuchsia-100 dark:hover:bg-fuchsia-900/50"
              activeClassName="bg-fuchsia-100 dark:bg-fuchsia-900/50"
              active={active === "datasets"}
              onClick={closeIfFloating}
            >
              <Database className="size-4 text-fuchsia-500" />
              Datasets
            </ProjectNavItem>

            <ProjectNavItem
              href={`${projectLink}/prompts`}
              className="gap-2.5 text-[13px] hover:bg-cyan-100 dark:hover:bg-cyan-900/50"
              activeClassName="bg-cyan-100 dark:bg-cyan-900/50"
              active={active === "prompts"}
              onClick={closeIfFloating}
            >
              <MessageCircle className="size-4 text-cyan-500" />
              Prompts
            </ProjectNavItem>

            <ProjectNavItem
              href={`${projectLink}/tools`}
              className="gap-2.5 text-[13px] hover:bg-amber-100 dark:hover:bg-amber-900/50"
              activeClassName="bg-amber-100 dark:bg-amber-900/50"
              active={active === "tools"}
              onClick={closeIfFloating}
            >
              <Bolt className="size-4 text-amber-500" />
              Tools
            </ProjectNavItem>

            <ProjectNavItem
              href={`${projectLink}/scorers`}
              className="gap-2.5 text-[13px] hover:bg-lime-200/70 dark:hover:bg-lime-900/50"
              activeClassName="bg-lime-200/70 dark:bg-lime-900/50"
              active={active === "scorers"}
              onClick={closeIfFloating}
            >
              <Percent className="size-4 text-lime-600" />
              Scorers
            </ProjectNavItem>

            {!isLoadingFlags && flags.agents && (
              <ProjectNavItem
                href={`${projectLink}/agents`}
                className="gap-2.5 text-[13px] hover:bg-red-100 dark:hover:bg-red-900/50"
                activeClassName="bg-red-100 dark:bg-red-900/50"
                active={active === "agents"}
                onClick={closeIfFloating}
              >
                <Route className="size-4 text-red-500" />
                Agents
              </ProjectNavItem>
            )}
            <ProjectNavItem
              href={getProjectConfigurationLink({
                orgName,
                projectName: selectedOrLastUsedProject?.project_name ?? "",
              })}
              className="gap-2.5 text-[13px] hover:bg-primary-200"
              activeClassName="bg-primary-200"
              active={active === "configuration"}
              onClick={closeIfFloating}
            >
              <Settings2 className="size-4 text-primary-500" />
              Configuration
            </ProjectNavItem>

            {recentProjects?.length > 0 && (
              <>
                <div className="px-2 pb-1 pt-5 text-xs text-primary-500">
                  Recent projects
                </div>
                {recentProjects.map((projectId) => {
                  const project = projects?.find(
                    (p) => p.project_id === projectId,
                  );
                  if (!project) return null;
                  return (
                    <HeaderNavLink
                      key={projectId}
                      href={getProjectHref({
                        orgName,
                        projectName: project.project_name,
                        pathname,
                      })}
                      isActive={false}
                      Icon={Clock}
                      onClick={closeIfFloating}
                    >
                      <span className="flex-1 truncate">
                        {project.project_name}
                      </span>
                    </HeaderNavLink>
                  );
                })}
              </>
            )}
          </div>
        </div>
        <div className="flex flex-none items-center justify-between gap-0.5 p-2">
          <Link
            href="/docs"
            target="_blank"
            className={cn(
              buttonVariants({ variant: "ghost", size: "xs" }),
              "gap-2.5 text-primary-600 hover:text-primary-950 text-[13px]",
            )}
          >
            <BookOpen className="size-4 transition-colors text-primary-500 group-hover:text-primary-900" />
            Docs
          </Link>
        </div>
      </div>
    </>
  );
};
