import { DocsPage, DocsBody } from "fumadocs-ui/page";
import { notFound } from "next/navigation";
import { proseMDXBaseClassName } from "#/ui/prose";
import { cn } from "#/utils/classnames";
import defaultMdxComponents from "fumadocs-ui/mdx";
import { getPage, getPages, openapi } from "../source";
import Footer from "#/ui/landing/footer";
import { Breadcrumb } from "./Breadcrumb";
import { buildMetadata } from "#/app/metadata";
import { type TableOfContents } from "fumadocs-core/server";
import { z } from "zod";
import { LLMCopyButton } from "./LLMCopyButton";
import { StructuredData } from "#/ui/structured-data";
import { generateBreadcrumbSchema } from "#/lib/structured-data";

const fumadocsTableOfContentsSchema = z.array(
  z.object({
    depth: z.number(),
    title: z.string(),
    url: z.string(),
  }),
);
// Enforces that the zod type matches `TableOfContents` from fumadocs or we get
// a TypeScript compiler error. https://stackoverflow.com/a/72945564
type IsExtends<TNarrow, TGeneral> = [TNarrow] extends [TGeneral] ? true : false;
type StaticAssert<T extends true> = T;
export type _TestTocSchemaCompatibility = StaticAssert<
  IsExtends<z.infer<typeof fumadocsTableOfContentsSchema>, TableOfContents>
>;

const fumadocsFrontmatterSchema = z.object({
  _openapi: z.object({
    toc: fumadocsTableOfContentsSchema,
  }),
});

export default async function Page(props0: {
  params: Promise<{ slug?: string[] }>;
}) {
  const params = await props0.params;
  const page = getPage(params.slug);
  if (!page) {
    notFound();
  }

  // fumadocs-openapi 5.0 and later renders the API documentation on the server
  // side. `page.data.toc` will be an empty list for those MDX files. This hack
  // allows us to access the precomputed TOC in the MDX frontmatter.
  const openApiToc = fumadocsFrontmatterSchema.safeParse(page.data);

  const toc = openApiToc.success ? openApiToc.data._openapi.toc : page.data.toc;
  const tocOptions = {
    enabled: page.file.name !== "index" && toc.length > 0,
  };

  // Generate breadcrumbs for structured data
  const breadcrumbs = [
    { name: "Home", url: "https://www.braintrust.dev" },
    { name: "Docs", url: "https://www.braintrust.dev/docs" },
    ...(params.slug
      ? params.slug.map((segment, index) => ({
          name:
            segment.charAt(0).toUpperCase() +
            segment.slice(1).replace(/-/g, " "),
          url: `https://www.braintrust.dev/docs/${params.slug ? params.slug.slice(0, index + 1).join("/") : ''}`,
        }))
      : []),
  ];

  return (
    <>
      <StructuredData data={generateBreadcrumbSchema(breadcrumbs)} />
      <div className={cn("docs-content flex flex-1 min-w-0", page.file.path)}>
        <DocsPage
          toc={toc}
          full={page.data.full}
          tableOfContent={tocOptions}
          tableOfContentPopover={tocOptions}
          breadcrumb={{
            enabled: true,
            component: <Breadcrumb full={page.data.full} />,
          }}
          footer={{
            enabled: true,
            component: (
              <Footer
                inDocs
                className="border-t pb-8 pt-6 text-sm border-primary-100 text-primary-600"
              />
            ),
          }}
        >
          <DocsBody
            className={cn(
              proseMDXBaseClassName,
              "leading-[1.8] prose-headings:font-medium prose-headings:leading-tight prose-headings:font-planar font-inter prose-lg",
            )}
          >
            <div className="absolute right-0 top-4">
              <LLMCopyButton path={page.file.path} />
            </div>
            <page.data.body
              components={{
                ...defaultMdxComponents,
                APIPage: openapi.APIPage,
              }}
            />
          </DocsBody>
        </DocsPage>
      </div>
    </>
  );
}

export async function generateStaticParams() {
  return getPages().map((page) => ({
    slug: page.slugs,
  }));
}

export async function generateMetadata(props: {
  params: Promise<{ slug?: string[] }>;
}) {
  const params = await props.params;
  const page = getPage(params.slug);

  if (!page) notFound();

  // metaTitle can be used to set a different meta title than what shows
  // up in docs sidebar/breadcrumbs
  const title = page.data.metaTitle ?? page.data.title;

  // Generate relevant tags based on the page path and title
  const generateDocsTagsFromPath = (slug?: string[]) => {
    const baseTags = [
      "documentation",
      "AI documentation",
      "LLM documentation",
      "developer docs",
    ];
    if (!slug || slug.length === 0) return baseTags;

    const pathTags = [];
    if (slug.includes("start")) pathTags.push("getting started", "tutorial");
    if (slug.includes("guides")) pathTags.push("guides", "how-to");
    if (slug.includes("reference")) pathTags.push("API reference", "reference");
    if (slug.includes("evaluation") || slug.includes("eval"))
      pathTags.push("AI evaluation", "LLM evaluation");
    if (slug.includes("monitor")) pathTags.push("monitoring", "observability");
    if (slug.includes("logs")) pathTags.push("logging", "debugging");
    if (slug.includes("functions"))
      pathTags.push("functions", "prompts", "tools");
    if (slug.includes("datasets")) pathTags.push("datasets", "data management");
    if (slug.includes("experiments"))
      pathTags.push("experiments", "A/B testing");

    return [...baseTags, ...pathTags];
  };

  return buildMetadata({
    title,
    sections: ["Docs"],
    description:
      page.data.description ??
      "Comprehensive documentation for Braintrust - the end-to-end platform for building, evaluating, and monitoring AI applications. Learn how to implement AI evaluation, observability, and debugging.",
    relativeUrl: `/docs/${params.slug?.join("/") ?? ""}`,
    tags: generateDocsTagsFromPath(params.slug),
  });
}
