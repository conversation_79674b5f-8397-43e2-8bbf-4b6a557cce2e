import { createProject } from "#/app/app/[org]/p/[project]/createProject";
import { newObjectName } from "#/utils/metadata";
import { isEmpty } from "#/utils/object";
import { useEffect, useState } from "react";
import { OneLineTextPrompt } from "./one-line-text-prompt";
import { toast } from "sonner";
import { useAppAnalytics } from "#/ui/analytics/segment-analytics";
import type { ProjectCreateEntryPoint } from "#/analytics/events";

interface CreateProjectDialogProps {
  orgId: string | undefined;
  defaultName: string;
  onClose?: () => void;
  onSuccessfulCreate?: (params: {
    projectName: string;
    projectId: string;
  }) => void;
  open: boolean;
  onAbandon?: (params: {
    projectName: string;
    reason: "error" | "closed";
  }) => void;
}

const CreateProjectDialog = ({
  orgId,
  defaultName,
  open,
  onClose = () => {},
  onSuccessfulCreate,
  onAbandon,
}: CreateProjectDialogProps) => {
  return (
    <OneLineTextPrompt
      title="Create project"
      fieldName="Name"
      onSubmit={async (name: string) => {
        if (!orgId) {
          throw new Error(`Missing required inputs: orgId=${orgId}`);
        }
        try {
          const { project, error } = await createProject({
            orgId,
            projectName: name,
          });
          if (isEmpty(project)) {
            toast.error(`Failed to create project ${name}`, {
              description: `${error}`,
            });
            onAbandon?.({
              projectName: name,
              reason: "error",
            });
          } else {
            onSuccessfulCreate?.({
              projectName: name,
              projectId: project.id,
            });
          }
        } finally {
          onClose();
        }
      }}
      onOpenChange={(isOpen: boolean, reason?: "submit" | "close") => {
        if (!isOpen && reason !== "submit") {
          onAbandon?.({
            projectName: defaultName || newObjectName("project"),
            reason: "closed",
          });
          onClose();
        }
      }}
      open={!!open}
      defaultValue={defaultName || newObjectName("project")}
      submitLabel="Create"
    />
  );
};

export const useCreateProjectDialog = ({
  orgId,
  onSuccessfulCreate,
}: {
  orgId: string | undefined;
  onSuccessfulCreate: (params: {
    projectName: string;
    projectId: string;
  }) => void;
}) => {
  const { track } = useAppAnalytics();
  const [newProjectDialogData, setNewProjectDialogData] = useState<null | {
    name: string;
    entryPoint?: ProjectCreateEntryPoint;
  }>(null);

  useEffect(() => {
    if (
      typeof window !== "undefined" &&
      new URLSearchParams(window.location.search).get("new")
    ) {
      setNewProjectDialogData(
        (prev) => prev ?? { name: "", entryPoint: undefined },
      );
    }
  }, []);

  const modal = (
    <CreateProjectDialog
      orgId={orgId}
      defaultName={newProjectDialogData?.name || newObjectName("Project")}
      open={!!newProjectDialogData}
      onClose={() => setNewProjectDialogData(null)}
      onSuccessfulCreate={(args) => {
        onSuccessfulCreate(args);
        track("projectCreate", {
          projectId: args.projectId,
          projectName: args.projectName,
          entryPoint: newProjectDialogData?.entryPoint,
        });
      }}
      onAbandon={({
        projectName,
        reason,
      }: {
        projectName: string;
        reason: "error" | "closed";
      }) => {
        track("projectCreateAbandon", {
          entryPoint: newProjectDialogData?.entryPoint,
          projectName,
          reason,
        });
      }}
    />
  );
  return {
    modal,
    open: ({
      name,
      entryPoint,
    }: {
      name: string;
      entryPoint?: ProjectCreateEntryPoint;
    }) => {
      setNewProjectDialogData({ name, entryPoint });
      track("projectCreateAttempt", { entryPoint, projectName: name });
    },
  };
};
