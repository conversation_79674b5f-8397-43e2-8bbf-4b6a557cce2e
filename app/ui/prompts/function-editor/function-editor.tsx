import {
  type Dispatch,
  type RefObject,
  type SetStateAction,
  useCallback,
  useMemo,
  useRef,
  useState,
} from "react";
import { Field, Schema, Utf8 } from "apache-arrow";
import { Button } from "#/ui/button";
import { Info } from "lucide-react";
import {
  type SyncedPlaygroundBlock,
  type UIFunction,
  promptSchema,
} from "#/ui/prompts/schema";
import { useAvailableModels } from "#/ui/prompts/models";
import { isEmpty } from "#/utils/object";
import { produce } from "immer";
import { type ZodError } from "zod";
import {
  type JSONStructure,
  makeFunctionEditorFunctionId,
  usePromptExtensions,
} from "#/ui/prompts/hooks";
import {
  type FunctionObjectType,
  type InvokeFunctionRequest,
} from "@braintrust/core/typespecs";
import { type TextEditorHandle } from "#/ui/text-editor";
import { Skeleton } from "#/ui/skeleton";
import { NoAISecrets } from "#/ui/prompts/empty";

import { toast } from "sonner";
import { zodErrorToString } from "#/utils/validation";
import { Tabs, TabsContent, TabsTrigger, TabsList } from "#/ui/tabs";
import {
  FunctionDescriptionField,
  FunctionInputSkeleton,
  FunctionMetadataField,
  FunctionNameField,
  FunctionSlugField,
} from "./function-meta-fields";
import { Tooltip, TooltipContent, TooltipTrigger } from "#/ui/tooltip";
import { useFeatureFlags } from "#/lib/feature-flags";
import dynamic from "next/dynamic";
import { type SetValue, useEntityStorage } from "#/lib/clientDataStorage";
import { useAPIVersion } from "#/ui/api-version/check-api-version";
import { PLACEHOLDER_PY, PLACEHOLDER_TS } from "./code-editor-placeholders";
import { type CopilotContextBuilder } from "#/ui/copilot/context";
import { LibraryItemLinks } from "#/app/app/[org]/p/[project]/library/library-item-links";
import { PythonLogo, TypescriptLogo } from "#/app/app/[org]/onboarding-logos";
import { InfoBanner } from "#/ui/info-banner";
import { type Message } from "@braintrust/core/typespecs";
import { type CompletionBlockHandle } from "#/app/app/[org]/prompt/[prompt]/completion-block";
import { PromptEditorSynced } from "#/app/app/[org]/prompt/[prompt]/prompt-editor-synced";
import { FunctionRunSection } from "./function-run-section";
import {
  type FunctionTab,
  type ActivityProps,
  type Mode,
  type ObjectType,
  type FunctionEditorContext,
} from "./types";
import { FunctionActivitySection } from "./function-activity-section";
import {
  SyncedPromptsProvider,
  useSyncedPrompts,
} from "#/app/app/[org]/p/[project]/prompts/synced/use-synced-prompts";
import { atom, useAtomValue } from "jotai";
import { FunctionEditorHeader } from "./function-editor-header";
import { FunctionEditorFooter } from "./function-editor-footer";
import { CollapsibleSection } from "#/ui/collapsible-section";
import { DataTextEditor } from "#/ui/data-text-editor";
import { useFunctionEditorPrompt } from "./use-function-editor-prompt";
import { useUpsellContext } from "#/app/playground/upsell-dialog";

const DynamicCodeEditor = dynamic(
  () => import("./code-editor").then((mod) => mod.CodeEditor),
  {
    ssr: false,
  },
);

export const emptyPromptMetaSchema = new Schema([
  Field.new({ name: "id", type: new Utf8() }),
  Field.new({ name: "user", type: new Utf8() }),
  Field.new({ name: "search_text", type: new Utf8() }),
]);

export type FunctionEditorProps = {
  identifier: string;
  orgName: string;
  projectId: string;
  projectName: string;
  type: FunctionObjectType;
  objectType: ObjectType;
  mode: Mode;
  activityProps?: ActivityProps;
  initialFunction: UIFunction | null;
  variableData?: unknown;
  title?: string;
  context: FunctionEditorContext;
  jsonStructure?: JSONStructure | null;
  outputNames?: string[];
  copilotContext?: CopilotContextBuilder;
  extraMessages?: Message[];
  status: string;
  isDirtyRef: RefObject<boolean>;
};

type MetaFields = {
  name?: string | null;
  slug?: string | null;
  description?: string | null;
  metadata?: Record<string, unknown> | null;
};

export function FunctionEditor({
  status,
  type,
  initialFunction,
  title,
  mode,
  objectType,
  ...rest
}: FunctionEditorProps) {
  // Return a separate loading state rather than having each component in FunctionEditorInner handle its own loading state in place.
  // This means that when FunctionEditor inner is mounted, the initial prompt is loaded, so state can be initialized without
  // any effect syncing.
  if (status === "loading") {
    return (
      <div className="flex flex-auto flex-col overflow-auto p-4">
        <FunctionEditorHeader
          type={type}
          sourcePrompt={initialFunction}
          mode={mode}
          title={title}
          objectType={objectType}
        />
        <div className="flex flex-col gap-4">
          {mode.type !== "view_unsaved" && (
            <div className="flex w-full gap-4">
              <FunctionInputSkeleton label="Name" />
              <FunctionInputSkeleton label="Slug" />
            </div>
          )}
          <div className="flex h-[400px] flex-col">
            <Skeleton className="h-10" />
            <Skeleton className="mt-2 h-20" />
            <Skeleton className="mt-2 h-20" />
          </div>
        </div>
      </div>
    );
  }

  return (
    <FunctionEditorInner
      type={type}
      objectType={objectType}
      initialFunction={initialFunction}
      mode={mode}
      title={title}
      {...rest}
    />
  );
}

function FunctionEditorInner({
  identifier,
  orgName,
  projectId,
  projectName,
  objectType,
  mode,
  activityProps,
  initialFunction,
  variableData,
  title,
  type,
  context,
  jsonStructure,
  outputNames,
  copilotContext,
  extraMessages,
  isDirtyRef,
}: Omit<FunctionEditorProps, "status">) {
  const [error, setError] = useState<string | null>(null);
  const [metaFields, setMetaFields] = useState<MetaFields>({
    name: initialFunction?.name,
    slug: initialFunction?.slug,
    description: initialFunction?.description,
    metadata: initialFunction?.metadata,
  });

  const { allAvailableModels } = useAvailableModels({ orgName });

  const jsEditorRef = useRef<TextEditorHandle<string>>(null);
  const pyEditorRef = useRef<TextEditorHandle<string>>(null);

  const defaultTab: FunctionTab =
    initialFunction?.function_data.type === "code"
      ? initialFunction?.function_data.data.runtime_context.runtime === "node"
        ? "ts"
        : "py"
      : "llm";
  const [activeTab, setActiveTab] = useState<FunctionTab>();
  const tab = activeTab ?? defaultTab;

  const { promptState, coercedFunction } = useFunctionEditorPrompt({
    initialFunction,
    type,
    projectId,
    modeType: mode.type,
  });

  const initialCode =
    initialFunction?.function_data.type === "code" &&
    initialFunction?.function_data.data.type === "inline"
      ? initialFunction.function_data.data.code
      : undefined;
  const isBundled =
    initialFunction?.function_data?.type === "code" &&
    initialFunction?.function_data?.data?.type === "bundle";

  /** Combines the selected tab, prompt editor atom state, and imperative retrieval of the code editors to construct the output prompt. */
  const getOutputPrompt = useCallback(
    (promptState: SyncedPlaygroundBlock): UIFunction => {
      const [runtime_context, code] =
        tab === "ts"
          ? [
              { runtime: "node", version: "20" } as const,
              jsEditorRef.current?.getValue(),
            ]
          : tab === "py"
            ? [
                { runtime: "python", version: "3.12" } as const,
                pyEditorRef.current?.getValue(),
              ]
            : [undefined, undefined];

      const overwrittenFunctionData =
        runtime_context != null
          ? {
              type: "code" as const,
              data: {
                type: "inline" as const,
                runtime_context,
                // Fallback to initialCode to ensure code is correct before the editor refs are set up
                code: code ?? initialCode ?? "",
              },
            }
          : { type: "prompt" as const };

      const prompt: UIFunction = {
        ...promptState,
        ...metaFields,
        name: metaFields.name ?? coercedFunction.name,
        slug: metaFields.slug ?? coercedFunction.slug,
        tags: coercedFunction.tags,
        project_id: coercedFunction.project_id,
        function_type: coercedFunction.function_type,
        ...(promptState.function_data.type !== "global" && !isBundled
          ? { function_data: overwrittenFunctionData }
          : {}),
      };

      if (prompt.function_data.type === "code") {
        delete prompt.prompt_data;
      }

      return prompt;
    },
    [
      tab,
      initialCode,
      metaFields,
      coercedFunction.name,
      coercedFunction.slug,
      coercedFunction.tags,
      coercedFunction.project_id,
      coercedFunction.function_type,
      isBundled,
    ],
  );

  // TODO: use state and an onLint callback to track lint errors
  /* eslint-disable react-compiler/react-compiler */
  const hasLintErrors =
    (activeTab === "ts" &&
      jsEditorRef.current &&
      jsEditorRef.current.getLintErrors().length > 0) ||
    (activeTab === "py" &&
      pyEditorRef.current &&
      pyEditorRef.current.getLintErrors().length > 0) ||
    false;
  /* eslint-enable react-compiler/react-compiler */

  /** Whether the user has interacted with the slug field. We don't auto-set the slug when the name changes after a user has touched the slug field. */
  const isSlugTouchedRef = useRef(false);
  /** This is a bit of a hack - since we code editor changes don't trigger a re-render, we bump this counter to trigger a re-render for declarative dirtiness detection */
  const [codeTouched, setCodeTouched] = useState(0);

  const [dataEditorValue, setDataEditorValue] = useEntityStorage({
    entityType: "functions",
    entityIdentifier: identifier,
    key: "runEditorValue",
    defaultValue:
      type === "scorer"
        ? DEFAULT_SCORER_DATA_EDITOR_VALUE
        : DEFAULT_PROMPT_DATA_EDITOR_VALUE,
  });

  return (
    <SyncedPromptsProvider
      allAvailableModels={allAvailableModels}
      externalValue={promptState}
    >
      <div className="flex flex-auto flex-col overflow-auto p-4">
        <FunctionEditorHeader
          type={type}
          sourcePrompt={initialFunction}
          mode={mode}
          title={title}
          objectType={objectType}
        />
        <div className="flex flex-col gap-4">
          <FunctionEditorBody
            mode={mode}
            initialFunction={initialFunction}
            getOutputPrompt={getOutputPrompt}
            pyEditorRef={pyEditorRef}
            jsEditorRef={jsEditorRef}
            activityProps={activityProps}
            setError={setError}
            tab={tab}
            setActiveTab={setActiveTab}
            metaFields={metaFields}
            setMetaFields={setMetaFields}
            isSlugTouchedRef={isSlugTouchedRef}
            jsonStructure={jsonStructure}
            outputNames={outputNames}
            copilotContext={copilotContext}
            variableData={variableData}
            type={type}
            context={context}
            orgName={orgName}
            projectId={projectId}
            projectName={projectName}
            objectType={objectType}
            extraMessages={extraMessages}
            dataEditorValue={dataEditorValue}
            setDataEditorValue={setDataEditorValue}
            setCodeTouched={setCodeTouched}
          />
        </div>
      </div>
      <FunctionEditorFooter
        mode={mode}
        type={type}
        getOutputPrompt={getOutputPrompt}
        isSlugTouchedRef={isSlugTouchedRef}
        error={error}
        setError={setError}
        // eslint-disable-next-line react-compiler/react-compiler
        hasLintErrors={hasLintErrors}
        initialDirtyFunctionComparisonBase={coercedFunction}
        isDirtyRef={isDirtyRef}
        orgName={orgName}
        projectId={projectId}
        projectName={projectName}
        dataEditorValue={dataEditorValue}
        extraMessages={extraMessages}
        promptName={metaFields.name ?? undefined}
        context={context}
        codeTouched={codeTouched}
      />
    </SyncedPromptsProvider>
  );
}

const DEFAULT_OUTPUT_NAMES: string[] = [];

function FunctionEditorBody({
  orgName,
  projectId,
  projectName,
  objectType,
  variableData,
  type,
  context,
  jsonStructure = null,
  outputNames = DEFAULT_OUTPUT_NAMES,
  copilotContext,
  mode,
  isSlugTouchedRef,
  metaFields,
  setMetaFields,
  setError,
  tab,
  setActiveTab,
  initialFunction,
  getOutputPrompt,
  pyEditorRef,
  jsEditorRef,
  activityProps,
  extraMessages = [],
  dataEditorValue,
  setDataEditorValue,
  setCodeTouched,
}: Omit<
  FunctionEditorProps,
  "title" | "status" | "isDirtyRef" | "identifier"
> & {
  metaFields: MetaFields;
  setMetaFields: Dispatch<SetStateAction<MetaFields>>;
  isSlugTouchedRef: RefObject<boolean>;
  setError: Dispatch<SetStateAction<string | null>>;
  tab: FunctionTab;
  setActiveTab: Dispatch<SetStateAction<FunctionTab | undefined>>;
  getOutputPrompt: (promptState: SyncedPlaygroundBlock) => UIFunction;
  pyEditorRef: RefObject<TextEditorHandle<string> | null>;
  jsEditorRef: RefObject<TextEditorHandle<string> | null>;
  dataEditorValue: Record<string, unknown>;
  setDataEditorValue: SetValue<Record<string, unknown>>;
  setCodeTouched: Dispatch<SetStateAction<number>>;
}) {
  const isUpdate = mode.type === "update";
  const isReadOnly = mode.type === "view_saved" || mode.type === "view_unsaved";
  const hasSavedVersions = mode.type === "update" || mode.type === "view_saved";

  const {
    flags: { customFunctions },
  } = useFeatureFlags();

  const { extensions } = usePromptExtensions({
    jsonStructure,
    outputNames,
    expandInputVariables: false,
  });
  const {
    allAvailableModels,
    configuredModelsByProvider,
    noConfiguredSecrets,
  } = useAvailableModels({ orgName });
  const { onUpsell } = useUpsellContext();
  const showNoConfiguredSecretsMessage = noConfiguredSecrets && !onUpsell;

  const { sortedSyncedPromptsAtom_ROOT, addMessage } = useSyncedPrompts();
  const prompt = useAtomValue(
    useMemo(
      () => atom((get) => get(sortedSyncedPromptsAtom_ROOT)[0]),
      [sortedSyncedPromptsAtom_ROOT],
    ),
  );

  const cellRef = useRef<CompletionBlockHandle>(null);
  const runPrompt = async () => {
    if (onUpsell) {
      onUpsell();
      return;
    }

    if (!cellRef.current) {
      return;
    }

    const outputPrompt = getOutputPrompt(prompt);
    const functionId = makeFunctionEditorFunctionId(outputPrompt, isUpdate);
    if (!functionId) {
      toast.warning(`No ${type} to run`);
      return;
    }

    const parent: InvokeFunctionRequest["parent"] | undefined =
      context === "functions"
        ? {
            object_type: "project_logs",
            object_id: projectId,
          }
        : undefined;

    cellRef.current.submit(
      {
        type: "function",
        functionId,
        input: dataEditorValue ?? {},
        parent,
        messages: extraMessages.length > 0 ? extraMessages : undefined,
      },
      orgName,
    );
  };

  const showTabs =
    mode.type === "create" &&
    prompt.function_data.type !== "global" &&
    type === "scorer";

  const bundledPreview =
    prompt.function_data?.type === "code" &&
    prompt.function_data?.data?.type === "bundle" &&
    prompt.function_data?.data?.preview;
  const tooComplex =
    (prompt.function_data?.type === "global" &&
      isEmpty(prompt.prompt_data?.prompt)) ||
    (prompt.function_data?.type === "code" &&
      prompt.function_data?.data?.type === "bundle");
  const showTooComplex = tooComplex && !bundledPreview;

  const codeBetaMessage = (
    <div className="px-1 pt-2 text-xs text-primary-500">
      Code runs in a sandboxed environment with restrictions on imports and
      filesystem access
    </div>
  );
  const { code_execution } = useAPIVersion();
  const noCodeExecution = <>Upgrade your stack and enable code execution</>;
  const codeDisabled = showTooComplex || !customFunctions || !code_execution;

  return (
    <>
      {mode.type !== "view_unsaved" && (
        <div className="flex w-full gap-4">
          <FunctionNameField
            className="flex-1"
            name={metaFields.name ?? ""}
            isUpdate={isUpdate}
            isReadOnly={isReadOnly}
            onChange={({ name, slug }) => {
              setMetaFields(
                produce((prev) => {
                  prev.name = name;
                  if (slug && !isSlugTouchedRef.current) {
                    prev.slug = slug;
                  }
                }),
              );
            }}
          />
          <FunctionSlugField
            className="flex-1"
            slug={metaFields.slug ?? ""}
            isReadOnly={isReadOnly}
            onChange={(newSlug) => {
              setMetaFields(
                produce((prev) => {
                  prev.slug = newSlug;
                }),
              );
              // eslint-disable-next-line react-compiler/react-compiler
              isSlugTouchedRef.current = true;
            }}
          />
        </div>
      )}

      {!showTooComplex && type !== "agent" && (
        <Tabs
          className="flex flex-col gap-4"
          value={tab}
          onValueChange={(t) => {
            // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
            setActiveTab(t as FunctionTab);
            setError(null);
          }}
        >
          {showTabs && (
            <div>
              <div className="mb-2 text-xs font-medium">Type</div>
              <TabsList className="mb-2 inline-flex h-auto gap-1 rounded-md border p-1 bg-background">
                <TabsTrigger value="llm" asChild>
                  <Button
                    size="xs"
                    disabled={showTooComplex}
                    variant={tab === "llm" ? "primary" : "ghost"}
                    className="rounded text-xs data-[state=active]:shadow-none data-[state=active]:bg-primary-200"
                  >
                    LLM-as-a-judge
                  </Button>
                </TabsTrigger>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div>
                      <TabsTrigger value="ts" asChild>
                        <Button
                          size="xs"
                          disabled={codeDisabled}
                          variant={tab === "ts" ? "primary" : "ghost"}
                          className="gap-2 rounded text-xs data-[state=active]:shadow-none data-[state=active]:bg-primary-200"
                        >
                          <TypescriptLogo className="size-3" />
                          TypeScript
                        </Button>
                      </TabsTrigger>
                    </div>
                  </TooltipTrigger>
                  {!code_execution && (
                    <TooltipContent>{noCodeExecution}</TooltipContent>
                  )}
                </Tooltip>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div>
                      <TabsTrigger value="py" asChild>
                        <Button
                          size="xs"
                          disabled={codeDisabled}
                          variant={tab === "py" ? "primary" : "ghost"}
                          className="gap-2 rounded text-xs data-[state=active]:shadow-none data-[state=active]:bg-primary-200"
                        >
                          <PythonLogo className="size-3" />
                          Python
                        </Button>
                      </TabsTrigger>
                    </div>
                  </TooltipTrigger>
                  {!code_execution && (
                    <TooltipContent>{noCodeExecution}</TooltipContent>
                  )}
                </Tooltip>
              </TabsList>
            </div>
          )}
          <TabsContent
            value="llm"
            className="mt-0"
            forceMount
            hidden={tab !== "llm"}
          >
            <div className="mb-2 text-xs font-medium">Prompt</div>
            <PromptEditorSynced
              isReadOnly={isReadOnly}
              orgName={orgName}
              modelOptionsByProvider={configuredModelsByProvider}
              allAvailableModels={allAvailableModels}
              extensions={extensions}
              promptData={prompt.prompt_data}
              onRun={() => {
                if (showNoConfiguredSecretsMessage) {
                  toast("No configured secrets", {
                    description: <NoAISecrets orgName={orgName} />,
                  });
                  return;
                }
                runPrompt();
              }}
              copilotContext={copilotContext}
              promptId={prompt.id}
              type={type}
              rowData={dataEditorValue}
              enableHotkeys
            />
          </TabsContent>
          <TabsContent
            value="ts"
            className="mt-0"
            forceMount
            hidden={tab !== "ts"}
          >
            <div className="mb-2 flex items-center gap-1 text-xs font-medium">
              {!showTabs && <TypescriptLogo className="size-3" />}
              TypeScript
            </div>
            {customFunctions ? (
              <div className="rounded-md border p-2 bg-primary-100">
                <DynamicCodeEditor
                  savedCode={
                    bundledPreview
                      ? bundledPreview
                      : prompt.function_data.type === "code" &&
                          prompt.function_data.data.type === "inline" &&
                          prompt.function_data.data.runtime_context.runtime ===
                            "node"
                        ? prompt.function_data.data.code
                        : PLACEHOLDER_TS
                  }
                  readOnly={!!bundledPreview || isReadOnly}
                  editorRef={jsEditorRef}
                  language="ts"
                  copilotContext={copilotContext}
                  onSave={() => {
                    setCodeTouched((prev) => prev + 1);
                  }}
                />
                {codeBetaMessage}
              </div>
            ) : (
              noCodeExecution
            )}
          </TabsContent>
          <TabsContent
            value="py"
            className="mt-0"
            forceMount
            hidden={tab !== "py"}
          >
            <div className="mb-2 flex items-center gap-1 text-xs font-medium">
              {!showTabs && <PythonLogo className="size-3" />}
              Python
            </div>
            {customFunctions ? (
              <div className="rounded-md border p-2 bg-primary-100">
                <DynamicCodeEditor
                  savedCode={
                    bundledPreview
                      ? bundledPreview
                      : prompt.function_data.type === "code" &&
                          prompt.function_data.data.type === "inline" &&
                          prompt.function_data.data.runtime_context.runtime ===
                            "python"
                        ? prompt.function_data.data.code
                        : PLACEHOLDER_PY
                  }
                  readOnly={!!bundledPreview || isReadOnly}
                  editorRef={pyEditorRef}
                  language="py"
                  copilotContext={copilotContext}
                  onSave={() => {
                    setCodeTouched((prev) => prev + 1);
                  }}
                />
                {codeBetaMessage}
              </div>
            ) : (
              noCodeExecution
            )}
          </TabsContent>
        </Tabs>
      )}
      {showTooComplex && (
        <div className="flex items-center rounded-md border p-3 text-sm bg-accent-50 border-accent-100 text-primary-700">
          <Info className="mr-1.5 inline-block size-3 flex-none" />
          This function is too complex to edit in the UI. You can run the
          function here, or edit it via the API.
        </div>
      )}
      {type === "agent" && (
        <InfoBanner>
          Agents are currently only visible in playgrounds
        </InfoBanner>
      )}
      <div>
        <FunctionRunSection
          showNoConfiguredSecretsMessage={showNoConfiguredSecretsMessage}
          copilotContext={copilotContext}
          dataEditorValue={dataEditorValue}
          setDataEditorValue={setDataEditorValue}
          promptData={prompt.prompt_data}
          initialFunction={initialFunction}
          runPrompt={runPrompt}
          variableData={variableData}
          ref={cellRef}
          onAddMessageToPrompt={(message) => {
            addMessage({
              id: prompt.id,
              content: message.content,
            });
          }}
        />
      </div>
      {extraMessages.length > 0 && (
        <div>
          <CollapsibleSection title="Additional messages" defaultCollapsed>
            <DataTextEditor
              value={extraMessages}
              allowedRenderOptions={["llm", "yaml", "json"]}
              readOnly
            />
          </CollapsibleSection>
        </div>
      )}
      {mode.type !== "view_unsaved" && (
        <>
          <FunctionDescriptionField
            description={metaFields.description ?? ""}
            isReadOnly={isReadOnly}
            onChange={(description) => {
              setMetaFields(
                produce((prev) => {
                  prev.description = description;
                }),
              );
            }}
          />
          <FunctionMetadataField
            metadata={metaFields.metadata}
            isReadOnly={isReadOnly}
            onChange={async (metadata) => {
              // TODO: debounce parsing?
              setMetaFields(
                produce((prev) => {
                  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
                  prev.metadata = metadata as Record<string, unknown> | null;
                }),
              );
              try {
                promptSchema.shape.metadata.parse(metadata);
                setError(null);
              } catch (error) {
                setError(
                  "Invalid metadata: " +
                    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
                    zodErrorToString(error as ZodError, 0, false),
                );
              }
              return prompt._xact_id;
            }}
            functionId={prompt.id}
          />
        </>
      )}
      {context !== "playground" && hasSavedVersions && (
        <div>
          <LibraryItemLinks
            projectName={projectName}
            objectType={type}
            objectId={prompt.id}
            objectName={metaFields.name ?? undefined}
            objectSlug={metaFields.slug ?? undefined}
          />
        </div>
      )}
      {activityProps && (
        <div>
          <FunctionActivitySection
            objectType={objectType}
            initialFunction={initialFunction}
            type={type}
            {...activityProps}
          />
        </div>
      )}
    </>
  );
}

const DEFAULT_SCORER_DATA_EDITOR_VALUE = {
  output: "",
  expected: "",
};

const DEFAULT_PROMPT_DATA_EDITOR_VALUE = {
  input: "",
};
