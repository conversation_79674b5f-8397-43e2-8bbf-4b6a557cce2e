import { TransactionIdField } from "@braintrust/local/query";
import {
  type SyncedPlaygroundBlock,
  type UIFunction,
} from "#/ui/prompts/schema";
import { useDefaultPromptData } from "#/ui/prompts/use-default-prompt-data";
import { newId } from "braintrust";
import {
  type PromptData,
  type FunctionObjectType,
} from "@braintrust/core/typespecs";
import { useMemo } from "react";
import { functionObjectTypeToFunctionType } from "#/ui/prompts/hooks";
import { slugify } from "#/utils/slug";

export function useFunctionEditorPrompt({
  initialFunction,
  type,
  projectId,
  modeType,
}: {
  initialFunction: UIFunction | null;
  type: FunctionObjectType;
  projectId: string;
  modeType: "create" | "update" | "view_unsaved" | "view_saved";
}) {
  const defaultPromptData = useDefaultPromptData();

  const isLLMScorer =
    type === "scorer" &&
    (!initialFunction?.function_data ||
      initialFunction?.function_data.type === "prompt");

  const defaultId = useMemo(() => newId(), []);
  const coercedFunction: UIFunction & { prompt_data: PromptData } =
    useMemo(() => {
      let prompt = {
        ...(initialFunction ?? {}),
        id: initialFunction?.id ?? defaultId,
        [TransactionIdField]: initialFunction?.[TransactionIdField] ?? "0",
        prompt_data:
          initialFunction?.prompt_data ??
          // Add default prompt data if it's a prompt or a new scorer
          (type === "prompt" || isLLMScorer
            ? {
                ...defaultPromptData,
                ...(isLLMScorer
                  ? {
                      parser: {
                        type: "llm_classifier",
                        use_cot: true,
                        choice_scores: {},
                      },
                    }
                  : {}),
              }
            : {}),
        function_data: initialFunction?.function_data ?? { type: "prompt" },
        function_type: functionObjectTypeToFunctionType(type),
        project_id: projectId,
      };

      if (modeType === "view_unsaved") {
        // Some unsaved prompts come from unsaved playground prompts, which have an id and transaction id attached. This ensures
        // we create a new id and transaction id for the unsaved prompt.
        prompt = {
          ...prompt,
          id: defaultId,
          [TransactionIdField]: "0",
          name: "Untitled",
          slug: slugify("Untitled"),
        };
      }

      return prompt;
    }, [
      defaultId,
      defaultPromptData,
      initialFunction,
      isLLMScorer,
      modeType,
      projectId,
      type,
    ]);

  const promptState: SyncedPlaygroundBlock[] = useMemo(
    () => [
      {
        id: coercedFunction.id,
        [TransactionIdField]: coercedFunction[TransactionIdField],
        prompt_data: coercedFunction.prompt_data,
        function_data: coercedFunction.function_data,
      },
    ],
    [coercedFunction],
  );

  return { promptState, coercedFunction };
}
