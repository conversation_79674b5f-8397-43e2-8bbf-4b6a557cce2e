import { type FormatterProps, type UpdateValueFn } from "#/ui/arrow-table";
import {
  type RenderOptionProps,
  UpdateableDataTextEditor,
} from "#/ui/data-text-editor";
import { isValidTimestamp, smartDateFormat } from "#/ui/date";
import { cn } from "#/utils/classnames";
import { isEmpty, isObject, normalizeArrowForJSON } from "#/utils/object";
import { type DuckDBJSONType, isDuckDBJSONType } from "#/utils/schema";
import { TRANSACTION_ID_FIELD } from "@braintrust/core";
import { DataType } from "apache-arrow";
import { NullFormatter } from "./null-formatter";
import {
  GroupAggregationFormatterFactory,
  type GroupAggregationProps,
} from "./group-aggregation-formatter";
import { MarkdownViewer } from "#/ui/markdown";
import { jsonToYaml, truncateJson } from "#/utils/parse";
import { BT_IS_GROUP } from "#/ui/table/grouping/queries";
import { BN } from "apache-arrow/util/bn";
import { useMemo } from "react";
import untruncateJson from "untruncate-json";

// https://stackoverflow.com/questions/2901102/how-to-format-a-number-with-commas-as-thousands-separators
export function numberWithCommas(x: number | bigint) {
  return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

const isPlainString = (value: string) => {
  try {
    JSON.parse(value);
    return false;
  } catch {
    return !value.startsWith("{") || !value.endsWith("...");
  }
};

export function DefaultValueDisplay({
  className,
  metaType,
  typeHint,
  inTable,
  renderForTooltip,
  moreText,
  row,
  value,
  updateValue,
  hideNulls,
  selectedRenderOption,
  setSelectedRenderOption,
  isGridLayout,
  isGroupRow,
}: {
  className?: string;
  metaType: DataType;
  typeHint?: DuckDBJSONType;
  inTable?: boolean;
  renderForTooltip?: (value: string) => React.ReactNode;
  moreText?: boolean;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  row: any;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  value: any;
  updateValue?: UpdateValueFn;
  hideNulls?: boolean;
  isGridLayout?: boolean;
  isGroupRow?: boolean;
} & RenderOptionProps) {
  const isJSONType = isDuckDBJSONType(metaType, typeHint);
  let isMonospace = false;
  let isNumber = false;
  let skipTruncate = false;

  if (DataType.isList(metaType) || DataType.isStruct(metaType)) {
    value = JSON.stringify(value, normalizeArrowForJSON, 2);
  } else if (isJSONType) {
    /*
    // Cannot enable this assert because when we switch diff mode off,
    // the value is briefly a struct before the query refreshes.
    console.assert(
      isEmpty(value) || typeof value === "string",
      value,
      metaType,
    );
    */

    // check if the value is a string
    if (typeof value === "string" && value.startsWith('"')) {
      // check if the last character is a quote
      const originalValue = value;
      value = value.endsWith('"') ? value.slice(1, -1) : value.slice(1);
      if (value.startsWith("{") && value.endsWith("...")) {
        try {
          value = untruncateJson(JSON.parse(`"${value}"`));
        } catch (e) {
          // If the value is not a valid JSON string, return the original value as-is
          value = originalValue;
        }
        isMonospace = true;
        skipTruncate = true;
      }
    } else if (!(typeof value === "string" && isPlainString(value))) {
      isMonospace = true;
      if (typeof value === "object") {
        value = JSON.stringify(value);
      }
    }
  } else if (
    DataType.isTimestamp(metaType) ||
    (typeof value === "string" && isValidTimestamp(value))
  ) {
    value =
      value &&
      smartDateFormat(
        new Date(typeof value === "bigint" ? Number(value) : value).getTime(),
      );
  } else if (DataType.isFloat(metaType) && typeof value === "number") {
    value = value && value.toFixed(2);
    isNumber = true;
  } else if (
    (DataType.isInt(metaType) && typeof value === "number") ||
    typeof value === "bigint"
  ) {
    value = isEmpty(value) ? value : numberWithCommas(value);
    isNumber = true;
  } else if (DataType.isDecimal(metaType)) {
    // bigint and hugeint are serialized as strings
    if (typeof value === "string") {
      value = Number(JSON.parse(value));
    }
    try {
      if (value instanceof Uint32Array && metaType.scale) {
        // Decimal values are stored as integers and scale is the number of digits after the decimal point
        const bnValue = new BN(value, true);
        value = bnValue.valueOf(metaType.scale);
      }
    } catch {}
    value = value?.toString();
    isNumber = true;
  } else if (DataType.isBool(metaType)) {
    value = `${value}`;
  }

  if (renderForTooltip) {
    return renderForTooltip(
      value == null || value === "null" ? <NullFormatter /> : value,
    );
  } else if (inTable) {
    if (isEmpty(value) || value === "null") {
      if (hideNulls) {
        return null;
      }
      return (
        <span className="font-inter text-sm">
          <NullFormatter />
        </span>
      );
    }

    if (isJSONType && isMonospace && isGridLayout) {
      let jsonValue = null;
      try {
        if (isJSONType) {
          jsonValue = skipTruncate
            ? JSON.parse(value)
            : truncateJson(JSON.parse(value), 1000);
        }
      } catch {
        jsonValue = value;
      }
      if (jsonValue) {
        return <MemoizedJsonToYaml jsonValue={jsonValue} />;
      }
    }

    if (isGridLayout && !isNumber && !isMonospace) {
      return (
        <MarkdownViewer
          disableHighlighting
          value={value.replace(/\\n/g, "\n")}
          className={cn(
            "whitespace-normal py-0 prose-p:whitespace-pre-line prose-pre:whitespace-pre-wrap",
            className,
          )}
        />
      );
    }

    const formattedValue = `${value}`
      .substring(0, moreText ? 1500 : 128)
      .replace(/\\n|\\t|\\"|\\\\/g, (match) => {
        switch (match) {
          case "\\n":
            return " ";
          case "\\t":
            return " ";
          case '\\"':
            return '"';
          case "\\\\":
            return "\\";
          default:
            return match;
        }
      });

    return (
      <span
        className={cn(
          { "font-mono": isMonospace, "text-xs": !isGroupRow && isMonospace },
          className,
        )}
      >
        {formattedValue}
      </span>
    );
  } else {
    let jsonValue = null;
    try {
      if (isJSONType) {
        jsonValue = JSON.parse(value);
      }
    } catch {
      jsonValue = value;
    }
    return (
      <UpdateableDataTextEditor
        value={jsonValue}
        updateValue={updateValue}
        rowId={row.id}
        xactId={row[TRANSACTION_ID_FIELD] ?? null}
        selectedRenderOption={selectedRenderOption}
        setSelectedRenderOption={setSelectedRenderOption}
      />
    );
  }
}

export function MemoizedJsonToYaml({ jsonValue }: { jsonValue: unknown }) {
  const yamlValue = useMemo(() => {
    return jsonToYaml(jsonValue);
  }, [jsonValue]);
  return (
    <div className="whitespace-pre-wrap font-mono text-xs">{yamlValue}</div>
  );
}

export function DefaultFormatter<
  TsTable extends { [BT_IS_GROUP]?: boolean },
  TsValue,
>(props: FormatterProps<TsTable, TsValue>) {
  const {
    className,
    cell,
    inTable,
    renderForTooltip,
    value,
    mergedValues,
    setValue,
    updateRow,
    meta,
    hideNulls,
  } = props;

  if (isObject(mergedValues) && Object.keys(mergedValues).length > 0) {
    const mergedValue = Object.values(mergedValues)[0];
    return mergedValue.formatter({
      ...props,
      value: mergedValue.value,
    });
  }

  const metaType = meta.type;
  return (
    <DefaultValueDisplay
      className={cn(className, {
        "font-mono text-xs":
          cell.column.id === "id" ||
          cell.column.id === "prompt_data" ||
          cell.column.id === "function_data",
      })}
      metaType={metaType}
      typeHint={meta.typeHint}
      inTable={inTable}
      renderForTooltip={renderForTooltip}
      value={value}
      row={cell.row.original}
      hideNulls={hideNulls}
      moreText={meta.moreText}
      isGridLayout={meta.isGridLayout}
      isGroupRow={cell.row.original[BT_IS_GROUP]}
      updateValue={
        updateRow &&
        // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
        (async (value: any) => {
          setValue?.(value);
          return await updateRow(
            cell.row.original,
            // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
            (cell.column.columnDef.meta! as any).path,
            value,
          );
        })
      }
    />
  );
}

export const DefaultWithAggregationFormatter = ({
  isGrouping,
  ...groupAggregationProps
}: GroupAggregationProps) => ({
  cell: GroupAggregationFormatterFactory({
    render: DefaultFormatter,
    renderForGroup: DefaultFormatter,
    ...groupAggregationProps,
  }),
  colSize:
    groupAggregationProps.summaryEnabled || isGrouping
      ? {
          minSize: 200,
          size: 200,
        }
      : undefined,
});
