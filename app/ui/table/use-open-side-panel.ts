import { type RowId, makeRowIdPrimary } from "#/utils/diffs/diff-objects";
import { useCallback } from "react";
import {
  useActiveRowAndSpan,
  useHumanReviewState,
} from "#/ui/query-parameters";
import { matchesRowId } from "./use-active-row-effects";
import { isObject } from "@braintrust/core";

type Args = {
  rowIds: RowId[];
  rowData: unknown[];
};

export default function useOpenSidePanel({ rowIds, rowData }: Args) {
  const [_activeRowAndSpan, setActiveRowAndSpan] = useActiveRowAndSpan();
  const [_humanReviewState, setHumanReviewState] = useHumanReviewState();

  return useCallback(
    (
      rowId: RowId,
      opts?: { humanReviewMode?: boolean; openInNewTab?: boolean },
    ) => {
      const primaryRowId = makeRowIdPrimary(rowId);
      const matchingRowId = rowIds.find((r) => {
        return matchesRowId(r, primaryRowId);
      });
      if (!matchingRowId) return;

      const matchingRow = rowData.find((r) => {
        return (
          isObject(r) &&
          "id" in r &&
          "span_id" in r &&
          // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
          makeRowIdPrimary(r.id as RowId) === primaryRowId
        );
      });
      const spanId =
        makeRowIdPrimary(
          isObject(matchingRow) && "span_id" in matchingRow
            ? // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
              (matchingRow.span_id as RowId)
            : null,
        ) ?? undefined;
      if (opts?.openInNewTab) {
        const pathname = window.location.pathname;
        const params = new URLSearchParams(window.location.search);
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
        params.set("r", matchingRowId as string);
        if (spanId) {
          params.set("s", spanId);
        }
        if (opts?.humanReviewMode) {
          params.set("review", "1");
        }
        window.open(`${pathname}?${params.toString()}`, "_blank");
        return;
      }

      setActiveRowAndSpan({ r: matchingRowId, s: spanId });

      if (opts?.humanReviewMode) {
        setHumanReviewState("1");
      }
    },
    [rowIds, setActiveRowAndSpan, setHumanReviewState, rowData],
  );
}
