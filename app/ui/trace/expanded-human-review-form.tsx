"use client";
import { memo, useCallback, useEffect, useMemo, useRef, useState } from "react";
import { cn } from "#/utils/classnames";
import { isEmpty } from "#/utils/object";
import {
  ScoreToggleGroup,
  type ScoreToggleGroupHandle,
  UncontrolledCategoricalToggleGroup,
} from "./manual-review";
import { useHotkeys } from "react-hotkeys-hook";
import { type ConfiguredScore } from "./graph";
import { MarkdownViewer } from "#/ui/markdown";
import {
  type NumericSliderHandle,
  UncontrolledNumericSlider,
} from "#/ui/numeric-slider";
import scrollIntoViewIfNeeded from "scroll-into-view-if-needed";
import { average } from "./diff-score-object";
import { type TransactionId } from "@braintrust/core";
import { type ManualScoreUpdate } from "./trace";
import {
  freeFormDataPath,
  FreeFormTextArea,
  type FreeFormTextAreaHandle,
} from "./free-form-text-area";
import { Spinner } from "#/ui/icons/spinner";
import { type ExpandedRowState } from "./query";
export const ExpandedHumanReviewForm = memo(
  ({
    scores,
    isAutoAdvanceEnabled,
    expandedRowState,
    isLoading,
    updateScores,
    rowKey,
    xactId,
    onAutoAdvanceToNextRow,
  }: {
    scores: ConfiguredScore[];
    isAutoAdvanceEnabled: boolean;
    expandedRowState: ExpandedRowState;
    isLoading: boolean;
    onAutoAdvanceToNextRow: VoidFunction;
    updateScores?: (
      scoreUpdates: ManualScoreUpdate[],
    ) => Promise<(TransactionId | null)[]>;
    rowKey?: string;
    xactId: TransactionId | null;
  }) => {
    const [focusIndex, setFocusIndex] = useState(0);
    const [isSavingScore, setSavingScore] = useState(false);

    const refs = useRef<{
      [key: string]:
        | NumericSliderHandle
        | ScoreToggleGroupHandle
        | FreeFormTextAreaHandle;
    }>({});

    useEffect(() => {
      const el = document.getElementById(
        `expanded-human-review-field-${focusIndex}`,
      );
      if (!el) return;

      el.querySelector("label")?.click();

      scrollIntoViewIfNeeded(el, {
        scrollMode: "if-needed",
        behavior: "smooth",
      });
    }, [focusIndex]);

    useHotkeys(
      ["up"],
      () => {
        // blur the active element
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
        (document.activeElement as HTMLElement)?.blur();
        setFocusIndex((prev) => Math.max(0, prev - 1));
      },
      {
        description: "Previous score",
        scopes: ["human-review"],
        preventDefault: true,
        enableOnFormTags: true,
      },
    );
    useHotkeys(
      ["p"],
      () => {
        setFocusIndex((prev) => Math.max(0, prev - 1));
      },
      {
        description: "Previous score",
        scopes: ["human-review"],
        preventDefault: true,
      },
    );
    useHotkeys(
      ["down"],
      () => {
        // blur the active element
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
        (document.activeElement as HTMLElement)?.blur();
        setFocusIndex((prev) => Math.min(scores.length - 1, prev + 1));
      },
      {
        description: "Next score",
        scopes: ["human-review"],
        preventDefault: true,
        enableOnFormTags: true,
      },
    );
    useHotkeys(
      ["n"],
      () => {
        setFocusIndex((prev) => Math.min(scores.length - 1, prev + 1));
      },
      {
        description: "Next score",
        scopes: ["human-review"],
        preventDefault: true,
      },
    );
    useHotkeys(
      ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "shift+0"],
      async (e) => {
        const score = scores[focusIndex];
        const currentRef = score ? refs.current[focusIndex] : undefined;
        if (
          !score ||
          !updateScore ||
          !currentRef ||
          score.config.score_type === "free-form"
        ) {
          return;
        }

        if (score.config.score_type === "slider") {
          const score = e.key === ")" ? 10 : parseInt(e.key);
          // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
          await (currentRef as NumericSliderHandle).save(score * 10);
        }

        if (score.config.score_type === "categorical") {
          if (e.key === ")" || score.config.categories === undefined) return;
          const index = parseInt(e.key);
          if (
            index === 0 &&
            Array.isArray(score.config.categories) &&
            !score.config.categories[9]
          ) {
            return;
          }
          if (
            Array.isArray(score.config.categories) &&
            index <= score.config.categories.length
          ) {
            // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
            (currentRef as ScoreToggleGroupHandle).save(
              score.config.categories[index === 0 ? 9 : index - 1],
            );
          }
        }

        maybeAdvance();
      },
      {
        preventDefault: true,
        description: "Set score value",
        scopes: ["human-review"],
      },
    );

    const maybeAdvance = useCallback(() => {
      if (!isAutoAdvanceEnabled) return;
      if (focusIndex === scores.length - 1) {
        onAutoAdvanceToNextRow();
      } else {
        setFocusIndex((prev) => Math.min(scores.length - 1, prev + 1));
      }
    }, [
      isAutoAdvanceEnabled,
      scores.length,
      focusIndex,
      onAutoAdvanceToNextRow,
    ]);

    const updateScore = useMemo(
      () =>
        updateScores
          ? async (
              scoreUpdate: ManualScoreUpdate,
            ): Promise<TransactionId | null> => {
              setSavingScore(true);
              const res = await updateScores([scoreUpdate]);
              setSavingScore(false);
              return res?.[0] ?? null;
            }
          : undefined,
      [updateScores],
    );

    const onAutoSaveFreeForm = useCallback(
      (n: string) => {
        const score = scores[focusIndex];
        if (!score || !updateScore) return Promise.resolve(null);
        return updateScore({
          name: score.config.name,
          category: null,
          value: n,
        });
      },
      [updateScore, focusIndex, scores],
    );

    const onFreeFormMetaEnter = useCallback(
      (type: "cmd" | "shift") => {
        if (type === "cmd") {
          maybeAdvance();
        }
      },
      [maybeAdvance],
    );

    const onFreeFormKeyUp = useCallback(
      (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
        if (e.key === "Escape") {
          e.currentTarget.blur();
        }
      },
      [],
    );

    const isFocusingOnTextArea = useMemo(() => {
      return scores[focusIndex]?.config.score_type === "free-form";
    }, [focusIndex, scores]);

    if (!updateScore) return null;

    return (
      <div
        className={cn("flex flex-col gap-2 opacity-100 transition-opacity", {
          "opacity-50 pointer-events-none":
            expandedRowState === "changing_rows" ||
            expandedRowState === "initially_loading" ||
            isLoading,
        })}
      >
        {scores.map((score, index) => {
          const { config } = score;
          const isFocused = focusIndex === index;
          const isWriteToExpected = config.config?.destination === "expected";
          const isMultiSelect = !!config.config?.multi_select;
          const customMetadataPath =
            config.score_type === "free-form"
              ? freeFormDataPath({
                  scoreType: config.score_type,
                  destination: config.config?.destination,
                  name: config.name,
                })
              : null;
          const value = average(score.right);
          return (
            <div
              key={config.id}
              id={`expanded-human-review-field-${index}`}
              className={cn(
                "mb-5 flex-none -mx-3 p-3 rounded-lg border border-transparent transition-all",
                {
                  "bg-primary-50 border-primary-400": isFocused,
                  "hover:bg-primary-50 cursor-pointer": !isFocused,
                },
              )}
              onClick={() => setFocusIndex(index)}
              onFocus={() => setFocusIndex(index)}
            >
              <label
                htmlFor={`expanded-human-review-field-input-${index}`}
                className="pointer-events-none mb-3 flex items-center justify-between gap-2 text-base font-medium"
              >
                {config.name}
                {isSavingScore && focusIndex === index && (
                  <Spinner className="size-4 opacity-50" />
                )}
              </label>
              {config.description && (
                <div className="mb-3 text-sm">
                  <MarkdownViewer className="p-0" value={config.description} />
                </div>
              )}
              {config.score_type === "slider" && (
                <>
                  <UncontrolledNumericSlider
                    updateOnBlur
                    title={score.config.name}
                    value={!isEmpty(value) ? value * 100 : undefined}
                    setValue={(v, isBlur) => {
                      if (!isBlur) {
                        return Promise.resolve(null);
                      }
                      return updateScore({
                        name: score.config.name,
                        category: null,
                        value: v !== undefined ? v / 100 : null,
                      });
                    }}
                    min={0}
                    max={100}
                    step={1}
                    unit="%"
                    compact
                    disabled={updateScore === undefined}
                    rowKey={rowKey}
                    xactId={xactId}
                    ref={(el) => {
                      if (!el) return;
                      refs.current[index] = el;
                    }}
                  />
                  <div
                    className={cn(
                      "flex flex-1 flex-wrap items-center gap-1 text-xs text-primary-400",
                    )}
                  >
                    Set score value <Key className="border-primary-100">0</Key>{" "}
                    - <Key className="border-primary-100">9</Key>
                  </div>
                </>
              )}

              {config.score_type === "free-form" && (
                <>
                  <FreeFormTextArea
                    score={score}
                    rowKey={rowKey}
                    xactId={xactId}
                    id={`expanded-human-review-field-input-${index}`}
                    onAutoSave={onAutoSaveFreeForm}
                    onMetaEnter={onFreeFormMetaEnter}
                    className={cn("min-h-16", {
                      // since we save text scores on blur, we don't want to allow
                      // focusing on a different text area until the previous score is saved
                      "pointer-events-none": isFocusingOnTextArea && !isFocused,
                    })}
                    onKeyUp={onFreeFormKeyUp}
                    ref={(el) => {
                      if (!el) return;
                      refs.current[index] = el;
                    }}
                  />
                  <div className="mt-3 text-xs text-primary-500">
                    This score will be written to{" "}
                    <span className="font-mono">
                      {customMetadataPath?.join(".") ??
                        `metadata."${score.config.name}"`}
                    </span>
                  </div>
                </>
              )}

              {config.score_type === "categorical" && (
                <>
                  {isWriteToExpected ? (
                    <UncontrolledCategoricalToggleGroup
                      score={score}
                      rowKey={rowKey}
                      xactId={xactId}
                      isMultiSelect={isMultiSelect}
                      updateCategories={(n) =>
                        updateScore({
                          name: score.config.name,
                          category: n,
                          value: null,
                        })
                      }
                      groupClassName="gap-2"
                      itemClassName="border text-base px-3 py-1"
                      selectedItemClassName="!bg-accent-500 !text-white"
                      selectedItemKeyboardShortcutClassName="text-white/50"
                      showKeyboardShortcuts
                      ref={(el) => {
                        if (!el) return;
                        refs.current[index] = el;
                      }}
                    />
                  ) : (
                    <ScoreToggleGroup
                      score={score}
                      rowKey={rowKey}
                      xactId={xactId}
                      updateScore={(n, v) =>
                        updateScore({
                          name: score.config.name,
                          category: n,
                          value: v,
                        })
                      }
                      groupClassName="gap-2"
                      itemClassName="border text-base px-3 py-1"
                      selectedItemClassName="!bg-accent-500 !text-white"
                      selectedItemKeyboardShortcutClassName="text-white/50"
                      showKeyboardShortcuts
                      ref={(el) => {
                        if (!el) return;
                        refs.current[index] = el;
                      }}
                    />
                  )}
                </>
              )}
              {isWriteToExpected && (
                <div className="mt-3 text-xs text-primary-500">
                  This score will be written to the expected field
                </div>
              )}
            </div>
          );
        })}
      </div>
    );
  },
);
ExpandedHumanReviewForm.displayName = "ExpandedHumanReviewForm";

const Key = ({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string;
}) => (
  <span
    className={cn(
      "inline-flex size-5 items-center justify-center rounded-sm border",
      className,
    )}
  >
    {children}
  </span>
);
