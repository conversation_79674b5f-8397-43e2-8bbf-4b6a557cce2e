import { useMemo } from "react";
import { type PreviewSpan, type Span, type PreviewTrace } from "./graph";
import { useHotkeys } from "react-hotkeys-hook";
import { useDebouncedCallback } from "#/utils/useDebouncedCallback";

function buildNodeList(roots: PreviewSpan[], seen: Set<string>) {
  const ret: PreviewSpan[] = [];
  for (const node of roots) {
    if (seen.has(node.id)) {
      continue;
    }
    seen.add(node.id);
    ret.push(node);
    ret.push(...buildNodeList(node.children, seen));
  }

  return ret;
}

export function useTraceShortcuts({
  trace,
  selectedSpan,
  setSelectedSpan,
}: {
  trace: PreviewTrace | null;
  selectedSpan: Span | null;
  setSelectedSpan(span: Span | null): void;
}) {
  const nodeList = useMemo(
    () => buildNodeList(trace ? [trace.root] : [], new Set()),
    [trace],
  );

  const rowIdx = selectedSpan
    ? nodeList.findIndex((n) => n.id === selectedSpan.id)
    : -1;

  const prevRow = useDebouncedCallback(() => {
    if (rowIdx > 0) {
      setSelectedSpan(nodeList[rowIdx - 1]);
    }
  }, 10);

  const nextRow = useDebouncedCallback(() => {
    if (rowIdx < nodeList.length - 1) {
      setSelectedSpan(nodeList[rowIdx + 1]);
    }
  }, 10);

  useHotkeys(
    "p",
    prevRow,
    {
      scopes: ["sidepanel"],
      description: "Move to the previous span",
      preventDefault: true,
    },
    [prevRow],
  );
  useHotkeys(
    "n",
    nextRow,
    {
      scopes: ["sidepanel"],
      description: "Move to the next span",
      preventDefault: true,
    },
    [nextRow],
  );
}
