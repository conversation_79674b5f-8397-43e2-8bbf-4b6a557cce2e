import {
  ArrowRightToLine,
  Braces,
  Database,
  FoldVertical,
  Minimize2,
  PlaySquare,
  TriangleAlert,
  UnfoldVertical,
} from "lucide-react";
import { ActionButton } from "./ActionButton";
import { useEntityStorage } from "#/lib/clientDataStorage";
import { parsePromptFromSpan, type Span } from "@braintrust/local";
import { useCallback, useContext, useMemo, useRef, useState } from "react";
import {
  BasicTooltip,
  Tooltip,
  TooltipContent,
  TooltipPortal,
  TooltipTrigger,
} from "#/ui/tooltip";
import { zodErrorToString } from "#/utils/validation";
import { PromptFromTrace } from "./prompt-from-trace";
import { ParentSpanNotFound } from "./parent-span-not-found";
import { type MultiTraceContext } from "#/ui/copilot/trace";
import { type SpanIFrame } from "@braintrust/core/typespecs";
import { But<PERSON> } from "#/ui/button";
import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import { useOrg } from "#/utils/user";
import { DatasetDropdown } from "#/ui/dataset-dropdown";
import { renderHotkey } from "#/utils/hotkeys";
import { useHotkeys } from "react-hotkeys-hook";
import { getSearchResultFieldClassName } from "./search-utils";
import { backfillSpanType, getSpanDisplayConfig } from "./span-display";
import { isEmpty } from "#/utils/object";
import { Spinner } from "#/ui/icons/spinner";
import { type SearchResultField } from "./trace-search-context";
import { cn } from "#/utils/classnames";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "#/ui/dialog";
import { DataTextEditor } from "#/ui/data-text-editor";
import { ErrorBoundary } from "#/utils/error-boundary";
import { ErrorBanner } from "#/ui/error-banner";
import { RefreshIframeButton } from "#/ui/iframe-viewer";

export const SpanHeader = ({
  isTraceTreeCollapsed,
  isSearchOpen,
  searchQuery,
  span,
  collapseSpanState,
  setCollapseSpanState,
  performAddRowsToDataset,
  isReadOnly,
  spanName,
  copilotContext,
  openCreateDatasetDialog,
  expandedFrame,
  expandedFrameRef,
  parsedSpanData,
  setExpandedFrame,
  parentSpanType,
}: {
  isTraceTreeCollapsed: boolean | null;
  isSearchOpen: boolean;
  searchQuery: string;
  span: Span;
  collapseSpanState?: "collapsed" | "expanded" | "mixed";
  setCollapseSpanState: (state: "collapsed" | "expanded" | "mixed") => void;
  isReadOnly?: boolean;
  performAddRowsToDataset: (params: {
    datasetName: string;
    datasetId: string;
    spans: Span[];
    selectedProjectId: string;
    selectedProjectName: string;
  }) => void;
  spanName: React.ReactNode;
  copilotContext: MultiTraceContext;
  openCreateDatasetDialog: (name: string) => void;
  expandedFrame?: SpanIFrame;
  expandedFrameRef: React.RefObject<HTMLIFrameElement | null>;
  parsedSpanData?: Record<string, unknown>;
  setExpandedFrame: (frameId: string | null) => void;
  parentSpanType?: string | null;
}) => {
  const [promptDialogOpened, setPromptDialogOpened] = useState(false);
  const parsedPrompt = useMemo(() => {
    if (!span) return undefined;
    return parsePromptFromSpan(span) ?? undefined;
  }, [span]);
  const isLLMScorerSpan = !!parsedPrompt && parentSpanType === "score";
  const tryText = isLLMScorerSpan ? "Try LLM scorer" : "Try prompt";

  const { id: orgId, name: orgName } = useOrg();
  const { projectId, projectName, orgDatasets } = useContext(ProjectContext);
  const [_, setTraceTreeCollapsed] = useEntityStorage({
    entityType: "traceTree",
    entityIdentifier: projectId ?? "",
    key: "isCollapsed",
  });

  const collapseSpanHandler = useCallback(() => {
    if (
      collapseSpanState === "mixed" ||
      collapseSpanState == "expanded" ||
      collapseSpanState === undefined
    ) {
      setCollapseSpanState("collapsed");
    } else if (collapseSpanState == "collapsed") {
      setCollapseSpanState("expanded");
    }
  }, [collapseSpanState, setCollapseSpanState]);

  const spanTitleRef = useRef<HTMLDivElement>(null);
  const [datasetDropdownOpen, setDatasetDropdownOpen] = useState(false);

  useHotkeys(
    "D",
    () => {
      setDatasetDropdownOpen(true);
      spanTitleRef.current?.scrollIntoView({
        behavior: "smooth",
        block: "center",
      });
    },
    {
      preventDefault: true,
      description: "Add span to dataset",
    },
  );

  return (
    <>
      <div className="flex flex-wrap items-center justify-end gap-1">
        {isTraceTreeCollapsed && !(isSearchOpen && searchQuery !== "") && (
          <div className="mr-2">
            <ActionButton
              hotkey="\"
              actionHandler={() => setTraceTreeCollapsed(false)}
              tooltipText="Show trace tree"
              icon={<ArrowRightToLine className="size-3" />}
              label="Trace tree"
            />
          </div>
        )}
        <div className="grow text-xs" ref={spanTitleRef}>
          Span
        </div>
        <Dialog>
          <DialogTrigger asChild>
            <div>
              <BasicTooltip side="bottom" tooltipContent="Raw span data">
                <Button
                  size="xs"
                  variant="ghost"
                  className="text-primary-400"
                  Icon={Braces}
                />
              </BasicTooltip>
            </div>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Raw span data</DialogTitle>
            </DialogHeader>
            <ErrorBoundary
              fallback={
                <ErrorBanner skipErrorReporting>
                  There was a problem rendering this content
                </ErrorBanner>
              }
            >
              <DataTextEditor value={span} readOnly />
            </ErrorBoundary>
          </DialogContent>
        </Dialog>
        <ActionButton
          hotkey="X"
          buttonVariant="ghost"
          actionHandler={collapseSpanHandler}
          tooltipText={
            collapseSpanState === "mixed" || collapseSpanState == "expanded"
              ? `Collapse all span sections`
              : `Expand all span sections`
          }
          className="text-primary-400"
          icon={
            collapseSpanState === "mixed" || collapseSpanState == "expanded" ? (
              <FoldVertical className="size-3" />
            ) : (
              <UnfoldVertical className="size-3" />
            )
          }
        />
        {projectName !== undefined && projectId && !isReadOnly && (
          <DatasetDropdown
            datasets={orgDatasets}
            onSelectDataset={(dataset) => {
              performAddRowsToDataset({
                datasetName: dataset.name,
                datasetId: dataset.id,
                spans: [span],
                selectedProjectId: dataset.project_id,
                selectedProjectName: dataset.project_name,
              });
            }}
            onCreateNewDataset={(name: string) => openCreateDatasetDialog(name)}
            open={datasetDropdownOpen}
            setOpen={setDatasetDropdownOpen}
          >
            <div>
              <BasicTooltip
                side="bottom"
                tooltipContent={
                  <>
                    Add span to dataset
                    <span className="ml-2.5 inline-block opacity-50">
                      {renderHotkey("D")}
                    </span>
                  </>
                }
              >
                <Button size="xs" className="truncate" Icon={Database}>
                  <span className="flex-1 truncate">Add span to dataset</span>
                </Button>
              </BasicTooltip>
            </div>
          </DatasetDropdown>
        )}
        {orgId &&
          orgName &&
          projectId &&
          projectName &&
          parsedPrompt &&
          !isReadOnly && (
            <>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div>
                    <ActionButton
                      hotkey="Mod+Shift+P"
                      buttonVariant="border"
                      icon={
                        parsedPrompt.success ? (
                          <PlaySquare className="size-3" />
                        ) : (
                          <TriangleAlert className="size-3" />
                        )
                      }
                      actionHandler={() => setPromptDialogOpened(true)}
                      tooltipText={tryText}
                      disabled={!parsedPrompt.success}
                      label={tryText}
                    />
                  </div>
                </TooltipTrigger>
                <TooltipPortal>
                  {!parsedPrompt.success && (
                    <TooltipContent className="max-w-sm text-xs">
                      Failed to parse prompt
                      <span className="mt-2 block whitespace-pre-wrap font-mono text-bad-800">
                        {zodErrorToString(parsedPrompt.error, 2, true)}
                      </span>
                    </TooltipContent>
                  )}
                </TooltipPortal>
              </Tooltip>
              {parsedPrompt.success && (
                <PromptFromTrace
                  projectId={projectId}
                  orgName={orgName}
                  projectName={projectName}
                  promptDialogOpened={promptDialogOpened}
                  setPromptDialogOpened={setPromptDialogOpened}
                  span={span}
                  copilotContext={copilotContext}
                  type={isLLMScorerSpan ? "scorer" : "prompt"}
                />
              )}
            </>
          )}
        {expandedFrame && (
          <>
            <Button
              Icon={Minimize2}
              size="xs"
              onClick={() => setExpandedFrame(null)}
            >
              Minimize span iframe
            </Button>
            <RefreshIframeButton
              spanIframe={expandedFrame}
              expandedFrameRef={expandedFrameRef}
              parsedSpanData={parsedSpanData}
              variant="border"
            />
          </>
        )}
      </div>
      <ParentSpanNotFound selectedSpan={span} />
      {spanName}
    </>
  );
};
type SpanNameProps = {
  hasError: boolean;
  resultIndex?: number | null;
  searchResultFields?: SearchResultField[];
  span: Span | null;
  className?: string;
  iconClassName?: string;
};

export const SpanName = ({
  hasError,
  resultIndex,
  searchResultFields,
  span,
  className,
  iconClassName,
}: SpanNameProps) => {
  if (!span) return null;

  const spanDisplayConfig = getSpanDisplayConfig({
    type:
      span?.data.span_attributes?.type ??
      backfillSpanType(span?.data.span_attributes?.name),
    cached: !!span?.data.metrics?.cached,
    remote: !!span?.data.span_attributes?.remote,
    hasError,
  });

  return (
    <div
      id="name-section"
      data-scroll-id="name-section"
      className={cn(
        "mb-2 flex items-center gap-2 text-lg font-semibold",
        className,
      )}
    >
      <div
        className={cn(
          spanDisplayConfig.iconClassName,
          "size-6 flex items-center justify-center rounded-md flex-none",
          iconClassName,
        )}
      >
        {isEmpty(span.data.metrics?.end) &&
        !isEmpty(span.data.metrics?.start) ? (
          <Spinner className="size-3" />
        ) : (
          <spanDisplayConfig.Icon className="size-4" />
        )}
      </div>
      <div
        className={getSearchResultFieldClassName({
          resultIndex,
          searchResultFields,
          spanId: span.id,
          field: "name",
          className: "flex-1 break-all",
        })}
      >
        {span.data.span_attributes?.name}
      </div>
    </div>
  );
};
