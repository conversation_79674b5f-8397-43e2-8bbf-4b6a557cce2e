"use client";

import { type RefObject, useContext, useMemo, useRef } from "react";
import { type SpanIdsMap, type PreviewSpan, type Span } from "#/ui/trace/graph";
import { ChevronRight, ListFilter } from "lucide-react";
import { getSpanDisplayConfig } from "./span-display";
import { cn } from "#/utils/classnames";
import { flattenSearchResults, useTraceSearch } from "./trace-search-context";
import { escapeRegExp } from "#/utils/regex";
import { Button } from "#/ui/button";
import { Checkbox, IndeterminateCheckbox } from "#/ui/checkbox";
import { CancelSelectionButton, SelectionBar } from "#/ui/table/selection-bar";
import { pluralize, pluralizeWithCount } from "#/utils/plurals";
import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import { type Dataset } from "@braintrust/core/typespecs";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from "#/ui/dropdown-menu";
import { DatasetDropdown } from "#/ui/dataset-dropdown";
import { useVirtualizer, type Virtualizer } from "@tanstack/react-virtual";
import { ExperimentColorSwatch } from "#/ui/charts/colors";
import { useInfiniteFetch } from "#/utils/virtualizer/infinite-fetch";
import { Spinner } from "#/ui/icons/spinner";

export const TraceSearchResults = ({
  spans,
  comparisonSpans,
  onGoToField,
  openCreateDatasetDialog,
  onBulkAddToDataset,
  virtualizerRef,
  comparisonExperimentIndex,
  spanIdsMap,
}: {
  spans?: Record<
    string,
    (Span | PreviewSpan) & { traceName?: string; traceClassName?: string }
  >;
  comparisonSpans?: Record<
    string,
    (Span | PreviewSpan) & { traceName?: string; traceClassName?: string }
  >;
  onGoToField: ({ span, field }: { span: Span; field: string }) => void;
  openCreateDatasetDialog?: () => void;
  onBulkAddToDataset?: (dataset: Dataset, spanIds: string[]) => void;
  virtualizerRef: RefObject<Virtualizer<HTMLDivElement, Element> | null>;
  comparisonExperimentIndex?: number;
  spanIdsMap?: SpanIdsMap;
}) => {
  const {
    searchQuery,
    setSearchOpen,
    setResultIndex,
    searchResults,
    selectedIndex,
    setSelectedIndex,
    selectedSpanIds,
    setSelectedSpanIds,
    spanFieldsToSearch,
    setSpanFieldsToSearch,
    spanTypesToSearch,
    setSpanTypesToSearch,
    searchQueryProps,
  } = useTraceSearch();

  const { orgDatasets } = useContext(ProjectContext);

  const addSpanToDatasetEnabled =
    !!openCreateDatasetDialog && !!onBulkAddToDataset;

  const { hasNextPage, fetchNextPage, isFetchingNextPage, isPending } =
    searchQueryProps;

  const flattenedSearchResults = useMemo(() => {
    return flattenSearchResults<{
      traceName?: string;
      traceClassName?: string;
    }>(searchResults, spans, comparisonSpans);
  }, [searchResults, spans, comparisonSpans]);

  const uniqueSpanIdsInFlattenedSearchResults = useMemo(() => {
    return new Set(flattenedSearchResults.map((result) => result!.spanId));
  }, [flattenedSearchResults]);

  const { allSpansSelected, someSpansSelected } = useMemo(() => {
    const uniqueSpanIds = [...uniqueSpanIdsInFlattenedSearchResults];
    const allSpansSelected = uniqueSpanIds.every((id) =>
      selectedSpanIds.has(id),
    );
    const someSpansSelected = uniqueSpanIds.some((id) =>
      selectedSpanIds.has(id),
    );
    return { allSpansSelected, someSpansSelected };
  }, [uniqueSpanIdsInFlattenedSearchResults, selectedSpanIds]);

  const selectedSpanIdsArray = Array.from(selectedSpanIds).filter((id) =>
    uniqueSpanIdsInFlattenedSearchResults.has(id),
  );

  const parentRef = useRef<HTMLDivElement>(null);

  const virtualizer = useVirtualizer({
    count: hasNextPage
      ? flattenedSearchResults.length + 1
      : flattenedSearchResults.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => 60, // Conservative estimate, will be measured accurately
    overscan: 3,
    // Enable dynamic measurement
    measureElement: (element) => {
      return element?.getBoundingClientRect().height ?? 60;
    },
  });
  // eslint-disable-next-line react-compiler/react-compiler
  virtualizerRef.current = virtualizer;

  useInfiniteFetch({
    virtualizer,
    hasNextPage,
    fetchNextPage,
    totalRowCount: flattenedSearchResults.length,
    isFetching: isFetchingNextPage,
  });

  return (
    <div
      className="flex flex-1 flex-col gap-px overflow-auto p-1 pb-36"
      ref={parentRef}
    >
      <div className="flex flex-none items-center gap-2 py-2 pl-3 pr-1 text-xs text-primary-500">
        {addSpanToDatasetEnabled && (
          <IndeterminateCheckbox
            disabled={flattenedSearchResults.length === 0}
            state={
              flattenedSearchResults.length === 0
                ? "unchecked"
                : allSpansSelected
                  ? "checked"
                  : someSpansSelected
                    ? "indeterminate"
                    : "unchecked"
            }
            className="-ml-px"
            onChange={() => {
              setSelectedSpanIds(
                allSpansSelected
                  ? new Set()
                  : new Set(searchResults?.map((result) => result.data.id)),
              );
            }}
          />
        )}
        <span className="flex-1">
          loaded{" "}
          {pluralizeWithCount(
            uniqueSpanIdsInFlattenedSearchResults.size,
            "span",
            "spans",
          )}{" "}
          with results
        </span>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button size="xs" className="text-primary-600" Icon={ListFilter} />
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuLabel>Filter span search results</DropdownMenuLabel>
            <DropdownMenuSub>
              <DropdownMenuSubTrigger>Span type</DropdownMenuSubTrigger>
              <DropdownMenuSubContent>
                {Object.entries(spanTypesToSearch).map(([type, value]) => {
                  const spanConfig = getSpanDisplayConfig({
                    type,
                    cached: false,
                    remote: false,
                    hasError: false,
                  });
                  return (
                    <DropdownMenuCheckboxItem
                      key={type}
                      checked={value}
                      onSelect={(e) => {
                        e.preventDefault();
                        setSpanTypesToSearch((prev) => ({
                          ...prev,
                          [type]: !value,
                        }));
                      }}
                    >
                      <spanConfig.Icon
                        className={cn(
                          spanConfig.iconClassName,
                          "size-4 rounded-[4px] mr-1.5 p-0.5",
                        )}
                      />
                      {spanConfig.label || "Other"}
                    </DropdownMenuCheckboxItem>
                  );
                })}
              </DropdownMenuSubContent>
            </DropdownMenuSub>
            <DropdownMenuSub>
              <DropdownMenuSubTrigger>Span field</DropdownMenuSubTrigger>
              <DropdownMenuSubContent>
                {Object.entries(spanFieldsToSearch).map(([field, value]) => (
                  <DropdownMenuCheckboxItem
                    key={field}
                    checked={value}
                    onSelect={(e) => {
                      e.preventDefault();
                      setSpanFieldsToSearch((prev) => ({
                        ...prev,
                        [field]: !value,
                      }));
                    }}
                  >
                    {field}
                  </DropdownMenuCheckboxItem>
                ))}
              </DropdownMenuSubContent>
            </DropdownMenuSub>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <div>
        {flattenedSearchResults.length > 0 ? (
          <div
            style={{
              height: `${virtualizerRef.current?.getTotalSize()}px`,
              width: "100%",
              position: "relative",
            }}
          >
            {virtualizerRef.current?.getVirtualItems().map((virtualItem) => {
              const isLoaderRow =
                virtualItem.index > flattenedSearchResults.length - 1;
              const result = flattenedSearchResults[virtualItem.index];
              if (!result && !isLoaderRow) return null;

              const { field, value, span, spanConfig } = result ?? {};
              const isSpanSelected = selectedSpanIds.has(span?.id);

              return (
                <div
                  key={virtualItem.key}
                  data-index={virtualItem.index}
                  ref={(node) => virtualizerRef.current?.measureElement(node)}
                  style={{
                    position: "absolute",
                    top: 0,
                    left: 0,
                    width: "100%",
                    transform: `translateY(${virtualItem.start}px)`,
                  }}
                >
                  {isLoaderRow ? (
                    <div className="flex items-center justify-center pt-2">
                      <Spinner />
                    </div>
                  ) : (
                    <button
                      onClick={() => {
                        // Since spans for comparison experiments show up as their own spans
                        // we want to select the associated span from the primary experiment
                        // if it exists.
                        // In the case where there is a comparison span but no primary span,
                        // that would indicate that the traces aren't matching.
                        //   > In those cases we use the comparison span directly.
                        const primarySpan =
                          spans?.[spanIdsMap?.[span.span_id]?.spanId ?? ""] ??
                          span;
                        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
                        onGoToField({ span: primarySpan as Span, field });
                        setResultIndex(virtualItem.index);
                        setSelectedIndex(virtualItem.index);
                      }}
                      data-selected={selectedIndex === virtualItem.index}
                      className={cn(
                        "group flex w-full flex-none flex-col gap-1 overflow-hidden rounded px-[11px] py-1.5 text-xs transition-colors bg-transparent data-[selected=true]:bg-primary-100 hover:bg-primary-100",
                        {
                          "bg-accent-50 data-[selected=true]:bg-accent-100":
                            isSpanSelected,
                        },
                      )}
                    >
                      <span className="flex w-full items-center gap-1">
                        {addSpanToDatasetEnabled && (
                          <Checkbox
                            className={cn(
                              "m-0 mr-1 size-4 translate-y-[-3px] p-0 block",
                              {
                                "hidden group-hover:block": !isSpanSelected,
                              },
                            )}
                            checked={isSpanSelected}
                            onChange={() => {
                              setSelectedSpanIds(
                                new Set(
                                  selectedSpanIds.has(span.id)
                                    ? Array.from(selectedSpanIds).filter(
                                        (id) => id !== span.id,
                                      )
                                    : [...selectedSpanIds, span.id],
                                ),
                              );
                            }}
                            onClick={(e) => {
                              e.stopPropagation();
                            }}
                          />
                        )}
                        <span
                          className={cn(
                            spanConfig.iconClassName,
                            "size-4 flex flex-none items-center justify-center rounded-[3px] mr-1",
                            {
                              hidden: isSpanSelected && addSpanToDatasetEnabled,
                              "group-hover:hidden":
                                !isSpanSelected && addSpanToDatasetEnabled,
                            },
                          )}
                        >
                          <spanConfig.Icon className="size-[10px]" />
                        </span>
                        {comparisonExperimentIndex != null && (
                          <ExperimentColorSwatch
                            index={
                              result.isComparison
                                ? comparisonExperimentIndex + 1
                                : 0
                            }
                          />
                        )}
                        <span className="flex-none font-medium">
                          {span.data.span_attributes.name}
                        </span>
                        <ChevronRight className="size-3 flex-none text-primary-500" />
                        <span className="truncate font-normal">{field}</span>
                        {span.traceName && (
                          <span
                            className="ml-auto flex items-center gap-1.5 text-xs"
                            title={span.traceName}
                          >
                            <span className="max-w-36 truncate">
                              {span.traceName}
                            </span>
                            <span
                              className={cn("size-2 flex-none rounded-full", [
                                span.traceClassName,
                              ])}
                            />
                          </span>
                        )}
                      </span>
                      <span className="line-clamp-2 max-w-md hyphens-auto break-words text-left text-xs text-primary-500">
                        {generateSnippet(value, searchQuery, 60)}
                      </span>
                    </button>
                  )}
                </div>
              );
            })}
          </div>
        ) : hasNextPage || isFetchingNextPage || isPending ? (
          <div className="flex items-center justify-center">
            <Spinner />
          </div>
        ) : (
          <div className="pb-2 pt-8 text-center text-xs text-primary-400">
            No results found
          </div>
        )}
      </div>

      <div className="flex-none">
        <Button
          size="xs"
          className="my-1 w-full text-primary-500"
          variant="ghost"
          onClick={() => setSearchOpen(false)}
        >
          Clear search
        </Button>
      </div>
      {addSpanToDatasetEnabled && selectedSpanIdsArray.length > 0 && (
        <SelectionBar className="absolute">
          <CancelSelectionButton
            onCancelSelection={() => setSelectedSpanIds(new Set())}
            selectedRowsNumber={selectedSpanIdsArray.length}
            selectedLabel={`${pluralize(
              selectedSpanIdsArray.length,
              "span",
              "spans",
            )} selected`}
          />

          <DatasetDropdown
            datasets={orgDatasets}
            onCreateNewDataset={openCreateDatasetDialog}
            onSelectDataset={(dataset) =>
              onBulkAddToDataset(dataset, selectedSpanIdsArray)
            }
          >
            <Button size="xs">Add selected to dataset</Button>
          </DatasetDropdown>
        </SelectionBar>
      )}
    </div>
  );
};

const generateSnippet = (
  text: string,
  query: string,
  snippetLength: number,
) => {
  const trimmedQuery = query.trim();
  if (!trimmedQuery) {
    // If the query is empty or only whitespace, return the truncated text without highlighting
    return text.length > snippetLength
      ? `${text.slice(0, snippetLength)}...`
      : text;
  }

  const regex = new RegExp(
    `(.{0,${snippetLength}})(${escapeRegExp(trimmedQuery)})(.{0,${snippetLength}})`,
    "i",
  );
  const match = regex.exec(text);

  if (match) {
    const before = match[1] || "";
    const matched = match[2] || "";
    const after = match[3] || "";
    return (
      <>
        {before.length === snippetLength ? `...${before}` : before}
        <span className="bg-yellow-500/30 text-primary-700">{matched}</span>
        {after.length === snippetLength ? `${after}...` : after}
      </>
    );
  }

  // If no match is found, return the truncated text
  return text.length > snippetLength
    ? `${text.slice(0, snippetLength)}...`
    : text;
};
