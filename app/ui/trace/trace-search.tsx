"use client";

import {
  use<PERSON><PERSON>back,
  useEffect,
  useMemo,
  useTransition,
  type RefObject,
} from "react";
import { type SpanData, type PreviewSpan, type Span } from "#/ui/trace/graph";
import { Input } from "#/ui/input";
import { ArrowLeft, ArrowRight, SearchIcon, XIcon } from "lucide-react";
import {
  flattenSearchResults,
  useTraceSearch,
  useTraceSearchSetters,
} from "./trace-search-context";
import { Button } from "#/ui/button";
import scrollIntoViewIfNeeded from "scroll-into-view-if-needed";
import { Spinner } from "#/ui/icons/spinner";
import { type Virtualizer } from "@tanstack/react-virtual";
import Fuse from "fuse.js";
import { useDebouncedCallback } from "#/utils/useDebouncedCallback";
import { unknownToString } from "./search-utils";

export interface TraceSearchResult {
  id: string;
  spanId: string;
  name: string;
  type: string | undefined;
  scores: string;
  input: string;
  output: string;
  expected: string;
  metadata: string;
  tags: string;
}

export const TraceSearch = ({
  selectedSpanId,
  ...props
}: { selectedSpanId: string } & TraceSearchComponentProps) => {
  const { setSearchQuery, setSearchResults, setSelectedIndex } =
    useTraceSearchSetters();

  const { spans } = props;
  const searchIndex = useMemo(() => {
    const documents = Object.values(spans ?? {}).map((span: Span) => ({
      id: span.id,
      spanId: span.span_id,
      name: span.data.span_attributes.name,
      type: span.data.span_attributes.type,
      scores: unknownToString(span.scores),
      input: unknownToString(span.data.input),
      output: unknownToString(span.data.output),
      expected: unknownToString(span.data.expected),
      metadata: unknownToString(span.data.metadata),
      tags: span.data.tags?.join(", ") ?? "",
    }));

    return new Fuse<TraceSearchResult>(documents, {
      keys: [
        "name",
        "scores",
        "input",
        "output",
        "expected",
        "metadata",
        "tags",
      ],
      includeMatches: true,
      ignoreLocation: true,
      distance: 0,
      threshold: 0.0,
    });
  }, [spans]);

  const [isPending, startTransition] = useTransition();
  const onChange = useDebouncedCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      startTransition(() => {
        const { value } = e.target;
        const trimmedValue = value.trim();
        setSearchQuery(value);

        if (!trimmedValue) {
          // If the query is empty or whitespace, reset search results
          setSearchResults([]);
          setSelectedIndex(0);
          return;
        }

        const results = searchIndex
          .search(trimmedValue)
          // Sort by selected span at search time
          .sort((a, b) => {
            return a.item.id === selectedSpanId
              ? -1
              : b.item.id === selectedSpanId
                ? 1
                : 0;
          });

        setSearchResults(
          results.map((result) => {
            const { id, spanId, name, type } = result.item;
            return {
              id,
              spanId,
              data: {
                id,
                span_id: spanId,
                span_attributes: {
                  name,
                  type: type ?? "other",
                },
              },
              type: type ?? "other",
              matches:
                result.matches?.flatMap((m) =>
                  m.key && m.value
                    ? [
                        {
                          field: m.key,
                          value: m.value,
                        },
                      ]
                    : [],
                ) ?? [],
            };
          }),
        );
        setSelectedIndex(0);
      });
    },
    // Debounce if there are more than 100 spans to minimize input blocking during search
    Object.keys(spans ?? {}).length > 100 ? 400 : 0,
  );

  return (
    <TraceSearchComponent
      {...props}
      isPending={isPending}
      onChange={onChange}
    />
  );
};

export const PaginatedTraceSearch = ({
  selectedSpans,
  ...props
}: {
  selectedSpans: {
    primary?: SpanData;
    comparison?: SpanData;
  };
} & TraceSearchComponentProps) => {
  const { setSearchQuery, setActiveSpans, setSelectedIndex } = useTraceSearch();

  const [isPending, startTransition] = useTransition();

  const onChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      startTransition(() => {
        const { value } = e.target;
        const trimmedValue = value.trim();
        setSearchQuery(trimmedValue);
        setActiveSpans({
          primary: selectedSpans.primary,
          comparison: selectedSpans.comparison,
        });
        setSelectedIndex(0);
      });
    },
    [
      setSearchQuery,
      setSelectedIndex,
      setActiveSpans,
      selectedSpans.primary,
      selectedSpans.comparison,
    ],
  );

  return (
    <TraceSearchComponent
      {...props}
      isPending={isPending}
      onChange={onChange}
    />
  );
};

type TraceSearchComponentProps = {
  virtualizerRef: RefObject<Virtualizer<HTMLDivElement, Element> | null>;
  spans?: Record<string, Span | PreviewSpan>;
  comparisonSpans?: Record<string, Span | PreviewSpan>;
  onGoToField: ({ span, field }: { span: Span; field: string }) => void;
  isDataset?: boolean;
};

const TraceSearchComponent = ({
  virtualizerRef,
  spans,
  comparisonSpans,
  onGoToField,
  isDataset,
  isPending,
  onChange,
}: {
  isPending: boolean;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
} & TraceSearchComponentProps) => {
  const {
    searchQuery,
    setSearchOpen,
    setResultIndex,
    resultIndex,
    searchResults,
    selectedIndex,
    setSelectedIndex,
  } = useTraceSearch();

  useEffect(() => {
    const selected = document.querySelector("button[data-selected='true']");
    if (selected) {
      scrollIntoViewIfNeeded(selected, {
        scrollMode: "if-needed",
        block: "end",
      });
    }
  }, [selectedIndex]);

  const flattenedSearchResults = useMemo(() => {
    return flattenSearchResults(searchResults, spans, comparisonSpans);
  }, [searchResults, spans, comparisonSpans]);

  const goToIndex = (index: number) => {
    const result = flattenedSearchResults[index];
    if (result) {
      onGoToField({ span: result.span, field: result.field });
      setResultIndex(index);
    }
  };

  return (
    <div className="relative flex h-10 flex-none items-center gap-2 border-b">
      {isPending ? (
        <Spinner className="pointer-events-none absolute left-4 top-3.5 size-3 text-primary-400" />
      ) : (
        <SearchIcon className="pointer-events-none absolute left-4 top-1/2 size-3 -translate-y-1/2 text-primary-500" />
      )}
      <Input
        autoFocus
        className="h-10 flex-1 truncate rounded-none border-0 py-0 pl-9 pr-30 text-sm ring-0 focus-visible:ring-0"
        placeholder={isDataset ? "Find in dataset row" : "Find in trace"}
        onKeyDown={(e) => {
          if (e.key === "Escape") {
            e.preventDefault();
            setSearchOpen(false);
          }
          if (e.key === "Enter") {
            e.preventDefault();
            goToIndex(selectedIndex);
          }
          if (e.key === "ArrowDown") {
            e.preventDefault();
            setSelectedIndex((prev) =>
              prev + 1 >= flattenedSearchResults.length ? 0 : prev + 1,
            );
          }
          if (e.key === "ArrowUp") {
            e.preventDefault();
            setSelectedIndex((prev) =>
              prev - 1 < 0 ? flattenedSearchResults.length - 1 : prev - 1,
            );
          }

          const nextIndex =
            ((e.key === "ArrowDown"
              ? selectedIndex + 1
              : e.key === "ArrowUp"
                ? selectedIndex - 1
                : selectedIndex) +
              flattenedSearchResults.length) %
            flattenedSearchResults.length;
          if (nextIndex >= 0 && nextIndex < flattenedSearchResults.length) {
            // Use virtualizer's scrollToIndex for smooth scrolling
            virtualizerRef.current?.scrollToIndex(nextIndex, {
              align: "center",
            });
          }
        }}
        onChange={onChange}
      />
      <div className="absolute inset-y-0 right-0 flex items-center justify-end gap-1 pr-4">
        {searchQuery && (
          <>
            {flattenedSearchResults.length > 1 && resultIndex !== null && (
              <>
                <span className="text-xs text-primary-500">
                  {resultIndex + 1} of {flattenedSearchResults.length} loaded
                  fields
                </span>
                <Button
                  size="xs"
                  variant="ghost"
                  className="text-primary-600"
                  Icon={ArrowLeft}
                  onClick={() => {
                    const newSelectedIndex =
                      selectedIndex - 1 < 0
                        ? flattenedSearchResults.length - 1
                        : selectedIndex - 1;
                    setSelectedIndex(newSelectedIndex);
                    goToIndex(newSelectedIndex);
                    virtualizerRef.current?.scrollToIndex(newSelectedIndex, {
                      align: "center",
                    });
                  }}
                />
                <Button
                  size="xs"
                  variant="ghost"
                  className="text-primary-600"
                  Icon={ArrowRight}
                  onClick={() => {
                    const newSelectedIndex =
                      selectedIndex + 1 >= flattenedSearchResults.length
                        ? 0
                        : selectedIndex + 1;
                    setSelectedIndex(newSelectedIndex);
                    goToIndex(newSelectedIndex);
                    virtualizerRef.current?.scrollToIndex(newSelectedIndex, {
                      align: "center",
                    });
                  }}
                />
              </>
            )}
          </>
        )}
        <Button
          size="xs"
          variant="ghost"
          className="text-primary-600"
          Icon={XIcon}
          onClick={() => setSearchOpen(false)}
        />
      </div>
    </div>
  );
};
