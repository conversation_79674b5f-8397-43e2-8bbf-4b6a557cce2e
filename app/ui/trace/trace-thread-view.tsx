import { isEmpty, isObject } from "@braintrust/core";
import {
  type SpanData,
  type PreviewSpan,
  type Span,
  type Trace,
} from "./graph";
import {
  DiffRightField,
  getDiffRight,
  isDiffObject,
} from "#/utils/diffs/diff-objects";
import { SpanName } from "./span-header";
import {
  LLMMessage,
  type LLMMessageType,
  parseLLMSpanPart,
} from "#/ui/LLMView";
import { MarkdownViewer } from "#/ui/markdown";
import { cn } from "#/utils/classnames";
import { ErrorBoundary } from "#/utils/error-boundary";
import { ErrorBanner } from "#/ui/error-banner";
import { ThreadSpanMetrics } from "./thread-span-metrics";
import { TableEmptyState } from "#/ui/table/TableEmptyState";
import { NullFormatter } from "#/ui/table/formatters/null-formatter";
import { useVirtualizer } from "@tanstack/react-virtual";
import {
  useMemo,
  useCallback,
  useRef,
  useEffect,
  useState,
  useContext,
} from "react";
import React from "react";
import { ErrorDisplay } from "./trace-error";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { fetchBtqlPaginated, useFetchBtqlOptions } from "#/utils/btql/btql";
import { useBtqlQueryBuilder } from "#/utils/btql/use-query-builder";
import { type DataObjectType } from "#/utils/btapi/btapi";
import { type Expr } from "@braintrust/btql/parser";
import { z } from "zod";
import { Spinner } from "#/ui/icons/spinner";
import { Skeleton } from "#/ui/skeleton";
import { type ModelCosts } from "#/ui/prompts/models";
import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import {
  makeFullSpanQueryKey,
  processFullSpanData,
  useRealtimeSpans,
} from "./use-load-full-spans";
import { deserializePlainStringAsJSON } from "braintrust";
import { CollapsibleSection } from "#/ui/collapsible-section";
import { Bolt } from "lucide-react";
import { useInfiniteFetch } from "#/utils/virtualizer/infinite-fetch";

const hashCache = new Map<string, string>();

const hashMessage = (message: LLMMessageType, index: number): string => {
  const messageString = `${message.role}:${index}:${JSON.stringify(message.content)}`;

  if (hashCache.has(messageString)) {
    return hashCache.get(messageString)!;
  }

  let hash = 0;
  for (let i = 0; i < messageString.length; i++) {
    const char = messageString.charCodeAt(i);
    hash = (hash << 5) - hash + char;
    hash = hash & hash; // Convert to 32bit integer
  }
  const hashString = hash.toString(36); // Convert to base36 for shorter string

  // Cache the result
  hashCache.set(messageString, hashString);
  return hashString;
};

const getThreadSpans = (rootSpans: Span[]): Span[] => {
  const spanMetadata: Span[] = [];

  const scanSpan = (span: Span) => {
    const isLLM = span.data.span_attributes?.type === "llm";
    const isScore = span.data.span_attributes?.type === "score";

    if (isLLM || isScore) {
      spanMetadata.push(span);
    }

    if (!isScore) {
      span.children.forEach(scanSpan);
    }
  };

  rootSpans.forEach(scanSpan);
  return spanMetadata;
};

export const TraceThreadView = (props: TraceThreadViewComponentProps) => {
  const { trace } = props;
  const threadSpans = useMemo(() => {
    return getThreadSpans([trace.root]);
  }, [trace.root]);

  return <TraceThreadViewComponent {...props} threadSpans={threadSpans} />;
};

const threadSpanSchema = z.object({
  id: z.string(),
  span_id: z.string(),
  input: z.any(),
  output: z.any(),
  metadata: z.any(),
});

const PAGE_SIZE = 5;

export const PaginatedTraceThreadView = ({
  trace,
  objectType,
  objectId,
  setSelectedSpan,
  setViewType,
  containerRef,
  allAvailableModelCosts,
}: {
  trace: Trace;
  objectType: DataObjectType;
  objectId: string;
} & TraceThreadViewComponentProps) => {
  const btqlOptions = useFetchBtqlOptions();
  const builder = useBtqlQueryBuilder({});
  const rootSpanId = trace.root.root_span_id;
  const threadSpanPages = useMemo(() => {
    const ids: { id: string; children: string[] }[] = [];

    // in-order traversal
    function visit(span: Span | PreviewSpan) {
      const isLLM = span.data.span_attributes?.type === "llm";
      const isScore = span.data.span_attributes?.type === "score";
      if (isLLM) {
        ids.push({ id: span.id, children: [] });
      }
      if (isScore) {
        ids.push({
          id: span.id,
          // process children for scorer spans directly
          children: span.children.flatMap((c) =>
            c.data.span_attributes?.type === "llm" &&
            "purpose" in c.data.span_attributes &&
            c.data.span_attributes.purpose === "scorer"
              ? [c.id]
              : [],
          ),
        });
        // skip recursing for scorer spans since we process the children directly
        return;
      }

      span.children.forEach(visit);
    }

    visit(trace.root);
    const pages: { id: string; children: string[] }[][] = [];
    for (let i = 0; i < ids.length; i += PAGE_SIZE) {
      pages.push(ids.slice(i, i + PAGE_SIZE));
    }

    return pages;
  }, [trace]);

  const [pageIndex, setPageIndex] = useState(0);
  const hasNextPage = threadSpanPages.length - 1 > pageIndex;
  const fetchNextPage = useCallback(() => {
    setPageIndex((prev) => prev + 1);
  }, []);

  const { projectId } = useContext(ProjectContext);
  const queryClient = useQueryClient();
  const {
    data: queriedData,
    isPending,
    isFetching,
    error,
  } = useQuery({
    queryKey: [
      "threadSpans",
      objectType,
      objectId,
      projectId,
      rootSpanId,
      pageIndex,
      threadSpanPages,
      btqlOptions,
    ],
    queryFn: async ({ signal }: { signal: AbortSignal }) => {
      const { rowIds, primaryRowIds } = threadSpanPages
        .slice(0, pageIndex + 1)
        .reduce(
          (acc: { rowIds: string[]; primaryRowIds: string[] }, page) => {
            page.forEach(({ id, children }) => {
              acc.rowIds.push(...[id, ...children.slice(0, 1)]);
              acc.primaryRowIds.push(id);
            });
            return acc;
          },
          { rowIds: [], primaryRowIds: [] },
        );
      const { existing, idsToFetch } = rowIds.reduce(
        (acc: { existing: SpanData[]; idsToFetch: string[] }, id) => {
          const existing = queryClient.getQueryData(
            makeFullSpanQueryKey(objectType, objectId!, projectId, id),
          );
          if (existing && isObject(existing) && "data" in existing) {
            // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
            acc.existing.push(existing.data as SpanData);
          } else {
            acc.idsToFetch.push(id);
          }
          return acc;
        },
        { existing: [], idsToFetch: [] },
      );
      const res =
        idsToFetch.length > 0
          ? await fetchBtqlPaginated(
              {
                args: {
                  query: {
                    from: builder.from(
                      objectType,
                      objectId ? [objectId] : [],
                      "spans",
                    ),
                    select: [{ op: "star" }],
                    filter: builder.or(
                      ...idsToFetch.map(
                        (id): Expr => ({
                          op: "eq",
                          left: builder.ident("id"),
                          right: {
                            op: "literal",
                            value: id,
                          },
                        }),
                      ),
                    ),
                  },
                  brainstoreRealtime: true,
                  useColumnstore: false,
                },
                ...btqlOptions,
                schema: threadSpanSchema,
                signal,
              },
              PAGE_SIZE,
            )
          : undefined;
      const threadSpanIndex = Object.fromEntries(
        primaryRowIds.map((id, index) => [id, index]),
      );
      const queriedData = idsToFetch.flatMap((id) => {
        const span = processFullSpanData(objectType, id, res?.data ?? []);
        if (!span) {
          return [];
        }
        return span;
      });
      const { data, childData } = [...queriedData, ...existing].reduce(
        (
          acc: { data: SpanData[]; childData: Record<string, SpanData> },
          span,
        ) => {
          if (threadSpanIndex[span.id] != null) {
            acc.data.push(span);
          } else {
            acc.childData[span.id] = span;
          }
          return acc;
        },
        { data: [], childData: {} },
      );
      return {
        data: data.toSorted((a, b) => {
          const aIndex = threadSpanIndex[a.id];
          const bIndex = threadSpanIndex[b.id];
          return aIndex - bIndex;
        }),
        childData,
      };
    },
    enabled: !!objectId && !!projectId,
    staleTime: Infinity,
  });

  const realtimeIdsToWatch = useMemo(
    () =>
      threadSpanPages.flatMap((page) =>
        page.flatMap((p) => [p.id, ...p.children].map((id) => id)),
      ),
    [threadSpanPages],
  );
  const allQueriedData = useMemo(() => {
    return [
      ...(queriedData?.data ?? []),
      ...Object.values(queriedData?.childData ?? {}),
    ].map((d) => ({
      data: d,
      customColumnsData: null,
    }));
  }, [queriedData]);
  const cachedData = useRealtimeSpans({
    idsToWatch: realtimeIdsToWatch,
    objectType,
    objectId,
    queriedSpanData: allQueriedData,
  });

  const threadSpans: Span[] = useMemo(() => {
    if (!queriedData) {
      return [];
    }

    const cachedSpanData = Object.fromEntries(
      cachedData.flatMap((d) => (d.data?.id ? [[d.data.id, d.data]] : [])),
    );
    const childData = queriedData.childData;
    return queriedData.data.flatMap((d) => {
      const previewSpan = trace.spans[d.span_id];
      if (
        // switching traces can cause this to not match loaded data
        !previewSpan
      ) {
        return [];
      }

      const spanData = cachedSpanData[previewSpan.id] ?? d;

      // make some kind of semi-loaded span shape
      // preserve relevant children spans for LLM scorers
      return [
        {
          ...previewSpan,
          data: {
            ...previewSpan.data,
            ...spanData,
          },
          children: previewSpan.children.map((c) => {
            const childSpanData = cachedSpanData[c.id] ?? childData?.[c.id];
            return childSpanData
              ? {
                  ...c,
                  data: {
                    ...c.data,
                    ...childData[c.id],
                  },
                }
              : c;
          }),
        },
      ];
    });
  }, [queriedData, cachedData, trace.spans]);

  return (
    <>
      {isPending ? (
        <div className="flex flex-col gap-3 px-3">
          <Skeleton className="h-10" />
          <Skeleton className="h-10" />
          <Skeleton className="h-10" />
          <Skeleton className="h-10" />
        </div>
      ) : (
        <TraceThreadViewComponent
          trace={trace}
          threadSpans={threadSpans}
          queryProps={{
            error,
            isFetching,
            hasNextPage,
            fetchNextPage,
          }}
          setSelectedSpan={setSelectedSpan}
          setViewType={setViewType}
          containerRef={containerRef}
          allAvailableModelCosts={allAvailableModelCosts}
        />
      )}
    </>
  );
};

function noopFn() {}

type TraceThreadViewComponentProps = {
  trace: Trace;
  setSelectedSpan: (span: Span) => void;
  setViewType: (viewType: "trace" | "timeline" | "thread") => void;
  containerRef: React.RefObject<HTMLDivElement | null>;
  allAvailableModelCosts?: Record<string, ModelCosts>;
};

const TraceThreadViewComponent = ({
  trace,
  queryProps,
  threadSpans,
  setSelectedSpan,
  setViewType,
  containerRef,
  allAvailableModelCosts,
}: {
  queryProps?: {
    error: Error | null;
    isFetching: boolean;
    hasNextPage: boolean;
    fetchNextPage: () => void;
  };
  threadSpans: Span[];
} & TraceThreadViewComponentProps) => {
  // Cache for processed span data - only compute when item enters viewport
  const processedSpansRef = useRef(
    new Map<
      string,
      {
        messages: LLMMessageType[];
        toolDefinitions: Map<string, string>;
      }
    >(),
  );

  // Global deduplication state - track seen hashes across all spans
  const globalDeduplicationRef = useRef({
    seenHashes: new Set<string>(),
    toolDefinitions: new Map<string, string>(),
  });

  const onClickSpan = useCallback(
    (span: Span) => {
      setSelectedSpan(span);
      setViewType("trace");
    },
    [setSelectedSpan, setViewType],
  );

  const { isFetching, hasNextPage, error, fetchNextPage } = queryProps ?? {
    isFetching: false,
    hasNextPage: false,
    error: null,
    fetchNextPage: noopFn,
  };

  // Reset caches when rootSpans change
  useEffect(() => {
    processedSpansRef.current.clear();
    globalDeduplicationRef.current.seenHashes.clear();
    globalDeduplicationRef.current.toolDefinitions.clear();
    // Clear hash cache periodically to prevent memory leaks
    if (hashCache.size > 10000) {
      hashCache.clear();
    }
  }, [trace]);

  const processSpanData = useCallback((span: Span) => {
    if (processedSpansRef.current.has(span.id)) {
      return processedSpansRef.current.get(span.id)!;
    }

    if (span.data.span_attributes.type !== "llm") {
      const emptyResult = {
        messages: [],
        toolDefinitions: new Map<string, string>(),
      };
      processedSpansRef.current.set(span.id, emptyResult);
      return emptyResult;
    }

    const inputString =
      typeof span.data.input === "string" ? span.data.input : "{}";
    const { value: input } = deserializePlainStringAsJSON(inputString);
    const outputString =
      typeof span.data.output === "string" ? span.data.output : "{}";
    const { value: output } = deserializePlainStringAsJSON(outputString);

    const allMessages = [];
    const parsedInput = !!input && parseLLMSpanPart(input);
    const parsedOutput = !!output && parseLLMSpanPart(output);

    if (parsedInput) {
      allMessages.push(...parsedInput);
    }
    if (parsedOutput) {
      allMessages.push(...parsedOutput);
    }

    // Track tool definitions from assistant messages
    for (const message of allMessages) {
      if (
        message &&
        message.role === "assistant" &&
        "tool_calls" in message &&
        message.tool_calls
      ) {
        for (const toolCall of message.tool_calls) {
          if (toolCall.id && toolCall.function?.name) {
            globalDeduplicationRef.current.toolDefinitions.set(
              toolCall.id,
              toolCall.function.name,
            );
          }
        }
      }
    }

    // Deduplicate messages using global hash tracking
    const uniqueMessages = [];
    for (let index = 0; index < allMessages.length; index++) {
      const message = allMessages[index];
      if (message) {
        const messageHash = hashMessage(message, index);
        if (!globalDeduplicationRef.current.seenHashes.has(messageHash)) {
          globalDeduplicationRef.current.seenHashes.add(messageHash);
          uniqueMessages.push(message);
        }
      }
    }

    const result = {
      messages: uniqueMessages,
      toolDefinitions: new Map(globalDeduplicationRef.current.toolDefinitions),
    };

    processedSpansRef.current.set(span.id, result);
    return result;
  }, []);

  const virtualizer = useVirtualizer({
    count: hasNextPage ? threadSpans.length + 1 : threadSpans.length,
    getScrollElement: () => containerRef.current,
    estimateSize: () => 80,
    overscan: 4,
    measureElement: (element) => {
      return element?.getBoundingClientRect().height ?? 80;
    },
  });

  useInfiniteFetch({
    virtualizer,
    hasNextPage,
    fetchNextPage,
    totalRowCount: threadSpans.length,
    isFetching,
  });

  const rootError = useSpanError(trace.root);

  return (
    <>
      {(error || rootError) && (
        <div className="px-3">
          <ErrorDisplay error={error ?? rootError} />
        </div>
      )}
      {(threadSpans.length ?? 0) === 0 ? (
        <div className="px-3">
          <TableEmptyState
            className="gap-3"
            label="No conversation thread to display"
            labelClassName="text-sm font-medium"
          >
            <div className="text-xs text-primary-500">
              This trace doesn&apos;t contain any LLM or score spans
            </div>
          </TableEmptyState>
        </div>
      ) : (
        <div
          style={{
            height: `${virtualizer.getTotalSize()}px`,
            width: "100%",
            position: "relative",
          }}
        >
          {virtualizer.getVirtualItems().map((virtualItem) => {
            const isLoaderRow = virtualItem.index > threadSpans.length - 1;
            const item = threadSpans[virtualItem.index];
            if (!isLoaderRow && !item) return null;

            return (
              <div
                key={virtualItem.key}
                data-index={virtualItem.index}
                ref={(node) => virtualizer.measureElement(node)}
                style={{
                  position: "absolute",
                  top: 0,
                  left: 0,
                  width: "100%",
                  transform: `translateY(${virtualItem.start}px)`,
                }}
                className="px-3"
              >
                <ErrorBoundary
                  fallback={
                    <ErrorBanner skipErrorReporting>
                      There was a problem rendering this item
                    </ErrorBanner>
                  }
                >
                  {isLoaderRow ? (
                    <div className="flex items-center justify-center">
                      <Spinner />
                    </div>
                  ) : (
                    <LazyThreadItem
                      span={item}
                      onClickSpan={onClickSpan}
                      processSpanData={processSpanData}
                      allAvailableModelCosts={allAvailableModelCosts}
                    />
                  )}
                </ErrorBoundary>
              </div>
            );
          })}
        </div>
      )}
    </>
  );
};

const LazyThreadItem = React.memo(
  ({
    span,
    onClickSpan,
    processSpanData,
    allAvailableModelCosts,
  }: {
    span: Span;
    onClickSpan: (span: Span) => void;
    processSpanData: (span: Span) => {
      messages: LLMMessageType[];
      toolDefinitions: Map<string, string>;
    };
    allAvailableModelCosts?: Record<string, ModelCosts>;
  }) => {
    if (
      span.data.span_attributes.type !== "score" &&
      span.data.span_attributes.type !== "llm"
    ) {
      return null;
    }

    return (
      <ThreadSpanHeader
        span={span}
        spanType={span.data.span_attributes.type}
        onClickSpan={onClickSpan}
        processSpanData={processSpanData}
        allAvailableModelCosts={allAvailableModelCosts}
      />
    );
  },
);

LazyThreadItem.displayName = "LazyThreadItem";

const useSpanError = (span: Span) => {
  if (isEmpty(span?.data.error)) {
    return false;
  }

  if (isDiffObject(span?.data.error) && span?.data.error[DiffRightField]) {
    return span?.data.error[DiffRightField];
  }

  return span?.data.error;
};

const ThreadSpanHeader = ({
  span,
  spanType,
  onClickSpan,
  processSpanData,
  allAvailableModelCosts,
}: {
  span: Span;
  spanType: "llm" | "score";
  onClickSpan: (span: Span) => void;
  processSpanData: (span: Span) => {
    messages: LLMMessageType[];
    toolDefinitions: Map<string, string>;
  };
  allAvailableModelCosts?: Record<string, ModelCosts>;
}) => {
  const spanError = useSpanError(span);

  if (spanType === "score") {
    const metadataString =
      typeof span.data.metadata === "string" ? span.data.metadata : "{}";
    const { value: metadata } = deserializePlainStringAsJSON(metadataString);
    const score = Object.values(span.data.scores ?? {})[0] ?? undefined;

    return (
      <div className="border-t py-4 border-primary-100">
        <div className="mb-3 flex flex-col gap-2.5">
          <button
            onClick={() => onClickSpan(span)}
            className="text-left transition-opacity hover:opacity-80"
          >
            <SpanName
              hasError={!!spanError}
              span={span}
              className="mb-0 gap-1.5 text-sm font-medium"
              iconClassName="rounded size-4 p-0.5"
            />
          </button>
          <ThreadSpanMetrics
            span={span}
            allAvailableModelCosts={allAvailableModelCosts}
          />
        </div>
        <div className="flex flex-col gap-3 rounded-xl p-3 bg-good-50">
          {score != null ? (
            <div className="text-lg font-semibold">
              {(Number(getDiffRight(score)) * 100).toLocaleString(undefined, {
                maximumFractionDigits: 2,
              })}
              %
            </div>
          ) : (
            <div className="text-lg font-semibold">
              <NullFormatter />
            </div>
          )}
          {metadata?.choice && (
            <div>
              <div className="mb-1 text-xs text-primary-500">Choice</div>
              <div className="text-sm">{metadata.choice}</div>
            </div>
          )}
          {metadata?.rationale && (
            <div>
              <div className="mb-1 text-xs text-primary-500">Rationale</div>
              <MarkdownViewer
                className="py-0 text-sm"
                value={metadata.rationale}
              />
            </div>
          )}
        </div>
      </div>
    );
  }

  const spanData = processSpanData(span);
  if (spanData.messages.length === 0) {
    return null;
  }

  return (
    <div className="border-t py-4 border-primary-100">
      <div className="mb-4 flex flex-col gap-2.5">
        <button
          onClick={() => onClickSpan(span)}
          className="text-left transition-opacity hover:opacity-80"
        >
          <SpanName
            hasError={!!spanError}
            span={span}
            className="mb-0 text-sm font-medium"
            iconClassName="rounded size-5 p-1"
          />
        </button>
        <ThreadSpanMetrics
          span={span}
          allAvailableModelCosts={allAvailableModelCosts}
        />
      </div>
      {spanData.messages.length > 0 && (
        <div className="flex flex-col gap-2">
          {spanData.messages.map((message, index) => (
            <MessageBubble
              key={`${span.id}-${index}`}
              message={message}
              toolDefinitions={spanData.toolDefinitions}
            />
          ))}
        </div>
      )}
    </div>
  );
};

const MessageBubble = ({
  message,
  toolDefinitions,
}: {
  message: LLMMessageType | null;
  toolDefinitions: Map<string, string>;
}) => {
  if (!message) return null;

  let displayRole: string = message.role;
  let toolName: string | undefined;
  if (
    message.role === "tool" &&
    "tool_call_id" in message &&
    message.tool_call_id
  ) {
    toolName = toolDefinitions.get(message.tool_call_id);
    if (toolName) {
      displayRole = "Tool";
    }
  }

  return (
    <div
      className={cn("flex-col flex items-start mb-2 gap-1 mr-3", {
        "items-end mr-0 ml-3": message.role === "user",
      })}
    >
      <div className="text-xs capitalize text-primary-500">{displayRole}</div>
      <div
        className={cn(
          "mb-2 inline-block rounded-xl px-3 text-sm bg-primary-100 group",
          {
            "bg-accent-50": message.role === "user",
            "bg-amber-50 dark:bg-amber-950 py-2": message.role === "tool",
          },
        )}
      >
        {toolName ? (
          <CollapsibleSection
            defaultCollapsed
            className="bg-transparent hover:bg-amber-100 dark:hover:bg-amber-900"
            title={
              <div className="flex items-center gap-2 text-xs font-medium">
                <Bolt className="size-3" />
                {toolName}
              </div>
            }
          >
            <LLMMessage message={message} hideCopyButton />
          </CollapsibleSection>
        ) : (
          <LLMMessage message={message} hideCopyButton />
        )}
      </div>
    </div>
  );
};
