"use client";

import { cn } from "#/utils/classnames";
import React, {
  type RefObject,
  useCallback,
  useEffect,
  useMemo,
  useRef,
} from "react";
import { DiffNumbers } from "#/ui/diff";
import {
  type Trace,
  type PreviewSpan,
  type Span,
  type SpanMetrics,
} from "./graph";
import {
  DiffLeftField,
  type DiffObjectType,
  DiffRightField,
  flattenDiffObjects,
  isDiffObject,
} from "#/utils/diffs/diff-objects";
import { backfillSpanType, getSpanDisplayConfig } from "./span-display";
import { ChevronRight } from "lucide-react";
import { isEmpty } from "#/utils/object";
import { BasicTooltip } from "#/ui/tooltip";
import { Spinner } from "#/ui/icons/spinner";
import * as d3 from "d3";
import { formatPriceValue } from "#/ui/type-formatters/metrics";
import { SpanTypeAttribute } from "@braintrust/core";
import { useVirtualizer } from "@tanstack/react-virtual";
import { type traceCollapseState } from "./trace";
import { useTraceViewTypeState } from "#/ui/query-parameters";
import { PaginatedTraceThreadView, TraceThreadView } from "./trace-thread-view";
import { ErrorBanner } from "#/ui/error-banner";
import { ErrorBoundary } from "#/utils/error-boundary";
import { type DataObjectType } from "#/utils/btapi/btapi";
import { type ModelCosts } from "#/ui/prompts/models";

const TimelineViewHeader = ({ totalDuration }: { totalDuration?: number }) => {
  const ref = useRef<HTMLDivElement>(null);
  const svgRef = useRef<SVGSVGElement>(null);

  useEffect(() => {
    if (!totalDuration) return;
    d3.select(svgRef.current).selectAll("*").remove();

    const width = ref.current?.clientWidth ?? 100;

    const xScale = d3
      .scaleLinear()
      .domain([0, totalDuration])
      .range([0, width]);

    const xAxis = d3
      .axisTop(xScale)
      .ticks(width / 80)
      .tickSize(2000)
      .tickFormat((d) => `${xScale.tickFormat(10)(d)}s`);

    d3.select(svgRef.current)
      .attr("width", width)
      .attr("height", 30)
      .append("g")
      .attr("transform", "translate(0,2016)")
      .call(xAxis);
  }, [totalDuration]);

  if (!totalDuration) {
    return null;
  }

  return (
    <div className="flex h-7 px-3 py-2">
      <div className="w-64 flex-none" />
      <div ref={ref} className="relative flex-1">
        <svg
          ref={svgRef}
          className="absolute left-0 top-0 w-full overflow-visible text-primary-500 [&_.domain]:hidden [&_.tick>line]:text-primary-100"
        />
      </div>
    </div>
  );
};

interface FlattenedSpan {
  span: Span | PreviewSpan;
  level: number;
  index: number;
  parentCollapsed: boolean;
}

function flattenSpanTree(
  roots: (PreviewSpan | Span)[],
  collapsedIds: Set<string>,
  level = 0,
  parentCollapsed = false,
): FlattenedSpan[] {
  const result: FlattenedSpan[] = [];

  for (let i = 0; i < roots.length; i++) {
    const span = roots[i];
    const isCollapsed = collapsedIds.has(span.id);
    const shouldShow = !parentCollapsed;

    if (shouldShow) {
      result.push({
        span,
        level,
        index: i,
        parentCollapsed,
      });
    }

    if (span.children.length > 0) {
      result.push(
        ...flattenSpanTree(
          span.children,
          collapsedIds,
          level + 1,
          parentCollapsed || isCollapsed,
        ),
      );
    }
  }

  return result;
}

export function TraceTree(props: {
  trace: Trace;
  level?: number;
  seen: Set<string>;
  comparisonClassName: string;
  selectedSpan: Span | null;
  setSelectedSpan: (span: Span) => void;
  firstRootTitle?: string;
  showMetrics: boolean;
  collapseState: traceCollapseState;
  onCollapseStateChange: (state: traceCollapseState) => void;
  totalDuration?: number;
  traceStart?: number;
  containerRef: RefObject<HTMLDivElement | null>;
  threadPaginationParams?: {
    objectType: DataObjectType;
    objectId: string | undefined;
  };
  allAvailableModelCosts?: Record<string, ModelCosts>;
  hideCollapseButton?: boolean;
}) {
  const {
    trace,
    level,
    seen,
    showMetrics,
    collapseState,
    onCollapseStateChange,
    totalDuration,
    containerRef,
    threadPaginationParams,
    allAvailableModelCosts,
    hideCollapseButton,
  } = props;
  const [viewType, setViewType] = useTraceViewTypeState();
  const isTimelineView = viewType === "timeline";

  const collapsedIds = useMemo(
    () =>
      collapseState.state === "expanded"
        ? new Set<string>()
        : collapseState.ids,
    [collapseState],
  );

  const toggleCollapsed = useCallback(
    (spanId: string) => {
      const currentCollapsedIds =
        collapseState.state === "expanded"
          ? new Set<string>()
          : collapseState.ids;
      const newCollapsedIds = new Set(currentCollapsedIds);

      if (newCollapsedIds.has(spanId)) {
        newCollapsedIds.delete(spanId);
      } else {
        newCollapsedIds.add(spanId);
      }

      onCollapseStateChange({ state: "mixed", ids: newCollapsedIds });
    },
    [collapseState, onCollapseStateChange],
  );

  // Flatten the tree for virtualization
  const flattenedSpans = useMemo(() => {
    const filtered = [trace.root].filter((root) => !seen.has(root.id));
    return flattenSpanTree(filtered, collapsedIds);
  }, [trace, seen, collapsedIds]);

  const virtualizer = useVirtualizer({
    count: flattenedSpans.length,
    getScrollElement: () => containerRef.current,
    estimateSize: () => {
      return showMetrics && !isTimelineView ? 48 : 32;
    },
    overscan: 10,
  });

  useEffect(() => {
    virtualizer.measure();
  }, [showMetrics, isTimelineView, virtualizer]);

  if (viewType === "thread")
    return (
      <ErrorBoundary
        fallback={
          <ErrorBanner skipErrorReporting>
            There was a problem rendering this thread
          </ErrorBanner>
        }
      >
        {threadPaginationParams ? (
          threadPaginationParams.objectId && (
            <PaginatedTraceThreadView
              trace={trace}
              setSelectedSpan={props.setSelectedSpan}
              setViewType={setViewType}
              containerRef={containerRef}
              objectType={threadPaginationParams.objectType}
              objectId={threadPaginationParams.objectId}
              allAvailableModelCosts={allAvailableModelCosts}
            />
          )
        ) : (
          <TraceThreadView
            trace={trace}
            setSelectedSpan={props.setSelectedSpan}
            setViewType={setViewType}
            containerRef={containerRef}
            allAvailableModelCosts={allAvailableModelCosts}
          />
        )}
      </ErrorBoundary>
    );

  return (
    <div>
      {isTimelineView && !level && (
        <TimelineViewHeader totalDuration={totalDuration} />
      )}

      <div
        style={{
          height: `${virtualizer.getTotalSize()}px`,
          width: "100%",
          position: "relative",
        }}
      >
        {virtualizer.getVirtualItems().map((virtualItem) => {
          const flattenedSpan = flattenedSpans[virtualItem.index];
          if (!flattenedSpan) return null;

          return (
            <div
              key={virtualItem.key}
              style={{
                position: "absolute",
                top: 0,
                left: 0,
                width: "100%",
                height: `${virtualItem.size}px`,
                transform: `translateY(${virtualItem.start}px)`,
              }}
            >
              <TreeChild
                {...props}
                currentSpan={flattenedSpan.span}
                level={flattenedSpan.level}
                toggleCollapsed={toggleCollapsed}
                isCollapsed={collapsedIds.has(flattenedSpan.span.id)}
                disableChildrenRendering={true}
                hideCollapseButton={hideCollapseButton}
              />
            </div>
          );
        })}
      </div>
    </div>
  );
}

const TreeChild = (props: {
  currentSpan: Span | PreviewSpan;
  level?: number;
  seen: Set<string>;
  selectedSpan: Span | null;
  setSelectedSpan: (span: Span | PreviewSpan) => void;
  firstRootTitle?: string;
  showMetrics: boolean;
  collapseState: traceCollapseState;
  totalDuration?: number;
  traceStart?: number;
  comparisonClassName: string;
  toggleCollapsed: (spanId: string) => void;
  isCollapsed: boolean;
  disableChildrenRendering?: boolean;
  hideCollapseButton?: boolean;
}) => {
  const {
    currentSpan,
    selectedSpan,
    setSelectedSpan,
    firstRootTitle,
    showMetrics,
    level = 0,
    totalDuration,
    traceStart,
    comparisonClassName,
    toggleCollapsed,
    isCollapsed,
    hideCollapseButton,
  } = props;

  const [viewType, setViewType] = useTraceViewTypeState();
  const isTimelineView = viewType === "timeline";
  const hasChildren = currentSpan.children.length > 0;

  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
  const currentSpanMetrics = flattenDiffObjects(currentSpan.data).metrics as
    | {
        cached?: boolean;
        start?: number;
        end?: number;
      }
    | undefined;

  const handleToggleCollapsed = useCallback(() => {
    toggleCollapsed(currentSpan.id);
  }, [toggleCollapsed, currentSpan.id]);

  const name = currentSpan.data.span_attributes?.name || firstRootTitle;

  const hasError =
    !isEmpty(currentSpan.data.error) &&
    (!isDiffObject(currentSpan.data.error) ||
      !isEmpty(currentSpan.data.error[DiffRightField]));

  const { Icon, iconClassName, tooltip, type } = useMemo(
    () =>
      getSpanDisplayConfig({
        type:
          currentSpan.data.span_attributes?.type ??
          backfillSpanType(currentSpan.data.span_attributes?.name),
        cached: !!currentSpanMetrics?.cached,
        remote: !!currentSpan.data.span_attributes?.remote,
        hasError,
      }),
    [
      currentSpanMetrics,
      currentSpan.data.span_attributes?.name,
      currentSpan.data.span_attributes?.remote,
      currentSpan.data.span_attributes?.type,
      hasError,
    ],
  );

  const isSelected = selectedSpan && selectedSpan.id === currentSpan.id;

  const start = currentSpanMetrics?.start;
  const end = currentSpanMetrics?.end;
  const duration = !isEmpty(start) && !isEmpty(end) ? end - start : undefined;

  const offset = !isEmpty(start) && !isEmpty(traceStart) && start - traceStart;

  const offsetPercent =
    offset !== false &&
    !isEmpty(totalDuration) &&
    (offset / totalDuration) * 100 + "%";

  const durationPercent =
    !isEmpty(duration) &&
    !isEmpty(totalDuration) &&
    (duration / totalDuration) * 100 + "%";

  const iconComponent = (
    <div
      className={cn(
        "flex flex-none size-4 items-center justify-center rounded-[3px] relative",
        iconClassName,
        {
          "bg-bad-100 text-bad-500 dark:bg-bad-50 dark:text-bad-500 dark:border-bad-100 border border-bad-200":
            hasError,
        },
      )}
    >
      {isEmpty(end) && !isEmpty(start) ? (
        <Spinner className="size-[10px]" />
      ) : (
        <Icon className="size-[10px]" />
      )}
    </div>
  );

  const wasScorerSkipped =
    type === SpanTypeAttribute.SCORE &&
    !isEmpty(end) &&
    Object.keys(currentSpan.data.scores).length > 0 &&
    Object.values(currentSpan.data.scores).some((v) => v === null);
  const spanTooltip = (
    <span className="flex flex-col gap-1">
      <div className="flex items-baseline gap-2 text-sm font-medium">
        {iconComponent}
        <span className="grow">{name}</span>
        <span className="text-[9px] uppercase text-primary-500">{type}</span>
      </div>
      {tooltip}
      {wasScorerSkipped && <div>This scorer returned a null value.</div>}
      {currentSpan.data.metrics && (
        <SpanMetricsSummary
          metrics={currentSpan.data.metrics}
          comparisonClassName={comparisonClassName}
          expanded
        />
      )}
    </span>
  );

  return (
    <BasicTooltip
      side="left"
      align="start"
      tooltipContent={!isTimelineView ? spanTooltip : undefined}
    >
      <div
        className={cn(
          "pr-3 h-8 cursor-pointer flex items-center group bg-primary-50 relative rounded-md border border-background",
          {
            "pb-2 h-12": showMetrics && !isTimelineView,
            "bg-primary-200/80": isSelected,
            "hover:bg-primary-100": !isSelected,
            "border-0 cursor-default bg-transparent h-7 hover:bg-transparent":
              isTimelineView,
          },
        )}
        style={{ margin: "0 2px" }}
        onClick={() => {
          if (isTimelineView) return;
          setSelectedSpan(currentSpan);
        }}
      >
        {Boolean(durationPercent) && (
          <div
            className={cn("absolute inset-x-0 bottom-0 px-3 pb-1.5", {
              "top-0.5 left-64": isTimelineView,
            })}
          >
            <BasicTooltip
              side="top"
              align="start"
              tooltipContent={isTimelineView ? spanTooltip : undefined}
            >
              <div
                className={cn(
                  "relative border-primary-300",
                  iconClassName,
                  "border-0",
                  {
                    "bg-transparent dark:bg-transparent border-b-2":
                      !isTimelineView,
                    "h-6 rounded-[4px] border hover:opacity-80 transition-opacity cursor-pointer":
                      isTimelineView,
                  },
                )}
                style={{
                  marginLeft: offsetPercent || 0,
                  width: durationPercent || 0,
                }}
                onClick={(e) => {
                  if (!isTimelineView) return;
                  e.stopPropagation();
                  setSelectedSpan(currentSpan);
                  setViewType("trace");
                }}
              >
                <div
                  className={cn(
                    "absolute -top-0.5 h-1.5 w-full border-primary-300",
                    iconClassName,
                    "bg-transparent dark:bg-transparent border-0 border-x",
                    {
                      hidden: isTimelineView,
                    },
                  )}
                />
              </div>
            </BasicTooltip>
          </div>
        )}
        <div
          className={cn("z-10 flex items-center truncate", {
            "w-64": isTimelineView,
            "flex-1": !isTimelineView,
          })}
          style={{ paddingLeft: `calc(${level * 8}px + 12px)` }}
        >
          {!hideCollapseButton && (
            <button
              className={cn(
                "size-4 mr-1 flex items-center justify-center bg-transparent hover:border-primary-400 rounded shrink-0 border",
                {
                  "border-transparent group-hover:border-primary-200 group-hover:hover:border-primary-400":
                    !isCollapsed,
                  "opacity-0": !hasChildren,
                  hidden: !hasChildren && level === 0,
                },
              )}
              onClick={handleToggleCollapsed}
            >
              <ChevronRight
                className={cn("size-3 transition-transform", {
                  "rotate-90": !isCollapsed,
                })}
              />
            </button>
          )}
          {iconComponent}
          <span
            className={cn(
              "flex-1 truncate px-1 text-xs font-medium text-primary-700",
            )}
          >
            {name}
          </span>
        </div>
        {showMetrics && currentSpan.data.metrics && (
          <div
            className={cn("flex-none z-10 text-xs text-primary-500", {
              "pr-1.5 text-primary-800 pointer-events-none absolute right-4":
                isTimelineView,
            })}
          >
            <SpanMetricsSummary
              metrics={currentSpan.data.metrics}
              comparisonClassName={comparisonClassName}
            />
          </div>
        )}
      </div>
    </BasicTooltip>
  );
};

function roundDecimals(num: number, decimals: number) {
  return Math.round(num * 10 ** decimals) / 10 ** decimals;
}

export function SpanMetricsSummary({
  metrics,
  comparisonClassName,
  expanded,
}: {
  metrics: SpanMetrics | DiffObjectType<SpanMetrics>;
  comparisonClassName: string;
  expanded?: boolean;
}) {
  if (isDiffObject(metrics)) {
    const leftMetrics = metrics[DiffLeftField];
    const rightMetrics = metrics[DiffRightField];

    const rightTime =
      !isEmpty(rightMetrics?.start) && !isEmpty(rightMetrics?.end)
        ? rightMetrics.end - rightMetrics.start
        : 0;
    const leftTime =
      !isEmpty(leftMetrics?.start) && !isEmpty(leftMetrics?.end)
        ? leftMetrics.end - leftMetrics.start
        : rightTime;

    return (
      <div>
        <DiffNumbers
          oldNumber={roundDecimals(leftTime, 2)}
          newNumber={roundDecimals(rightTime, 2)}
          oldScoreClassName={comparisonClassName}
          formatNumber={(n) => `${n.toFixed(2)}s`}
          percentDiff={(start, end) => (end - start) / start}
          upIsGood={false}
          hideChanged
        />
      </div>
    );
  } else {
    const components: { label: string; value: string }[] = [];
    if (!isEmpty(metrics.start)) {
      if (!isEmpty(metrics.end)) {
        components.push({
          label: "Duration",
          value: `${(metrics.end - metrics.start).toFixed(2)}s`,
        });
      } else {
        components.push({ label: "Duration", value: "in progress" });
      }
    }
    if (metrics.tokens) {
      components.push({
        label: "Total tokens",
        value: `${metrics.tokens} tok`,
      });
    }
    if (metrics.time_to_first_token && expanded) {
      components.push({
        label: "Time to first token",
        value: `${metrics.time_to_first_token.toFixed(2)}s`,
      });
    }
    if (metrics.estimated_cost && expanded) {
      components.push({
        label: "Estimated LLM cost",
        value: formatPriceValue(metrics.estimated_cost),
      });
    }
    if (metrics.retries && expanded) {
      components.push({
        label: "Retries",
        value: `${metrics.retries}`,
      });
    }

    if (expanded) {
      return (
        <>
          {components.map((c) => (
            <span key={c.label} className="flex flex-col gap-0.5">
              <span className="text-primary-500">{c.label}</span>
              <span>{c.value}</span>
            </span>
          ))}
        </>
      );
    }
    return <span>{components.map((c) => c.value).join(", ")}</span>;
  }
}
