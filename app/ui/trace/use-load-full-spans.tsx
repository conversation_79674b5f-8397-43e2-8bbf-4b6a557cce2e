import {
  type QueryKey,
  useQueries,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query";
import { type PreviewTrace, type LoadedTrace } from "./graph";
import { useCallback, useContext, useEffect, useMemo } from "react";
import { type DataObjectType } from "#/utils/btapi/btapi";
import {
  dataObjectPageShape,
  fetchBtql,
  type FetchBtqlOptions,
  rowWithIdsSchema,
  useFetchBtqlOptions,
} from "#/utils/btql/btql";
import {
  type CustomColumnScope,
  //getCustomColumnScope,
} from "#/utils/custom-columns/use-custom-columns";
import {
  type BtqlQueryBuilder,
  useBtqlQueryBuilder,
} from "#/utils/btql/use-query-builder";
import * as QueryBuilder from "#/utils/btql/query-builder";
import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import { makeTraceQueryKey, parseScores } from "./use-virtual-trace";
import { stringifyObjectJSON } from "#/utils/schema";
import { type CustomColumn } from "#/utils/custom-columns/use-custom-columns";
import { type CustomColumn as CustomColumnDb } from "@braintrust/core/typespecs";
import { type SpanData, type Span, isRootSpan } from "@braintrust/local";
import { type ExpandedRowState } from "./query";
import {
  type ChannelSpec,
  MetricsField,
  OriginField,
  ScoresField,
  SpanAttributesField,
  TagsField,
} from "#/utils/duckdb";
import { useRealtimeChannel } from "#/utils/simple-channel";
import { combineResults } from "#/utils/react-query";

export function useLoadFullSpans({
  trace,
  spanRowId,
  objectType,
  objectId,
  customColumns,
}: {
  trace: PreviewTrace | null;
  spanRowId: string | null;
  objectType: DataObjectType;
  objectId: string;
  customColumns?: CustomColumn[] | CustomColumnDb[];
}) {
  const { projectId } = useContext(ProjectContext);

  const builder = useBtqlQueryBuilder({});
  const btqlOptions = useFetchBtqlOptions();
  const queryClient = useQueryClient();

  const isRoot = !!trace?.root.id && trace.root.id === spanRowId;
  const { data, error, isError, isLoading, isFetching, isPending } = useQuery({
    queryKey: makeFullSpanQueryKey(
      objectType,
      objectId,
      projectId,
      spanRowId,
      isRoot,
    ),
    queryFn: makeFullSpanQueryFn(builder, btqlOptions),
    enabled: !!spanRowId,
    staleTime: Infinity,
  });

  const { loadedTrace, loadedSpan } = useMemo<{
    loadedTrace: LoadedTrace | null;
    loadedSpan: Span | null;
  }>(() => {
    if (!trace) {
      return { loadedTrace: null, loadedSpan: null };
    }

    let loadedRootSpan: Span | undefined;
    let loadedSpan: Span | null = null;
    const loadedSpans: Record<string, Span> = Object.fromEntries(
      Object.entries(trace.spans).map(([spanId, previewSpan]) => {
        const isRoot = isRootSpan(previewSpan);
        const isLoadedSpan = data?.data?.id === previewSpan.id;
        const queryData = isLoadedSpan
          ? data
          : queryClient.getQueryData<{
              data: Record<string, unknown>;
              customColumnsData: Record<string, unknown> | null | undefined;
            }>(
              makeFullSpanQueryKey(
                objectType,
                objectId,
                projectId,
                previewSpan.id,
                isRoot,
              ),
            );

        const fullData = queryData?.data
          ? isRootSpan(previewSpan)
            ? withCustomColumnsData({
                row: queryData.data,
                customColumnsData: queryData.customColumnsData,
                customColumns,
              })
            : queryData.data
          : undefined;
        const fullSpan = {
          ...previewSpan,
          scores: parseScores({ span: previewSpan, scores: fullData?.scores }),
          data: {
            ...previewSpan.data,
            ...fullData,
          },
        };
        if (isRoot) {
          loadedRootSpan = fullSpan;
        }

        if (isLoadedSpan) {
          loadedSpan = fullSpan;
        }
        return [spanId, fullSpan];
      }),
    );

    return {
      loadedTrace: {
        ...trace,
        root: {
          ...trace.root,
          ...loadedRootSpan,
          data: {
            ...trace.root.data,
            ...loadedRootSpan?.data,
          },
        },
        spans: loadedSpans,
      },
      loadedSpan,
    };
  }, [
    trace,
    queryClient,
    objectType,
    objectId,
    projectId,
    data,
    customColumns,
  ]);

  const expandedRowState: ExpandedRowState = useMemo(() => {
    if (isError) {
      return "fully_loaded";
    }
    if (isLoading) {
      return "initially_loading";
    }
    if (isFetching) {
      return "reloading_row";
    }
    return "fully_loaded";
  }, [isError, isLoading, isFetching]);

  const realtimeSpec: ChannelSpec | null = useMemo(
    () =>
      objectId
        ? {
            objectType,
            id: objectId,
            audit_log: false,
            shape: "traces",
          }
        : null,
    [objectId, objectType],
  );

  const onEvent = useCallback(
    (event: unknown) => {
      const row = rowWithIdsSchema.safeParse(event);
      if (!row.success || trace?.root.root_span_id !== row.data.root_span_id) {
        return;
      }
      // invalidate the entire tree for now - makes things a lot easier
      // since there no observers for fully loaded spans that aren't currently selected
      // react-query may clean up that data
      queryClient.invalidateQueries({
        queryKey: makeTraceQueryKey(objectType, objectId, trace.root.id),
      });
      queryClient.invalidateQueries({
        queryKey: makeFullSpanQueryKey(
          objectType,
          objectId,
          projectId,
          row.data.id,
          true,
        ),
      });

      if (row.data._object_delete) {
        // refresh of the tree should take care of deletes
      } else {
        const query = queryClient.getQueryCache().find({
          queryKey: makeFullSpanQueryKey(
            objectType,
            objectId,
            projectId,
            row.data.id,
          ),
        });

        // in threads view and search view we have disabled useQueries calls
        // which are listening to any realtime updates
        // Since they are disabled, they won't get refetched via refetchQueries
        // unless we explicitly refetch them with staleTime 0
        // so manually check for refetching based on if the observers exist
        if (query && query.getObserversCount() > 0) {
          queryClient.prefetchQuery({
            queryKey: [
              "fullSpan",
              objectType,
              objectId,
              projectId,
              row.data.id,
            ],
            queryFn: makeFullSpanQueryFn(builder, btqlOptions),
            staleTime: 0,
          });
        }
      }
    },
    [
      queryClient,
      objectType,
      objectId,
      projectId,
      trace?.root.id,
      trace?.root.root_span_id,
      builder,
      btqlOptions,
    ],
  );
  useRealtimeChannel({
    spec: realtimeSpec,
    onEvent,
  });

  return {
    loadingError: error,
    traceWithLoadedSpans: loadedTrace,
    expandedRowState,
    loadedSpan,
    isPending,
  };
}

export function makeFullSpanQueryKey(
  objectType: DataObjectType | undefined,
  objectId: string | undefined,
  projectId: string | null,
  rowId: string | null,
  isRoot?: boolean,
) {
  return [
    "fullSpan",
    objectType,
    objectId,
    projectId,
    rowId,
    ...(isRoot ? [true] : []),
  ];
}

export type QueriedSpanData = {
  data: SpanData | null;
  customColumnsData: Record<string, unknown> | null | undefined;
};

export function makeFullSpanQueryFn(
  builder: BtqlQueryBuilder,
  btqlOptions: FetchBtqlOptions,
) {
  return async function ({
    queryKey,
    signal,
  }: {
    queryKey: QueryKey;
    signal: AbortSignal;
  }) {
    const [_queryName, _objectType, _objectId, _projectId, _rowId] = queryKey;
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    const objectType = _objectType as DataObjectType;
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    const objectId = _objectId as string;
    /*
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    const projectId = _projectId as string;
    */
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    const rowId = _rowId as string;
    /*
    const customColumnScope = getCustomColumnScope({
      objectType,
      objectId,
      projectId,
    });
    */
    const [btqlResp, customColumnsData] = await Promise.all([
      fetchBtql({
        args: {
          query: {
            filter: {
              op: "eq",
              left: { btql: "id" },
              right: { op: "literal", value: rowId },
            },
            from: builder.from(objectType, objectId ? [objectId] : [], "spans"),
            select: [{ op: "star" }],
          },
          brainstoreRealtime: true,
          useColumnstore: false,
        },
        ...btqlOptions,
        signal,
      }),
      /*
      isRoot
        ? fetchCustomColumns({
            rowId,
            objectType,
            objectId,
            btqlOptions,
            signal,
            customColumnScope,
          })
        : Promise.resolve(null),
        */
      Promise.resolve(null),
    ]);

    return {
      data: processFullSpanData(objectType, rowId, btqlResp.data),
      customColumnsData,
    };
  };
}

// used to handle some backwards compatibility with the older duckdb shape
export function processFullSpanData(
  objectType: DataObjectType,
  rowId: string,
  data: Record<string, unknown>[],
): SpanData | null {
  // In older data planes that do not have Brainstore, the "spans" shape is not supported
  // and will just return the full trace. So we should filter the results here
  const row = data.find((d) => d.id === rowId);
  if (!row) {
    return null;
  }

  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
  return stringifyObjectJSON(
    objectType,
    { ...row, [TagsField]: row[TagsField] ?? [] },
    [
      // match SpanData type
      ScoresField,
      MetricsField,
      SpanAttributesField,
      TagsField,
      OriginField,
    ],
  ) as SpanData;
}

type CustomColumnsParams = {
  rowId: string;
  objectType: DataObjectType;
  objectId: string | null;
  btqlOptions: FetchBtqlOptions;
  signal: AbortSignal;
  useSummaryShape?: boolean;
  customColumnScope: CustomColumnScope;
};

// temp export
export async function fetchCustomColumns({
  rowId,
  objectType,
  objectId,
  btqlOptions,
  signal,
  customColumnScope,
}: CustomColumnsParams) {
  const shape = dataObjectPageShape(objectType);
  const useSummaryShape = shape === "summary";
  const res = await fetchBtql({
    args: {
      query: {
        filter: {
          op: "eq",
          left: { btql: "id" },
          right: { op: "literal", value: rowId },
        },
        from: QueryBuilder.from(
          objectType,
          objectId ? [objectId] : [],
          useSummaryShape ? "summary" : undefined,
        ),
        select: useSummaryShape
          ? [{ op: "star" }]
          : [
              {
                alias: "span_id",
                expr: {
                  btql: "span_id",
                },
              },
              {
                alias: "root_span_id",
                expr: {
                  btql: "root_span_id",
                },
              },
              ...(objectType === "dataset"
                ? []
                : [
                    {
                      alias: "span_parents",
                      expr: {
                        btql: "span_parents",
                      },
                    },
                  ]),
            ],
        ...(shape === "summary" ? { preview_length: -1 } : {}),
      },
      customColumnScope,
      brainstoreRealtime: true,
      useColumnstore: false,
    },
    ...btqlOptions,
    signal,
  });

  // In older data planes that do not have Brainstore, the "spans" shape is not supported
  // and will just return the full trace. So we should filter the results here
  return res.data.find((r) => isRootSpan(r));
}

function withCustomColumnsData({
  row,
  customColumnsData,
  customColumns,
}: {
  row: Record<string, unknown> | undefined;
  customColumnsData: Record<string, unknown> | null | undefined;
  customColumns?: CustomColumn[] | CustomColumnDb[];
}): Record<string, unknown> | null {
  if (!row) {
    return null;
  }
  return {
    ...row,
    ...Object.fromEntries(
      (customColumns ?? []).map((c) => [c.name, customColumnsData?.[c.name]]),
    ),
  };
}

// this function will watch for realtime updates to spans and return the latest data
export function useRealtimeSpans({
  idsToWatch,
  objectType,
  objectId,
  queriedSpanData,
}: {
  idsToWatch: string[];
  objectType: DataObjectType | undefined;
  objectId: string | undefined;
  queriedSpanData?: QueriedSpanData[];
}) {
  const btqlOptions = useFetchBtqlOptions();
  const builder = useBtqlQueryBuilder({});
  const { projectId } = useContext(ProjectContext);

  const available = objectType && objectId && projectId;
  const queryClient = useQueryClient();
  useEffect(() => {
    if (!available) {
      return;
    }
    queriedSpanData?.forEach((data) => {
      if (!data.data?.id) {
        return;
      }
      const queryKey = makeFullSpanQueryKey(
        objectType,
        objectId!,
        projectId,
        data.data.id,
      );
      const existing = queryClient.getQueryData<QueriedSpanData>(queryKey);
      if (
        !existing ||
        !existing.data?._xact_id ||
        existing.data._xact_id < data.data._xact_id
      ) {
        queryClient.setQueryData(queryKey, data);
      }
    });
  }, [
    available,
    queryClient,
    queriedSpanData,
    objectType,
    objectId,
    projectId,
  ]);

  // somewhat of a hack to get data out of the cache in a reactive way
  const { data: cachedData } = useQueries({
    queries: idsToWatch.map((id) => ({
      queryKey: makeFullSpanQueryKey(objectType, objectId, projectId, id),
      // for typing
      queryFn: makeFullSpanQueryFn(builder, btqlOptions),
      enabled: false,
    })),
    combine: combineResults,
  });

  // somewhat of another hack to ensure multiple callsites cleanup only
  // when the last callsite unmounts
  useQuery({
    queryKey: ["fullSpan", objectType, objectId, projectId],
    queryFn: () => null,
    staleTime: Infinity,
  });

  useEffect(() => {
    const queryKey = ["fullSpan", objectType, objectId, projectId];
    // since disabled queries will still garbage collect cached data,
    // disable garbage collection for realtime updates while we still have observers for this query key
    queryClient.setQueryDefaults(queryKey, {
      gcTime: Infinity,
      staleTime: Infinity,
    });

    return () => {
      const query = queryClient.getQueryCache().find({
        queryKey,
      });

      if (query?.getObserversCount() === 0) {
        queryClient.setQueryDefaults(queryKey, { gcTime: undefined });
        //queryClient.removeQueries({ queryKey });
      }
    };
  }, [queryClient, objectType, objectId, projectId]);

  return cachedData;
}
