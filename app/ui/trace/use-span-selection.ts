import {
  type SetStateAction,
  type TransitionStartFunction,
  useCallback,
  useEffect,
  useRef,
  useState,
} from "react";
import {
  useActiveCommentId,
  useActiveRowAndSpan,
  useScrollTo,
} from "#/ui/query-parameters";
import { type LoadedTrace, type Span, type Trace } from "./graph";
import { type MultiTraceContext } from "#/ui/copilot/trace";

type Args = {
  copilotContext: MultiTraceContext;
  startSpanChangeTransition: TransitionStartFunction;
  trace: Trace | null;
  loadedTrace: LoadedTrace | null;
  comparisonTrace?: Trace | null;
};

export default function useSpanSelection({
  copilotContext,
  startSpanChangeTransition,
  trace,
  loadedTrace,
  comparisonTrace,
}: Args) {
  const [{ s: activeSpanId }, setActiveRowAndSpan] = useActiveRowAndSpan();
  const [scrollToSpanId, setScrollToSpanId] = useState<string | null>(null);
  const [scrollTo, _setScrollTo] = useScrollTo();
  const setScrollTo = useCallback(
    (args: { spanId: string; v: SetStateAction<string | null> } | null) => {
      if (args == null) {
        setScrollToSpanId(null);
        _setScrollTo(null);
        return;
      }
      const { spanId, v } = args;
      setScrollToSpanId(spanId);
      _setScrollTo(v);
    },
    [setScrollToSpanId, _setScrollTo],
  );
  const [_, setFocusedComment] = useActiveCommentId();

  const lastTrace = useRef<LoadedTrace | null>(null);
  const lastSpanPath = useRef<number[]>([]);

  const setSelectedSpan = useCallback(
    (span: Span) => {
      startSpanChangeTransition(() => {
        lastTrace.current = loadedTrace;
        if (span.span_id !== activeSpanId) {
          setActiveRowAndSpan({ s: span.span_id });
          setFocusedComment(null);
          setScrollTo(null);
        }

        let spanPath = [];
        let curr = span;
        while (
          loadedTrace &&
          curr.parent_span_id &&
          loadedTrace.spans[curr.parent_span_id]
        ) {
          const idx = loadedTrace.spans[curr.parent_span_id].children.findIndex(
            (s) => s.span_id === curr.span_id,
          );
          if (idx >= 0) {
            spanPath.push(idx);
          } else {
            // If the element isn't found, just reset the path and don't apply this heuristic
            spanPath = [];
            break;
          }
          curr = loadedTrace.spans[curr.parent_span_id];
        }
        spanPath.reverse();
        lastSpanPath.current = spanPath;
        copilotContext.visitSpan(span);
      });
    },
    [
      activeSpanId,
      setActiveRowAndSpan,
      setScrollTo,
      loadedTrace,
      copilotContext,
      startSpanChangeTransition,
      setFocusedComment,
    ],
  );

  const hasValidActiveSpan =
    activeSpanId &&
    (trace?.spans[activeSpanId] || comparisonTrace?.spans[activeSpanId]);
  useEffect(() => {
    if (!trace || hasValidActiveSpan) {
      // If the trace is not loaded, or the active span is already in the trace, do nothing
      return;
    }

    if (lastSpanPath.current?.length > 0) {
      let curr = trace.root;
      for (const idx of lastSpanPath.current) {
        if (!curr.children[idx]) {
          break;
        }
        curr = curr.children[idx];
      }
      setActiveRowAndSpan({ s: curr.span_id });
    } else {
      setActiveRowAndSpan({ s: trace.root.span_id });
    }
  }, [activeSpanId, setActiveRowAndSpan, trace, hasValidActiveSpan]);

  return {
    activeSpanId,
    setSelectedSpan,
    scrollTo,
    scrollToSpanId,
    setScrollTo,
  };
}
