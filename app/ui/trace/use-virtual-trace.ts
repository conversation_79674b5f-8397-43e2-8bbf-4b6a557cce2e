import { type DataObjectType } from "#/utils/btapi/btapi";
import {
  fetchBtql,
  fetchBtqlPaginated,
  useFetchBtqlOptions,
} from "#/utils/btql/btql";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useBtqlQueryBuilder } from "#/utils/btql/use-query-builder";
import { type AliasExpr } from "@braintrust/btql/parser";
import { z } from "zod";
import { useMemo } from "react";
import {
  type Span,
  spanAttributesSchema,
  spanMetricNames,
  type SpanMetrics,
} from "@braintrust/local";
import {
  fillSpanScores,
  parseScoreData,
  type PreviewSpan,
  sortSpanChildren,
  topSortSpans,
  type PreviewTrace,
} from "./graph";
import { objectReferenceSchema } from "@braintrust/core/typespecs";

/**
 *  {
 *    end?: number | null;
 *    start?: number | null;
 *    ...
 *    retries?: number | null;
 *    [k: string]: number | null | undefined;   // fallback
 *  }
 */
export const metricsSchema = z
  .object(
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    Object.fromEntries(
      spanMetricNames.map((m) => [m, z.number().nullish()]),
    ) as unknown as {
      [K in (typeof spanMetricNames)[number]]: z.ZodType<
        number | null | undefined
      >;
    },
  )
  .catchall(z.number().nullable());

const rootSpanIdSchema = z.object({
  root_span_id: z.string(),
});
export const baseSpanOverviewSchema = z.object({
  id: z.string(),
  _xact_id: z.string(),
  _pagination_key: z.string().nullish(),
  created: z
    .string()
    .datetime()
    // This is for Clickhouse's demented datetime format
    .or(z.string().regex(/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/)),
  root_span_id: z.string(),
  span_id: z.string(),
  tags: z.array(z.string()).nullish(),
  origin: objectReferenceSchema.nullish(),
});
export const spanOverviewSchema = baseSpanOverviewSchema.extend({
  span_parents: z.array(z.string()).nullish(),
  error: z.unknown().nullish(),
  metrics: metricsSchema.nullish(),
  scores: z.record(z.number().nullable()).nullish(),
  span_attributes: spanAttributesSchema.nullish(),
});
export type SpanOverviewRow = z.infer<typeof spanOverviewSchema>;

export function useVirtualTrace({
  rowId,
  objectId,
  objectType,
  isFastSummaryEnabled,
  allowEmpty,
}: {
  rowId: string | null;
  objectId: string | null;
  objectType: DataObjectType;
  allowEmpty?: boolean;
  isFastSummaryEnabled?: boolean;
}): {
  trace: PreviewTrace | null;
  comparisonKey: string;
  hasLoaded: boolean | undefined;
} {
  const builder = useBtqlQueryBuilder({});
  const btqlOptions = useFetchBtqlOptions();

  const queryClient = useQueryClient();
  const isQueryEnabled = !!rowId && !!objectId;

  // Fetch the full list of spans in a trace up front. Eventually, we might want to virtualize
  // this too, but for now, we assume that it's fast enough.
  const { data: spanTreeData, isLoading } = useQuery(
    {
      queryKey: makeTraceQueryKey(objectType, objectId, rowId),
      enabled: isQueryEnabled,
      queryFn: async ({ signal }: { signal: AbortSignal }) => {
        const schema =
          objectType === "dataset"
            ? baseSpanOverviewSchema
            : spanOverviewSchema;

        const rootSpanIdQuery = await fetchBtql({
          args: {
            query: {
              filter: {
                op: "eq",
                left: { btql: "id" },
                right: { op: "literal", value: rowId },
              },
              from: builder.from(
                objectType,
                objectId ? [objectId] : [],
                "spans",
              ),
              select: [
                {
                  alias: "root_span_id",
                  expr: {
                    btql: "root_span_id",
                  },
                },
              ],
              limit: 1,
            },
            brainstoreRealtime: true,
          },
          ...btqlOptions,
          schema: rootSpanIdSchema,
          signal,
        });
        const rootSpanId = rootSpanIdQuery.data[0]?.root_span_id;
        if (!rootSpanId) {
          return null;
        }
        return await fetchBtqlPaginated({
          args: {
            query: {
              filter: {
                op: "eq",
                left: { btql: "root_span_id" },
                right: {
                  op: "literal",
                  value: rootSpanId,
                },
              },
              from: builder.from(
                objectType,
                objectId ? [objectId] : [],
                "spans",
              ),
              select: Object.keys(schema.shape).flatMap(
                (name): AliasExpr => ({
                  alias: name,
                  expr: builder.ident(name),
                }),
              ),
            },
            brainstoreRealtime: true,
          },
          ...btqlOptions,
          schema: spanOverviewSchema,
          signal,
        });
      },
      throwOnError: false,
      staleTime: Infinity,
    },
    queryClient,
  );

  const trace = useMemo(() => {
    if (!isQueryEnabled && allowEmpty) {
      return null;
    }
    return buildVirtualTrace(spanTreeData?.data ?? []);
  }, [spanTreeData, isQueryEnabled, allowEmpty]);

  return {
    trace,
    comparisonKey: "", // XXX TODO
    hasLoaded: !isLoading,
  };
}

export function buildVirtualTrace(
  rows: SpanOverviewRow[],
): PreviewTrace | null {
  if (rows.length === 0) {
    return null;
  }

  const spans: Record<string, PreviewSpan> = {};
  let root: PreviewSpan | null = null;

  for (const row of rows) {
    const span = makePreviewSpanFromRow(row);
    spans[row.span_id] = span;

    if (span.span_parents?.length === 0) {
      console.assert(root === null, "root already set");
      root = span;
    }
  }

  if (!root) {
    throw new Error("root span not found");
  }

  for (const row of rows) {
    if (root.root_span_id !== row.root_span_id) {
      // This should not be possible because the caller of buildTrace should be
      // filtering for rows that match one of the root span rows, so the root
      // span row itself must exist.
      throw new Error(
        `root span id ${row.root_span_id} for span ${row.span_id} does not match root span ${root.root_span_id}. All span ids: ${JSON.stringify(Object.keys(spans), null, 2)}`,
      );
    }
  }

  const sortedSpans = topSortSpans(spans);
  const topSortIndex: Map<string, number> = new Map();
  for (const [idx, span] of sortedSpans.entries()) {
    topSortIndex.set(span.span_id, idx);
  }

  for (const row of rows) {
    const span = spans[row.span_id];
    const span_parents = span.span_parents ?? [];
    if (span_parents.length > 0) {
      // Even though a span can technically have multiple parents, this
      // functionality is not used anywhere so we only handle the case of a
      // single parent.
      if (span_parents.length > 1) {
        console.warn("Only using first parent of span", row.span_id);
      }

      if (
        topSortIndex.get(span_parents[0])! > topSortIndex.get(span.span_id)!
      ) {
        console.warn(
          `Cycle detected in span graph at span ${span.span_id}. Skipping its parent.`,
        );
        continue;
      }

      // If we find the actual parent span, use it and set `span.parent_span_id`
      // to point to it. Otherwise, we have an orphan span which we put
      // underneath the root_span for display purposes, but we don't set
      // `span.parent_span_id` to indicate that it's an orphan.
      const parentSpan = (() => {
        const parent_span_id = span_parents[0];
        if (parent_span_id in spans) {
          const parentSpan = spans[parent_span_id];
          span.parent_span_id = parentSpan.span_id;
          return parentSpan;
        } else {
          console.warn(
            `parent span ${parent_span_id} not found for span ${span.span_id}. Using root span ${root.span_id} instead`,
          );
          return root;
        }
      })();
      parentSpan.children.push(span);
    }
  }

  const colored: Record<string, boolean> = {};
  for (const span of Object.values(spans)) {
    fillSpanScores(span, colored);
  }

  // For each span, sort its children by start time
  for (const span of Object.values(spans)) {
    sortSpanChildren(span);
  }

  if (!root) {
    throw new Error("root span not found");
  }

  return { root, spans };
}

export function makeTraceQueryKey(
  objectType: DataObjectType,
  objectId: string | null,
  traceRowId: string | null,
) {
  return ["virtualTraceSpanTree", objectType, objectId, traceRowId];
}

export function makePreviewSpanFromRow(row: SpanOverviewRow): PreviewSpan {
  return {
    id: row.id,
    span_id: row.span_id,
    root_span_id: row.root_span_id,
    span_parents: row.span_parents ?? [],
    parent_span_id: null,
    scores: parseScores({
      span: { span_id: row.span_id },
      scores: row?.scores,
    }),
    data: {
      ...row,
      span_attributes: row.span_attributes ?? { name: "" },
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
      metrics: row.metrics as
        | (SpanMetrics & Record<string, number>)
        | undefined,
      scores: row.scores ?? {},
      tags: row.tags ?? [],
      error: row.error ?? undefined,
      origin: row.origin ?? undefined,
    },
    children: [],
  };
}

const rawScoreSchema = z.record(z.number().nullable());

export function parseScores({
  span,
  scores,
}: {
  span: { span_id: string; scores?: Span["scores"] };
  scores: unknown;
}) {
  const scoresObj =
    typeof scores === "string" ? JSON.parse(scores || "null") : scores;
  const parsedScores = rawScoreSchema.safeParse(scoresObj);
  const newScores = parsedScores.success
    ? parseScoreData(span.span_id, parsedScores.data)
    : undefined;

  // Keep the old scores because some of the span's scores were computed via child spans
  // while building the trace
  return { ...span.scores, ...newScores };
}
