import { NULL_DASH } from "#/ui/table/formatters/null-formatter";

function capitalize(word: string) {
  if (["llm"].includes(word.toLowerCase())) {
    return word.toUpperCase();
  } else {
    return word.charAt(0).toUpperCase() + word.slice(1);
  }
}

export function transformMetricName(name: string) {
  if (name === "tokens") {
    return "Total tokens";
  } else {
    // Replace _ with space and capitalize first letter
    return (
      name
        .replace(/_/g, " ")
        .split(" ")
        // Sentence case
        .map((s, i) => (i === 0 ? capitalize(s) : s))
        .join(" ")
    );
  }
}

export function formatPriceValue(value: number | null) {
  if (value == null) {
    return NULL_DASH;
  }
  const absValue = Math.abs(value);
  return absValue < 0.001
    ? "<$0.001"
    : `${value < 0 ? "-" : ""}$${absValue.toLocaleString(undefined, {
        minimumFractionDigits: 2,
        maximumFractionDigits: value > 1 ? 2 : 3,
      })}`;
}
