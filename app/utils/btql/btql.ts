import { type ParsedQuery } from "@braintrust/btql/parser";
import * as Sentry from "@sentry/nextjs";
import {
  type ResponseSchema,
  responseSchemaSchema,
  weakestScalarType,
} from "@braintrust/btql/binder";
import {
  jsonSchemaObjectSchema,
  type LogicalSchema,
  mergeSchemas,
  parseLogicalSchema,
} from "@braintrust/btql/schema";
import { useAPIVersion } from "#/ui/api-version/check-api-version";
import * as semver from "semver";
import { useCallback, useMemo, useState } from "react";
import { type DataObjectType, useArrowAPI } from "#/utils/btapi/btapi";
import {
  Field as ArrowField,
  Schema as ArrowSchema,
  type DataType as ArrowDataType,
  Bool as ArrowBool,
  Int as ArrowInt,
  Float as ArrowFloat,
  Utf8 as ArrowUtf8,
  Precision,
  List as ArrowList,
  Struct as ArrowStruct,
  Timestamp as ArrowTimestamp,
  Map_ as ArrowMap,
  TimeUnit as ArrowTimeUnit,
} from "apache-arrow";
import { z } from "zod";
import { isArray, isObject } from "#/utils/object";
import { BTQL_API_VERSION, MINIMUM_BTQL_API_VERSION } from "./constants";
import { type FeatureFlags, useFeatureFlags } from "#/lib/feature-flags";
import { apiFetchBinary, apiFetchJson, extractPlan } from "#/utils/btapi/fetch";
import {
  useSessionToken,
  type FetchCachedBtSessionTokenFn,
} from "#/utils/auth/session-token";
import { type AclObjectType } from "@braintrust/core/typespecs";
import { sha1 } from "#/utils/hash";
import { type AsyncDuckDB } from "@duckdb/duckdb-wasm";
import { useOrg } from "#/utils/user";
import { realtimeStateSchema } from "@braintrust/local/app-schema";
import { xactToSmartTime } from "#/app/app/[org]/p/[project]/brainstore/[object]/brainstore-object-configuration";
import { redactBtqlQuery } from "@braintrust/local/api-schema";

export function parseBtqlSchema(
  _row: unknown,
  logicalSchema: ResponseSchema,
): ArrowSchema {
  // For now, we can keep the parsing logic very simple, because we
  // only expect non-object types in the schema
  return new ArrowSchema(
    Object.entries(logicalSchema.items.properties).map(([key, value]) => {
      let type: ArrowDataType = new ArrowUtf8();
      try {
        type = jsonTypeToArrowType(value);
      } catch {}
      return ArrowField.new({
        name: key,
        type,
      });
    }),
  );
}

// TODO: Maybe we could even share this type with the backend.
export const responseSchema = z.object({
  data: z.array(z.record(z.unknown())),
  schema: responseSchemaSchema,
  cursor: z.string().nullish(),
});
export type BTQLResponse<T> = {
  data: T[];
  schema: ResponseSchema;
  cursor: string | undefined;
};

export interface BtqlQueryArgs {
  query: string | ParsedQuery | null;
  useLocalTZ?: boolean;
  api_version?: number;
  disableLimit?: boolean;
  forcePushLimit?: boolean;
  expensive?: boolean;
  minimumApiVersion?: string;
  inferenceDepth?: number;
  brainstoreIncludeUnbackfilled?: boolean;
  brainstoreRealtime: boolean;
  useColumnstore?: boolean;
  customColumnScope?: {
    objectType: AclObjectType;
    objectId: string | null;
    subtype?: AclObjectType;
  };
}

function makeBtqlParams(
  args: Pick<
    BtqlQueryArgs,
    | "query"
    | "minimumApiVersion"
    | "brainstoreIncludeUnbackfilled"
    | "brainstoreRealtime"
    | "expensive"
    | "inferenceDepth"
    | "useColumnstore"
    | "useLocalTZ"
    | "api_version"
    | "disableLimit"
    | "forcePushLimit"
    | "customColumnScope"
  >,
  flags: FeatureFlags,
) {
  const { query, minimumApiVersion: _, customColumnScope, ...rest } = args;
  const opts = {
    useLocalTZ: true,
    api_version: BTQL_API_VERSION,
    disableLimit: false,
    forcePushLimit: false,
    useColumnstore: true,
    ...rest,
  };

  const {
    brainstore,
    brainstore_realtime,
    brainstore_skip_backfill_check,
    queryDiagnostics,
  } = flags;

  const includeUnbackfilled =
    brainstore_skip_backfill_check || opts.brainstoreIncludeUnbackfilled;

  return {
    query,
    use_columnstore: opts.useColumnstore, // so that we fall back to it if brainstore is not available
    use_brainstore: brainstore,
    brainstore_skip_backfill_check: includeUnbackfilled,
    brainstore_realtime:
      brainstore_realtime && opts.brainstoreRealtime && !includeUnbackfilled,
    tz_offset: opts.useLocalTZ ? new Date().getTimezoneOffset() : undefined,
    api_version: opts.api_version,
    disable_limit: opts.disableLimit,
    force_push_limit: opts.forcePushLimit,
    expected_cost: opts.expensive ? 10 : undefined,
    inference_depth: opts.inferenceDepth,
    include_plan: queryDiagnostics,
    fmt: "json",
    ...(customColumnScope
      ? {
          custom_column_scope: {
            object_type: customColumnScope.objectType,
            object_id: customColumnScope.objectId,
            subtype: customColumnScope.subtype,
          },
        }
      : {}),
  };
}

export function useBtql(args: BtqlQueryArgs & { name: string }) {
  const { version: apiVersion } = useAPIVersion();

  const { flags } = useFeatureFlags();

  const {
    name,
    query,
    minimumApiVersion,
    brainstoreIncludeUnbackfilled,
    brainstoreRealtime,
    expensive,
    inferenceDepth,
    useColumnstore,
    useLocalTZ,
  } = args;

  const params = useMemo(
    () =>
      makeBtqlParams(
        {
          query,
          minimumApiVersion,
          brainstoreIncludeUnbackfilled,
          brainstoreRealtime,
          expensive,
          inferenceDepth,
          useColumnstore,
          useLocalTZ,
        },
        flags,
      ),
    [
      query,
      minimumApiVersion,
      brainstoreIncludeUnbackfilled,
      brainstoreRealtime,
      expensive,
      inferenceDepth,
      useColumnstore,
      useLocalTZ,
      flags,
    ],
  );

  const [btqlSchema, setBtqlSchema] = useState<ResponseSchema | undefined>(
    undefined,
  );
  const captureAndParseBtqlSchema = useCallback(
    (row: unknown, logicalSchema: ResponseSchema) => {
      setBtqlSchema(logicalSchema);
      return parseBtqlSchema(row, logicalSchema);
    },
    [],
  );

  const apiTooOld = useMemo(() => {
    if (!apiVersion) {
      return null;
    }
    return semver.lt(
      apiVersion,
      args.minimumApiVersion ?? MINIMUM_BTQL_API_VERSION,
    );
  }, [apiVersion, args.minimumApiVersion]);

  const ret = useArrowAPI({
    name,
    endpoint: !apiTooOld && args.query !== null ? "btql" : null,
    params,
    schema: captureAndParseBtqlSchema,
    parseData: responseSchema.parse,
  });

  const invalidRequest = useMemo(
    () =>
      (ret.error && `${ret.error}`.includes("Invalid request")) ||
      (ret.error && `${ret.error}`.includes("costly")),
    [ret.error],
  );

  return {
    apiTooOld,
    unsupported: apiTooOld || invalidRequest,
    // Do not return an error if the API is too old, because these often propagate
    // to the user. Instead, let the caller of this endpoint check `apiTooOld` and
    // fall back as appropriate.
    error: apiTooOld === null || apiTooOld ? undefined : ret.error,
    data: apiTooOld === null ? undefined : ret.data,
    schema: apiTooOld === null ? undefined : btqlSchema,
    loading: apiTooOld === null ? true : ret.loading,
  };
}

function jsonTypeToArrowType(type: LogicalSchema): ArrowDataType {
  const scalarType = weakestScalarType(type);
  switch (scalarType) {
    case "array":
      if (!type.items || !isObject(type.items) || isArray(type.items)) {
        throw new Error("Array type must have items");
      }
      return new ArrowList(
        new ArrowField("item", jsonTypeToArrowType(type.items)),
      );
    case "object":
      if (type.properties) {
        return new ArrowStruct(
          Object.entries(type.properties).map(([key, value]) =>
            ArrowField.new({
              name: key,
              type: jsonTypeToArrowType(parseLogicalSchema(value)),
            }),
          ),
        );
      } else if (
        type.additionalProperties &&
        isObject(type.additionalProperties)
      ) {
        const targetType = jsonTypeToArrowType(type.additionalProperties);
        return new ArrowMap(
          ArrowField.new({
            name: "entries",
            // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
            type: new ArrowStruct<any>([
              ArrowField.new({
                name: "key",
                type: new ArrowUtf8(),
              }),
              ArrowField.new({
                name: "value",
                type: targetType,
              }),
            ]),
          }),
        );
      }
      throw new Error(`Unsupported scalar type ${type} for Arrow`);
    case "unknown":
      return new ArrowUtf8();
    case "boolean":
      return new ArrowBool();
    case "integer":
      return new ArrowInt(true, 64);
    case "number":
      return new ArrowFloat(Precision.DOUBLE);
    case "null":
    case "string":
      return new ArrowUtf8();
    case "date":
    case "datetime":
      return new ArrowTimestamp(ArrowTimeUnit.MICROSECOND);
    case "interval":
      throw new Error(`Unsupported scalar type ${type}`);
  }
}

// The span `is_root` field is populated by the API as of this version.
const IS_ROOT_API_VERSION = "0.0.57";

export function useIsRootAvailable(): boolean {
  const { version: apiVersion } = useAPIVersion();
  return semver.gte(apiVersion, IS_ROOT_API_VERSION);
}

export function useIsRootBtqlSnippet(): string {
  const isRootAvailable = useIsRootAvailable();
  return isRootAvailable ? "is_root" : "root_span_id=span_id";
}

export const rowWithIdsSchema = z.object({
  id: z.string(),
  _xact_id: z.string(),
  root_span_id: z.string(),
  span_id: z.string(),
  _object_delete: z.boolean().nullish(),
  _pagination_key: z.string().nullish(),
});
export type RowWithIds = z.infer<typeof rowWithIdsSchema>;

export interface FetchBtqlOptions {
  flags: FeatureFlags;
  apiUrl: string;
  getOrRefreshToken: FetchCachedBtSessionTokenFn;
}

export interface FetchBtqlArgs<T> extends FetchBtqlOptions {
  args: BtqlQueryArgs;
  schema?: z.ZodSchema<T>;
  signal?: AbortSignal;
}

export function useFetchBtqlOptions(): FetchBtqlOptions {
  const { flags } = useFeatureFlags();
  const { api_url: apiUrl } = useOrg();
  const { getOrRefreshToken } = useSessionToken();
  return useMemo(
    () => ({ flags, apiUrl, getOrRefreshToken }),
    [flags, apiUrl, getOrRefreshToken],
  );
}

export async function fetchBtql<T>({
  args,
  flags,
  apiUrl,
  getOrRefreshToken,
  schema,
  signal,
}: FetchBtqlArgs<T>): Promise<BTQLResponse<T & Record<string, unknown>>> {
  const params = makeBtqlParams(args, flags);
  const url = `${apiUrl}/btql`;

  const idToken = await getOrRefreshToken();
  let response: Response;
  const startTime = Date.now();
  try {
    response = await apiFetchJson(url, idToken, params, signal);
    if (response.status !== 200) {
      throw new Error(await response.text());
    }
  } finally {
    const endTime = Date.now();
    if (endTime - startTime > 10000) {
      Sentry.captureException(
        new Error(`Slow BTQL query: ${endTime - startTime}ms`),
        (scope) => {
          scope.setTag("page", "btql");
          scope.setContext("btql", {
            args: redactBtqlQueryArgs(args),
          });
          return scope;
        },
      );
    }
  }
  let queryPlan: string | undefined;
  if (response.headers.get("x-bt-query-plan")) {
    queryPlan = extractPlan(response);
    console.log(queryPlan);
  }

  const jsonData = await response.json();

  // We don't do anything with this value. We just want to validate it. Manu
  // will likely have an opinion about a better way to do this, but I think this
  // is fine for now because it doesn't tamper with the data.
  if (schema) {
    z.array(schema).parse(jsonData.data);
  }

  const realtimeState = realtimeStateSchema.safeParse(jsonData.realtime_state);
  if (
    realtimeState.success &&
    (realtimeState.data.type === "exhausted_memory_budget" ||
      realtimeState.data.type === "exhausted_timeout")
  ) {
    const errorText =
      (realtimeState.data.type === "exhausted_memory_budget"
        ? "Exhausted realtime memory budget while executing query, so data may be behind"
        : "Exhausted realtime timeout while executing query, so data may be behind") +
      (realtimeState.data.type === "exhausted_memory_budget" &&
      realtimeState.data.actual_xact_id
        ? ` (up to ${xactToSmartTime(`${realtimeState.data.actual_xact_id}`)}).`
        : ".");
    console.warn(errorText);
    Sentry.captureException(errorText, (scope) => {
      scope.setTag("page", "btql");
      scope.setContext("btql", {
        realtimeState: realtimeState.data,
        queryPlan,
      });
      return scope;
    });
  }

  return jsonData;
}

export async function fetchBtqlBinaryParquet({
  args,
  flags,
  apiUrl,
  getOrRefreshToken,
  signal,
  duck,
}: {
  args: BtqlQueryArgs;
  flags: FeatureFlags;
  apiUrl: string;
  getOrRefreshToken: FetchCachedBtSessionTokenFn;
  signal?: AbortSignal;
  duck: AsyncDuckDB;
}) {
  const params = { ...makeBtqlParams(args, flags), fmt: "parquet" };
  const url = `${apiUrl}/btql`;

  const idToken = await getOrRefreshToken();
  const response = await apiFetchBinary(url, idToken, params, signal);
  if (response.status !== 200) {
    throw new Error(await response.text());
  }
  if (response.headers.get("x-bt-query-plan")) {
    console.log(extractPlan(response));
  }

  const fileName =
    typeof args.query === "string"
      ? args.query
      : sha1(JSON.stringify(args.query));
  const targetFname = `${fileName}_buffer`;
  const buf = await response.arrayBuffer();

  if (buf && buf.byteLength > 0) {
    await duck.registerFileBuffer(targetFname, new Uint8Array(buf));
    return targetFname;
  } else {
    return null;
  }
}

const inferSchemaSchema = z.object({
  name: z.array(z.string()),
  top_values: z.array(
    z.object({
      value: z.unknown(),
      count: z.number(),
    }),
  ),
  type: jsonSchemaObjectSchema,
});
export type InferSchema = z.infer<typeof inferSchemaSchema>;

export async function fetchInferBtql({
  args,
  flags,
  apiUrl,
  getOrRefreshToken,
  signal,
}: {
  args: BtqlQueryArgs;
  flags: FeatureFlags;
  apiUrl: string;
  getOrRefreshToken: FetchCachedBtSessionTokenFn;
  signal?: AbortSignal;
}): Promise<BTQLResponse<InferSchema>> {
  return fetchBtql({
    args,
    flags,
    apiUrl,
    getOrRefreshToken,
    schema: inferSchemaSchema,
    signal,
  });
}

export function dataObjectPageShape(objectType: DataObjectType) {
  return objectType === "experiment" ||
    objectType === "project_logs" ||
    objectType === "playground_logs"
    ? "summary"
    : "spans";
}

export const BTQL_MAX_ITERATIONS = 100;
export async function fetchBtqlPaginated<T>(
  fetchArgs: Omit<FetchBtqlArgs<T>, "args"> & {
    args: Omit<BtqlQueryArgs, "query"> & {
      query: ParsedQuery;
    };
  },
  pageSize: number = 100,
): Promise<Omit<BTQLResponse<T>, "cursor">> {
  let cursor = null;
  let result: BTQLResponse<T> | null = null;
  let iterations = 0;
  for (; iterations < BTQL_MAX_ITERATIONS; iterations++) {
    const currentArgs: FetchBtqlArgs<T> = {
      ...fetchArgs,
      args: {
        ...fetchArgs.args,
        query: {
          ...fetchArgs.args.query,
          limit: pageSize,
          ...(cursor ? { cursor: cursor } : {}),
        },
      },
    };
    const currentResult = await fetchBtql(currentArgs);
    cursor = currentResult.cursor;

    if (!result) {
      result = currentResult;
    } else {
      result.data = result.data.concat(currentResult.data);
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
      result.schema.items = mergeSchemas(
        result.schema.items,
        currentResult.schema.items,
      ) as typeof result.schema.items;
    }

    if (currentResult.data.length === 0) {
      break;
    }
  }

  if (iterations >= BTQL_MAX_ITERATIONS) {
    throw new Error(
      `Exhausted max iterations (${BTQL_MAX_ITERATIONS}) while loading btql result`,
    );
  }
  if (!result) {
    throw new Error(`Invalid no-op btql result (this is likely a bug)`);
  }

  return {
    data: result.data,
    schema: result.schema,
  };
}

function redactBtqlQueryArgs(args: BtqlQueryArgs) {
  return {
    ...args,
    query: redactBtqlQuery(args.query),
  };
}
