import { z } from "zod";
import {
  type AclObjectType,
  type CustomColumn as CustomColumnDb,
  customColumnSchema as dbCustomColumnSchema,
} from "@braintrust/core/typespecs";
import { useOrg } from "#/utils/user";
import {
  type BtSessionToken,
  useSessionToken,
} from "#/utils/auth/session-token";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import {
  apiDelete,
  apiFetchGetCors,
  apiPatch,
  apiPostCors,
} from "#/utils/btapi/fetch";
import { useCallback, useMemo } from "react";
import { type CustomColumnDef } from "#/ui/table/custom-column-form";
import { useFeatureFlags } from "#/lib/feature-flags";
import {
  inferSchema,
  parseLogicalSchema,
  type JSONSchemaObject,
} from "@braintrust/btql/schema";
import { Parser } from "@braintrust/btql/parser";
import { bindBTQLExpr } from "#/utils/search-btql";
import { dbQuery } from "#/utils/duckdb";
import { doubleQuote } from "@braintrust/local/query";
import { type AsyncDuckDBConnection } from "@duckdb/duckdb-wasm";
import { type DataObjectType } from "#/utils/btapi/btapi";

export type CustomColumn = CustomColumnDb & {
  sql: string;
};

const emptyArray: CustomColumn[] = [];

const getTableFromAclType = (aclType: AclObjectType) => {
  switch (aclType) {
    case "experiment":
    case "dataset":
      return aclType;
    case "project_log":
      return "project_logs";
    default:
      return undefined;
  }
};

async function fetchCustomColumns({
  apiUrl,
  sessionToken,
  objectType,
  objectId,
  subtype,
}: {
  apiUrl: string;
  sessionToken: BtSessionToken;
  objectType: AclObjectType;
  objectId: string | null;
  subtype?: AclObjectType;
}): Promise<CustomColumnDb[]> {
  if (
    sessionToken === "loading" ||
    sessionToken === "unauthenticated" ||
    !objectType ||
    !objectId
  ) {
    return [];
  }

  const params = new URLSearchParams();
  params.append("object_type", objectType);
  params.append("object_id", objectId);
  if (subtype) {
    params.append("subtype", subtype);
  }

  const resp = await apiFetchGetCors(
    `${apiUrl}/v1/column?${params.toString()}`,
    sessionToken,
  );
  if (!resp.ok) {
    throw new Error(await resp.text());
  }

  const { objects } = await resp.json();
  return z.array(dbCustomColumnSchema).parse(objects);
}

export function useCustomColumns({
  objectType,
  objectId,
  subtype,
}: {
  objectType: AclObjectType;
  objectId: string | null;
  subtype?: AclObjectType;
}) {
  const { api_url: apiUrl } = useOrg();
  const { getOrRefreshToken } = useSessionToken();
  const queryClient = useQueryClient();
  const {
    flags: { customColumns: areCustomColumnsEnabled },
  } = useFeatureFlags();

  const {
    data: customColumns,
    refetch,
    isError,
    isLoading,
  } = useQuery(
    {
      queryKey: ["useCustomColumns", objectType, objectId, subtype],
      queryFn: async () => {
        const sessionToken = await getOrRefreshToken();
        return fetchCustomColumns({
          apiUrl,
          sessionToken,
          objectType,
          objectId,
          subtype,
        });
      },
      enabled:
        areCustomColumnsEnabled && !!apiUrl && !!objectType && !!objectId,
      meta: {
        // Temporary disabling toast:
        // Public pages throw an error toast when try to load custom columns.
        // Disable this for now while we figure out why custom columns aren't loading
        disableGlobalErrorToast: true,
      },
    },
    queryClient,
  );

  const createCustomColumn = useCallback(
    async ({ name, expr }: CustomColumnDef) => {
      const sessionToken = await getOrRefreshToken();

      const resp = await apiPostCors({
        url: `${apiUrl}/v1/column`,
        sessionToken,
        payload: {
          object_id: objectId,
          object_type: objectType,
          subtype: subtype || null,
          name,
          expr,
        },
        alreadySerialized: false,
      });
      if (!resp.ok) {
        throw new Error(await resp.text());
      }

      refetch();
    },
    [apiUrl, objectType, objectId, subtype, refetch, getOrRefreshToken],
  );

  const deleteCustomColumn = useCallback(
    async (customColumnId: string) => {
      const sessionToken = await getOrRefreshToken();
      const resp = await apiDelete({
        url: `${apiUrl}/v1/column/${customColumnId}`,
        sessionToken,
        payload: {},
        alreadySerialized: false,
      });
      if (!resp.ok) {
        throw new Error(await resp.text());
      }
      refetch();
    },
    [apiUrl, refetch, getOrRefreshToken],
  );

  const updateCustomColumn = useCallback(
    async ({
      columnId,
      columnData,
    }: {
      columnId: string;
      columnData: CustomColumnDef;
    }) => {
      const sessionToken = await getOrRefreshToken();
      const resp = await apiPatch({
        url: `${apiUrl}/v1/column/${columnId}`,
        sessionToken,
        payload: {
          name: columnData.name,
          expr: columnData.expr,
        },
        alreadySerialized: false,
      });
      if (!resp.ok) {
        throw new Error(await resp.text());
      }
      refetch();
    },
    [apiUrl, refetch, getOrRefreshToken],
  );

  return useMemo(
    () => ({
      customColumnDefinitions: customColumns,
      customColumnsEnabled: areCustomColumnsEnabled,
      createCustomColumn,
      deleteCustomColumn,
      updateCustomColumn,
      isError,
      isLoading,
    }),
    [
      customColumns,
      areCustomColumnsEnabled,
      createCustomColumn,
      deleteCustomColumn,
      updateCustomColumn,
      isError,
      isLoading,
    ],
  );
}

function getSqlString({
  column,
  table,
  btqlFields,
}: {
  column: CustomColumnDb;
  table: string;
  btqlFields?: {
    field: string;
    btql: string;
    typeOverride?: JSONSchemaObject;
  }[];
}) {
  try {
    const parser = new Parser(column.name);
    const expr = parser.parseExpr();
    const c = bindBTQLExpr({
      table,
      expr,
      topLevelFields: [],
      btqlFields,
      tableAlias: "t",
    });
    return c.sql.toPlainStringQuery();
  } catch (err) {
    return undefined;
  }
}

export async function parseCustomColumnsSchema({
  conn,
  abort,
  customColumns,
  objectType,
  subtype,
  rowScan,
}: {
  conn: AsyncDuckDBConnection;
  abort: AbortSignal;
  customColumns?: CustomColumnDb[];
  objectType: AclObjectType;
  subtype?: AclObjectType;
  rowScan: string | null;
}) {
  const inferenceDepth =
    customColumns &&
    Math.max(...customColumns.map((c) => c.expr.split(".").length));

  const schemaQueries = (() => {
    const table = getTableFromAclType(subtype ?? objectType);
    if (!customColumns || !table) {
      return undefined;
    }
    if (customColumns.length === 0) {
      return emptyArray;
    }
    const btqlFields = customColumns.map(({ name, expr }) => ({
      field: name,
      btql: expr,
    }));
    return customColumns.reduce((acc: string[], column) => {
      try {
        const sql = getSqlString({ column, table, btqlFields });
        if (sql) {
          acc.push(`${sql} AS ${doubleQuote(column.name)}`);
        }
      } catch (err) {}
      return acc;
    }, []);
  })();

  const query =
    rowScan && schemaQueries && schemaQueries.length
      ? `
    SELECT
      ${schemaQueries.join(",")}
    FROM (${rowScan}) t`
      : null;

  const result = await dbQuery(conn, abort, query);
  const customColumnsSchema = (() => {
    if (!inferenceDepth) {
      return undefined;
    }
    let schema: JSONSchemaObject = {};
    result?.toArray().forEach((r) => {
      const value = r.toJSON();
      for (const [k, v] of Object.entries(value)) {
        if (typeof v === "string") {
          try {
            value[k] = JSON.parse(v);
          } catch {}
        }
      }
      schema = inferSchema({ schema, value, depth: inferenceDepth });
    });
    return schema;
  })();

  const withClauses = (() => {
    const table = getTableFromAclType(subtype ?? objectType);
    if (!customColumns || !table || !customColumnsSchema) {
      return undefined;
    }
    if (customColumns.length === 0) {
      return emptyArray;
    }
    const btqlFields = customColumns.map(({ name, expr }) => {
      try {
        const fieldSchema = parseLogicalSchema(
          customColumnsSchema.properties?.[name],
        );
        return {
          field: name,
          btql: expr,
          typeOverride: fieldSchema,
        };
      } catch {
        return {
          field: name,
          btql: expr,
        };
      }
    });
    return customColumns.reduce((acc: CustomColumn[], column) => {
      try {
        const sql = getSqlString({ column, table, btqlFields });
        if (sql) {
          acc.push({
            ...column,
            sql: `${sql} AS ${doubleQuote(column.name)}`,
          });
        }
      } catch (err) {}
      return acc;
    }, []);
  })();

  return {
    customColumns: withClauses,
    customColumnsSchema: customColumnsSchema,
  };
}

export type CustomColumnScope = {
  objectType: AclObjectType;
  objectId: string | null;
  subtype?: AclObjectType;
};

export function getCustomColumnScope(
  args:
    | {
        objectType: "dataset";
        objectId: string | null;
        subtype?: undefined;
      }
    | {
        objectType?: Exclude<DataObjectType, "dataset">;
        projectId: string | null;
      },
): CustomColumnScope {
  if (args.objectType === "dataset") {
    return {
      objectType: args.objectType,
      objectId: args.objectId,
      subtype: undefined,
    };
  }

  return {
    objectType: "project",
    objectId: args.projectId,
    subtype:
      args.objectType === "project_logs"
        ? "project_log"
        : args.objectType === "experiment" ||
            args.objectType === "prompt_session"
          ? args.objectType
          : undefined,
  };
}
