use std::{borrow::Cow, cmp::Ordering, collections::HashSet};

use time::{format_description::well_known::Rfc3339, Date, Duration, Month, OffsetDateTime};
use util::Value;

use super::context::ExprContext;
use crate::{
    binder::ast::{ArithmeticOp, UnaryOp},
    typesystem::{
        cast::value_is_null,
        coerce::cast_to_scalar_type,
        error::{Result, TypesystemError},
        types::{ArithmeticType, CalendarDuration},
        CastInto,
    },
};

// NOTE(austin): This function is not ideal for recursion because it collapses
// the lifetime of the slice and the lifetime of the contained `Cow`s.  Instead,
// we copy to a vector of borrows and then call the vector-based `interpret_expr_vec`
// recursively instead.
pub fn interpret_expr<'v>(
    ctx: &ExprContext,
    expr: &'v crate::binder::ast::Expr,
    rows: &'v [Cow<'v, util::Value>],
) -> Result<Vec<Cow<'v, util::Value>>> {
    let vec: Vec<Cow<'v, util::Value>> = rows.iter().map(|r| Cow::Borrowed(r.as_ref())).collect();
    return interpret_expr_vec(ctx, expr, vec);
}

// This function clones the input rows in various places, but we generally call
// this with borrowed values so cloning them is cheap.
pub fn interpret_expr_vec<'v>(
    ctx: &ExprContext,
    expr: &'v crate::binder::ast::Expr,
    rows: Vec<Cow<'v, util::Value>>,
) -> Result<Vec<Cow<'v, util::Value>>> {
    use crate::binder::ast::Expr::*;
    Ok(match &expr {
        Literal(lit) => rows
            .into_iter()
            .map(|_| Cow::Borrowed(&lit.value))
            .collect::<Vec<_>>(),
        Field(field) => rows
            .into_iter()
            .map(|row: Cow<'v, util::Value>| {
                let field_value: Cow<'v, util::Value> =
                    util::json::value_at_path_pieces_cow(row, &field.name)
                        .unwrap_or(Cow::Owned(Value::Null));

                // NOTE: This is not the most efficient way to model this cast. We should be deriving the field type
                // at optimization time and comparing it to the physical type we expect from storage. But it'll do for now.
                let coerced_field_value = cast_to_scalar_type(field_value, field.scalar_type)
                    .unwrap_or(Cow::Owned(Value::Null));

                Ok(coerced_field_value)
            })
            .collect::<Result<Vec<_>>>()?,
        Comparison { op, left, right } => {
            let left_values = interpret_expr(ctx, left, &rows)?;
            let right_values = interpret_expr(ctx, right, &rows)?;
            left_values
                .iter()
                .zip(right_values.iter())
                .map(|(left_value, right_value)| {
                    Ok(Cow::Owned(nullable_boolean_expr(
                        ctx,
                        &left_value,
                        &right_value,
                        op,
                    )?))
                })
                .collect::<Result<Vec<_>>>()?
        }
        Boolean { op, children } => {
            let children_values = children
                .iter()
                .map(|child| interpret_expr(ctx, child, &rows))
                .collect::<Result<Vec<_>>>()?;

            let mut transposed_values =
                vec![vec![Cow::Owned(Value::Null); children.len()]; rows.len()];
            for (i, expr_batch) in children_values.into_iter().enumerate() {
                for (j, value) in expr_batch.into_iter().enumerate() {
                    transposed_values[j][i] = value;
                }
            }

            transposed_values
                .into_iter()
                .map(|values| Ok(Cow::Owned(perform_boolean_op(op, &values)?)))
                .collect::<Result<Vec<_>>>()?
        }
        Arithmetic { op, left, right } => {
            let left_values = interpret_expr(ctx, left, &rows)?;
            let right_values = interpret_expr(ctx, right, &rows)?;
            left_values
                .iter()
                .zip(right_values.iter())
                .map(|(left_value, right_value)| {
                    Ok(Cow::Owned(perform_arithmetic_op(
                        &left_value,
                        &right_value,
                        op,
                    )?))
                })
                .collect::<Result<Vec<_>>>()?
        }
        Unary { op, expr } => {
            let values = interpret_expr_vec(ctx, expr, rows)?;
            values
                .into_iter()
                .map(|v| perform_unary_op_cow(v, op))
                .collect::<Result<Vec<_>>>()?
        }
        Ternary { conds, else_expr } => {
            let mut current_conds = vec![false; rows.len()];
            let mut current_results: Vec<Cow<'v, util::Value>> =
                vec![Cow::Owned(util::Value::Null); rows.len()];
            for cond in conds {
                let live_rows: Vec<Cow<'v, util::Value>> = current_conds
                    .iter()
                    .zip(rows.iter())
                    .map(|(is_true, row)| {
                        if *is_true {
                            Cow::Owned(Value::Bool(false))
                        } else {
                            row.clone()
                        }
                    })
                    .collect::<Vec<_>>();
                let cond_values = interpret_expr_vec(ctx, cond.cond.as_ref(), live_rows)?;

                let matches = cond_values
                    .iter()
                    .map(|cond_value| {
                        if let Ok(b) = CastInto::<bool>::cast(cond_value.as_ref()) {
                            b
                        } else {
                            false
                        }
                    })
                    .collect::<Vec<_>>();

                if matches.iter().any(|m| *m) {
                    let live_thens: Vec<Cow<'v, util::Value>> = matches
                        .iter()
                        .zip(rows.iter())
                        .map(|(is_true, row)| {
                            if *is_true {
                                row.clone()
                            } else {
                                Cow::Owned(Value::Null)
                            }
                        })
                        .collect::<Vec<_>>();

                    let then_values = interpret_expr_vec(ctx, cond.then.as_ref(), live_thens)?;

                    for (i, (is_match, then_value)) in
                        matches.iter().zip(then_values.into_iter()).enumerate()
                    {
                        if *is_match {
                            current_results[i] = then_value;
                            current_conds[i] = true;
                        }
                    }
                }
            }

            let remaining_rows: Vec<Cow<'v, util::Value>> = current_conds
                .iter()
                .zip(rows.into_iter())
                .map(|(is_true, row)| {
                    if *is_true {
                        Cow::Owned(Value::Null)
                    } else {
                        row
                    }
                })
                .collect::<Vec<_>>();
            let else_values = interpret_expr_vec(ctx, else_expr, remaining_rows)?;

            for (i, (is_match, else_value)) in current_conds
                .iter()
                .zip(else_values.into_iter())
                .enumerate()
            {
                if !*is_match {
                    current_results[i] = else_value;
                }
            }

            current_results
        }
        Function(func) => {
            let args: Vec<Vec<Cow<'v, util::Value>>> = func
                .args
                .iter()
                .map(|arg| interpret_expr_vec(ctx, arg, rows.clone()))
                .collect::<Result<Vec<_>>>()?;

            // The row count is only needed if the function takes no args.
            let row_count = if args.is_empty() {
                Some(rows.len())
            } else {
                None
            };

            apply_function(ctx, &func.name, args, row_count)?
        }
        Interval { value, unit } => {
            let interval_str = format!("{} {:?}", value, unit);
            rows.iter()
                .map(|_| Cow::Owned(Value::String(interval_str.clone())))
                .collect()
        }
        Includes { haystack, needle } => {
            let haystack_values = interpret_expr(ctx, haystack, &rows)?;
            let needle_values = interpret_expr(ctx, needle, &rows)?;

            haystack_values
                .iter()
                .zip(needle_values.iter())
                .map(|(haystack_value, needle_value)| {
                    let result = perform_includes(ctx, haystack_value, needle_value)
                        .map(|b| Value::Bool(b))
                        .unwrap_or(Value::Null);
                    Ok(Cow::Owned(result))
                })
                .collect::<Result<Vec<_>>>()?
        }
    })
}

fn nullable_boolean_expr(
    ctx: &ExprContext,
    left: &util::Value,
    right: &util::Value,
    op: &crate::binder::ast::ComparisonOp,
) -> Result<util::Value> {
    let result = match op {
        crate::binder::ast::ComparisonOp::Eq => perform_equals(left, right),
        crate::binder::ast::ComparisonOp::Ne => perform_equals(left, right).map(|b| !b),
        crate::binder::ast::ComparisonOp::Match => perform_match(ctx, left, right),
        crate::binder::ast::ComparisonOp::Like => perform_like(left, right, false),
        crate::binder::ast::ComparisonOp::Ilike => perform_like(left, right, true),
        crate::binder::ast::ComparisonOp::Gt => null_safe_binop(left, right, |left, right| {
            order_values(left, right).map(|ord| ord == Ordering::Greater)
        }),
        crate::binder::ast::ComparisonOp::Ge => null_safe_binop(left, right, |left, right| {
            order_values(left, right).map(|ord| ord == Ordering::Greater || ord == Ordering::Equal)
        }),
        crate::binder::ast::ComparisonOp::Lt => null_safe_binop(left, right, |left, right| {
            order_values(left, right).map(|ord| ord == Ordering::Less)
        }),
        crate::binder::ast::ComparisonOp::Le => null_safe_binop(left, right, |left, right| {
            order_values(left, right).map(|ord| ord == Ordering::Less || ord == Ordering::Equal)
        }),
    };
    Ok(match result {
        Some(b) => util::Value::Bool(b),
        None => util::Value::Null,
    })
}

fn perform_match(ctx: &ExprContext, left: &util::Value, right: &util::Value) -> Option<bool> {
    if value_is_null(left) {
        return None;
    }
    let right_string = CastInto::<String>::cast(right);
    match (left, right_string) {
        (util::Value::String(left), Ok(right)) => perform_match_strings(ctx, left, &right),
        (util::Value::Array(_), Ok(right)) | (util::Value::Object(_), Ok(right)) => {
            let left_str: String = CastInto::<String>::cast(left).ok()?;
            perform_match_strings(ctx, &left_str, &right)
        }
        (left, _) => perform_equals(left, right),
    }
}

// TODO(austin): This should ideally be done in the planner.
pub fn perform_like(
    left: &util::Value,
    right: &util::Value,
    case_insensitive: bool,
) -> Option<bool> {
    let right_string = CastInto::<String>::cast(right).ok()?;
    let left_string = CastInto::<String>::cast(left).ok()?;

    // Convert SQL LIKE pattern to regex pattern
    let mut regex_pattern = String::with_capacity(right_string.len() + 2);
    regex_pattern.push('^');
    for c in right_string.chars() {
        match c {
            '%' => regex_pattern.push_str(".*"),
            c => regex_pattern.push_str(&regex::escape(&c.to_string())),
        }
    }
    regex_pattern.push('$');

    let regex = if case_insensitive {
        regex::RegexBuilder::new(&regex_pattern)
            .case_insensitive(true)
            .build()
            .ok()?
    } else {
        regex::RegexBuilder::new(&regex_pattern).build().ok()?
    };

    Some(regex.is_match(&left_string))
}

#[cfg(feature = "tantivy")]
pub fn perform_match_strings(ctx: &ExprContext, left: &str, right: &str) -> Option<bool> {
    let mut tokenizer = ctx
        .get_tokenizer("default")
        .expect("failed to get default tokenizer");

    let right = match CastInto::<tantivy::DateTime>::cast(right) {
        Ok(dt) => Cow::Owned(format!("{:?}", dt)),
        Err(_) => Cow::Borrowed(right),
    };

    let mut match_tokens = Vec::new();
    tokenizer.token_stream(&right).process(&mut |token| {
        match_tokens.push(token.text.clone());
    });

    if match_tokens.is_empty() {
        return Some(true);
    }

    let mut tok_stream = tokenizer.token_stream(left);
    let mut window = std::collections::VecDeque::with_capacity(match_tokens.len());

    while let Some(token) = tok_stream.next() {
        if window.len() == match_tokens.len() {
            window.pop_front();
        }
        window.push_back(token.text.clone());
        if window.len() == match_tokens.len()
            && window.iter().zip(&match_tokens).all(|(a, b)| a == b)
        {
            return Some(true);
        }
    }

    Some(false)
}

// NOTE(hurshal): This is a fallback implementation of match using naive string contains for the wasm32 target.
// In the future consider using a simple tantivy tokenizer to re-consolidate implementations
#[cfg(not(feature = "tantivy"))]
pub fn perform_match_strings(ctx: &ExprContext, left: &str, right: &str) -> Option<bool> {
    let right = match OffsetDateTime::parse(right, &time::format_description::well_known::Rfc3339)
        .map(|dt| truncate_timestamp_with_tz_offset(dt, "second", ctx.tz_offset))
    {
        Ok(dt) => Cow::Owned(format!("{:?}", dt)),
        Err(_) => Cow::Borrowed(right),
    };

    if right.is_empty() || left.contains(right.as_ref()) {
        return Some(true);
    }
    Some(false)
}

pub fn perform_equals(left: &util::Value, right: &util::Value) -> Option<bool> {
    null_safe_binop(left, right, |left, right| Some(left == right))
}

pub fn null_safe_binop<F>(left: &util::Value, right: &util::Value, op: F) -> Option<bool>
where
    F: FnOnce(&util::Value, &util::Value) -> Option<bool>,
{
    if matches!(left, util::Value::Null) || matches!(right, util::Value::Null) {
        return None;
    }
    match crate::typesystem::coerce(left, right) {
        Ok((left, right)) => op(left.as_ref(), right.as_ref()),
        Err(e) => {
            log::debug!("failed to coerce values: {:?}, {:?}: {:?}", left, right, e);
            None
        }
    }
}

pub fn order_values(a: &Value, b: &Value) -> Option<Ordering> {
    // If one value is NULL but the other is not, then we need to sort consistently with "NULLS LAST"
    // semantics, otherwise the sort will be unstable.
    match (a, b) {
        (Value::Null, Value::Null) => return Some(Ordering::Equal),
        (Value::Null, _) => return Some(Ordering::Greater),
        (_, Value::Null) => return Some(Ordering::Less),
        _ => {}
    }

    Some(match crate::typesystem::coerce(a, b) {
        Ok((left, right)) => match (left.as_ref(), right.as_ref()) {
            (Value::Null, Value::Null) => Ordering::Equal,
            (Value::Bool(a), Value::Bool(b)) => a.cmp(b),
            (Value::Number(a), Value::Number(b)) => {
                // This is surely not the most efficient way to do this. We may want to change
                // coerce to instead return the raw values or implement our own better numeric type
                if a.is_f64() && b.is_f64() {
                    a.as_f64().unwrap().total_cmp(&b.as_f64().unwrap())
                } else if a.is_i64() && b.is_i64() {
                    a.as_i64().unwrap().cmp(&b.as_i64().unwrap())
                } else if a.is_u64() && b.is_u64() {
                    a.as_u64().unwrap().cmp(&b.as_u64().unwrap())
                } else {
                    Ordering::Equal
                }
            }
            (Value::String(a), Value::String(b)) => a.cmp(b),
            (Value::Array(a), Value::Array(b)) => {
                let max_len = std::cmp::max(a.len(), b.len());
                for i in 0..max_len {
                    if i >= a.len() {
                        return Some(Ordering::Less);
                    }
                    if i >= b.len() {
                        return Some(Ordering::Greater);
                    }

                    let ordering = order_values(&a[i], &b[i])?;
                    if ordering != Ordering::Equal {
                        return Some(ordering);
                    }
                }
                Ordering::Equal
            }
            (Value::Object(a), Value::Object(b)) => {
                let mut all_keys: HashSet<String> = a.keys().cloned().collect();
                all_keys.extend(b.keys().cloned());

                for key in all_keys {
                    let a_value = a.get(&key).unwrap_or(&Value::Null);
                    let b_value = b.get(&key).unwrap_or(&Value::Null);
                    let ordering = order_values(a_value, b_value)?;
                    if ordering != Ordering::Equal {
                        return Some(ordering);
                    }
                }
                Ordering::Equal
            }
            _ => {
                debug_assert!(false, "uncomparable values: {:?}, {:?}", a, b);
                return None;
            }
        },
        Err(e) => {
            log::debug!("failed to coerce values: {:?}, {:?}: {:?}", a, b, e);
            // Turn them both into strings and compare them
            let a_str = CastInto::<String>::cast(a).ok()?;
            let b_str = CastInto::<String>::cast(b).ok()?;
            a_str.cmp(&b_str)
        }
    })
}

fn perform_unary_op_cow<'v>(value: Cow<'v, Value>, op: &UnaryOp) -> Result<Cow<'v, Value>> {
    match op {
        UnaryOp::Cast { cast_type } => {
            // We could technically do this in `cast_to_scalar_type`, but it is quite disruptive
            // to other things that rely on that.
            if value_is_null(value.as_ref()) {
                return Ok(Cow::Owned(Value::Null));
            }
            Ok(cast_to_scalar_type(value, *cast_type).unwrap_or(Cow::Owned(Value::Null)))
        }
        _ => perform_unary_op(&value, op).map(Cow::Owned),
    }
}

fn perform_unary_op(value: &Value, op: &UnaryOp) -> Result<Value> {
    Ok(match op {
        UnaryOp::Cast { cast_type: _ } => {
            unreachable!("cast should be handled in perform_unary_op_cow");
        }
        UnaryOp::Not => match value {
            Value::Null => Value::Null,
            _ => {
                let bool_val: Result<bool, _> = value.cast();
                match bool_val {
                    Ok(b) => Value::Bool(!b),
                    Err(e) => {
                        return Err(TypesystemError::InternalError(format!(
                            "Failed to cast value to boolean: {}",
                            e
                        )))
                    }
                }
            }
        },
        UnaryOp::Neg => match value {
            Value::Null => Value::Null,
            Value::Number(n) => {
                if let Some(i) = n.as_i64() {
                    Value::Number((-i).into())
                } else if let Some(f) = n.as_f64() {
                    match serde_json::Number::from_f64(-f) {
                        Some(num) => Value::Number(num),
                        None => {
                            return Err(TypesystemError::InternalError(
                                "Failed to negate float value".to_string(),
                            ))
                        }
                    }
                } else {
                    return Err(TypesystemError::InternalError(
                        "Failed to negate numeric value".to_string(),
                    ));
                }
            }
            _ => {
                return Err(TypesystemError::InternalError(format!(
                    "Cannot negate non-numeric value: {:?}",
                    value
                )))
            }
        },
        UnaryOp::IsNull => Value::Bool(value_is_null(value)),
        UnaryOp::IsNotNull => Value::Bool(!value_is_null(value)),
    })
}

fn bool_op(values: &[Cow<util::Value>], op: fn(bool, bool) -> bool) -> Option<bool> {
    values
        .iter()
        .map(|value| value.as_ref().cast())
        .collect::<Result<Vec<bool>, TypesystemError>>()
        .ok()?
        .iter()
        .fold(None, |acc, value| match acc {
            None => Some(*value),
            Some(b) => Some(op(b, *value)),
        })
}

#[inline]
fn accum_and(left: bool, right: bool) -> bool {
    left && right
}

#[inline]
fn accum_or(left: bool, right: bool) -> bool {
    left || right
}

fn perform_boolean_op(
    op: &crate::binder::ast::BooleanOp,
    values: &[Cow<util::Value>],
) -> Result<util::Value> {
    let result = match op {
        crate::binder::ast::BooleanOp::And => bool_op(values, accum_and),
        crate::binder::ast::BooleanOp::Or => bool_op(values, accum_or),
    };

    Ok(match result {
        Some(b) => util::Value::Bool(b),
        None => util::Value::Null,
    })
}

pub fn add_duration_to_datetime(
    dt: OffsetDateTime,
    duration: &CalendarDuration,
) -> Result<OffsetDateTime> {
    match duration {
        CalendarDuration::Fixed(duration) => dt.checked_add(*duration).ok_or_else(|| {
            TypesystemError::InternalError("DateTime arithmetic overflow".to_string())
        }),
        CalendarDuration::Months(months) => {
            let mut year = dt.year();
            let mut month = dt.month() as i32 + months;

            year += month.div_euclid(12);
            month = month.rem_euclid(12);
            if month == 0 {
                month = 12;
                year -= 1;
            }

            let new_month = Month::try_from(month as u8).unwrap();
            let days_in_new_month = new_month.length(year);
            let new_day = dt.day().min(days_in_new_month);

            Ok(OffsetDateTime::new_in_offset(
                Date::from_calendar_date(year, new_month, new_day).map_err(|_| {
                    TypesystemError::InternalError("Invalid date after adding months".to_string())
                })?,
                dt.time(),
                dt.offset(),
            ))
        }
        CalendarDuration::Years(years) => {
            let new_year = dt.year() + years;

            // Clamp Feb 29 to Feb 28 in non-leap years
            let new_day = if dt.month() == Month::February
                && dt.day() == 29
                && !time::util::is_leap_year(new_year)
            {
                28
            } else {
                dt.day()
            };

            Ok(OffsetDateTime::new_in_offset(
                Date::from_calendar_date(new_year, dt.month(), new_day).map_err(|_| {
                    TypesystemError::InternalError("Invalid date after adding years".to_string())
                })?,
                dt.time(),
                dt.offset(),
            ))
        }
    }
}

pub fn perform_arithmetic_op(
    left: &util::Value,
    right: &util::Value,
    op: &crate::binder::ast::ArithmeticOp,
) -> Result<util::Value> {
    let left: ArithmeticType = left.cast()?;
    let right: ArithmeticType = right.cast()?;

    match (left, right) {
        (ArithmeticType::Null, _) | (_, ArithmeticType::Null) => Ok(Value::Null),
        (ArithmeticType::Integer(l), ArithmeticType::Integer(r)) => {
            let result = match op {
                ArithmeticOp::Add => l.checked_add(r),
                ArithmeticOp::Sub => l.checked_sub(r),
                ArithmeticOp::Mul => l.checked_mul(r),
                ArithmeticOp::Div => {
                    if r == 0 {
                        return Ok(Value::Null);
                    }
                    l.checked_div(r)
                }
                ArithmeticOp::Mod => {
                    if r == 0 {
                        return Ok(Value::Null);
                    }
                    l.checked_rem(r)
                }
            };
            match result {
                Some(n) => Ok(Value::Number(n.into())),
                None => Err(TypesystemError::InternalError(
                    "Integer overflow".to_string(),
                )),
            }
        }
        (ArithmeticType::Integer(l), ArithmeticType::Number(r)) => {
            perform_float_op(l as f64, r, op)
        }
        (ArithmeticType::Number(l), ArithmeticType::Integer(r)) => {
            perform_float_op(l, r as f64, op)
        }
        (ArithmeticType::Number(l), ArithmeticType::Number(r)) => perform_float_op(l, r, op),
        (ArithmeticType::DateTime(dt), ArithmeticType::Duration(duration)) => {
            let duration = match op {
                ArithmeticOp::Add => duration,
                ArithmeticOp::Sub => duration.negate(),
                _ => {
                    return Err(TypesystemError::InternalError(format!(
                        "Unsupported operation {:?} between datetime and interval",
                        op
                    )))
                }
            };
            let new_dt = add_duration_to_datetime(dt, &duration)?;
            let result = new_dt.format(&Rfc3339).map_err(|e| {
                TypesystemError::InternalError(format!("Failed to format datetime: {}", e))
            })?;
            Ok(Value::String(result))
        }
        (ArithmeticType::Duration(duration), ArithmeticType::DateTime(dt)) => {
            match op {
                ArithmeticOp::Add => {
                    let new_dt = add_duration_to_datetime(dt, &duration)?;
                    let result = new_dt.format(&Rfc3339).map_err(|e| {
                        TypesystemError::InternalError(format!("Failed to format datetime: {}", e))
                    })?;
                    Ok(Value::String(result))
                }
                // Subtracting a datetime from an interval is not supported.
                _ => Err(TypesystemError::InternalError(format!(
                    "Unsupported operation '{:?}' between interval and datetime",
                    op
                ))),
            }
        }
        (left, right) => Err(TypesystemError::InternalError(format!(
            "Invalid arithmetic operation between {:?} and {:?}",
            left, right
        ))),
    }
}

fn apply_function<'v>(
    ctx: &ExprContext,
    name: &str,
    mut args: Vec<Vec<Cow<'v, util::Value>>>,
    row_count: Option<usize>,
) -> Result<Vec<Cow<'v, util::Value>>> {
    match name {
        "coalesce" => {
            if args.is_empty() {
                return Ok(vec![Cow::Owned(Value::Null); row_count.unwrap_or(0)]);
            }
            // Use the first argument as the initial result.
            let rest = args.split_off(1);
            let mut result = args
                .pop()
                .expect("one remaining element after splitting off");
            assert!(
                args.len() == 0,
                "after splitting off and popping the remaining element, args should be empty"
            );

            for i in 0..result.len() {
                match result[i].as_ref() {
                    Value::Null => { /* leave as is */ }
                    v if value_is_null(v) => {
                        result[i] = Cow::Owned(Value::Null);
                    }
                    _ => {}
                }
            }

            for arg in rest {
                for (i, value) in arg.into_iter().enumerate() {
                    match (result[i].as_ref(), value.as_ref()) {
                        (&Value::Null, v) if !value_is_null(v) => {
                            // The only case we update is when the current value is null
                            result[i] = value;
                        }
                        _ => {}
                    }
                }
            }

            Ok(result)
        }
        "nullif" => {
            if args.len() != 2 {
                return Err(TypesystemError::InternalError(
                    "nullif function requires exactly 2 arguments".to_string(),
                ));
            }

            let right = args.pop().unwrap();
            let left = args.pop().unwrap();

            left.into_iter()
                .zip(right.into_iter())
                .map(|(left, right)| {
                    if perform_equals(&left, &right).unwrap_or(false) {
                        Ok(Cow::Owned(util::Value::Null))
                    } else {
                        Ok(left)
                    }
                })
                .collect()
        }
        "current_timestamp" => {
            if !args.is_empty() {
                return Err(TypesystemError::InternalError(
                    "current_timestamp function takes no arguments".to_string(),
                ));
            }
            let count = row_count.ok_or_else(|| {
                TypesystemError::InternalError(
                    "Row count not provided for zero-argument function".to_string(),
                )
            })?;

            let timestamp_str = ctx.current_timestamp.format(&Rfc3339).map_err(|e| {
                TypesystemError::InternalError(format!("Failed to format timestamp: {}", e))
            })?;

            Ok(vec![Cow::Owned(util::Value::String(timestamp_str)); count])
        }
        "current_date" => {
            if !args.is_empty() {
                return Err(TypesystemError::InternalError(
                    "current_date function takes no arguments".to_string(),
                ));
            }
            let count = row_count.ok_or_else(|| {
                TypesystemError::InternalError(
                    "Row count not provided for zero-argument function".to_string(),
                )
            })?;

            let date = ctx
                .current_timestamp
                .replace_hour(0)?
                .replace_minute(0)?
                .replace_second(0)?
                .replace_nanosecond(0)?;

            let date_str = date.format(&Rfc3339).map_err(|e| {
                TypesystemError::InternalError(format!("Failed to format date: {}", e))
            })?;

            Ok(vec![Cow::Owned(util::Value::String(date_str)); count])
        }
        "year" | "month" | "week" | "day" | "hour" | "minute" | "second" => {
            if args.len() != 1 {
                return Err(TypesystemError::InternalError(format!(
                    "{} function requires exactly 1 argument",
                    name
                )));
            }

            args[0]
                .iter()
                .map(|value| {
                    let timestamp = match value.as_ref() {
                        util::Value::Null => return Ok(Cow::Owned(util::Value::Null)),
                        util::Value::String(s) => time::OffsetDateTime::parse(s, &Rfc3339)
                            .map_err(|e| {
                                TypesystemError::InternalError(format!(
                                    "Failed to parse timestamp: {}",
                                    e
                                ))
                            })?,
                        _ => {
                            return Err(TypesystemError::InternalError(format!(
                                "{} function requires string timestamp input",
                                name
                            )))
                        }
                    };

                    let truncated =
                        truncate_timestamp_with_tz_offset(timestamp, name, ctx.tz_offset)?;

                    let result = truncated.format(&Rfc3339).map_err(|e| {
                        TypesystemError::InternalError(format!("Failed to format timestamp: {}", e))
                    })?;

                    Ok(Cow::Owned(util::Value::String(result)))
                })
                .collect()
        }
        "lower" => {
            if args.len() != 1 {
                return Err(TypesystemError::InternalError(
                    "lower function requires exactly 1 argument".to_string(),
                ));
            }
            args[0]
                .iter()
                .map(|value| {
                    Ok(Cow::Owned(if value_is_null(value.as_ref()) {
                        Value::Null
                    } else {
                        Value::String(CastInto::<String>::cast(value.as_ref())?.to_lowercase())
                    }))
                })
                .collect()
        }
        "upper" => {
            if args.len() != 1 {
                return Err(TypesystemError::InternalError(
                    "upper function requires exactly 1 argument".to_string(),
                ));
            }
            args[0]
                .iter()
                .map(|value| {
                    Ok(Cow::Owned(if value_is_null(value.as_ref()) {
                        Value::Null
                    } else {
                        Value::String(CastInto::<String>::cast(value.as_ref())?.to_uppercase())
                    }))
                })
                .collect()
        }
        "concat" => {
            if args.is_empty() {
                return Err(TypesystemError::InternalError(
                    "concat function requires at least 1 argument".to_string(),
                ));
            }

            // Zip all argument vectors together
            let mut result = vec![String::new(); args[0].len()];

            for arg_vec in args {
                for (i, value) in arg_vec.iter().enumerate() {
                    if !value_is_null(value) {
                        match value.as_ref() {
                            Value::String(s) => result[i].push_str(s),
                            _ => result[i].push_str(&value.to_string()),
                        }
                    }
                }
            }

            result
                .into_iter()
                .map(|s| Ok(Cow::Owned(Value::String(s))))
                .collect()
        }
        "len" => {
            if args.len() != 1 {
                return Err(TypesystemError::InternalError(
                    "len function requires exactly 1 argument".to_string(),
                ));
            }
            args[0]
                .iter()
                .map(|value| {
                    Ok(Cow::Owned(match value.as_ref() {
                        Value::Array(arr) => Value::Number(arr.len().into()),
                        Value::Null => Value::Number(0.into()),
                        // NOTE(austin): Not quite sure what the right behavior is here.
                        // Since `[]` and `null` are equivalent, we have to return `0` for both,
                        // but it's unclear what to do for non-null non-array values.
                        _ => Value::Null,
                    }))
                })
                .collect()
        }
        "json_extract" => Ok(vec![Cow::Owned(Value::Null); args.len()]),
        "insert" => {
            if args.len() % 2 != 1 {
                return Err(TypesystemError::InternalError(
                    "INSERT requires an odd number of arguments (base objects + key-value pairs)"
                        .to_string(),
                ));
            }

            let base_objects = std::mem::take(&mut args[0]);
            if args.len() == 1 {
                return Ok(base_objects);
            }

            if args[1..].iter().any(|arg| arg.len() != base_objects.len()) {
                return Err(TypesystemError::InternalError(
                    "All arguments must have the same number of rows".to_string(),
                ));
            }

            let mut row_values: Vec<Value> = base_objects
                .into_iter()
                .map(|obj| {
                    if matches!(obj.as_ref(), util::Value::Null) {
                        util::Value::Object(serde_json::Map::new())
                    } else {
                        obj.into_owned()
                    }
                })
                .collect();

            for chunk in args[1..].chunks_exact_mut(2) {
                let [keys, values] = chunk else {
                    unreachable!("insert function requires an even number of arguments");
                };
                for ((row_value, key), value) in row_values
                    .iter_mut()
                    .zip(keys.into_iter())
                    .zip(values.drain(..))
                {
                    let key = match key.as_ref() {
                        util::Value::String(s) => s.to_string(),
                        _ => {
                            return Err(TypesystemError::InternalError(
                                "insert function keys must be strings".to_string(),
                            ))
                        }
                    };
                    util::json::set_value_at_path(row_value, &[key], value.into_owned())
                        .map_err(|e| TypesystemError::InternalError(e.to_string()))?;
                }
            }

            Ok(row_values.into_iter().map(Cow::Owned).collect())
        }
        "greatest" | "least" => {
            // Use the last argument as the initial result.
            let mut result = match args.pop() {
                Some(last) => last,
                None => {
                    return Err(TypesystemError::InternalError(format!(
                        "{} function requires at least 1 argument",
                        name
                    )))
                }
            };

            let replace_if = if name == "greatest" {
                Ordering::Greater
            } else {
                Ordering::Less
            };

            for arg in args {
                for (i, value) in arg.into_iter().enumerate() {
                    match (value.as_ref(), result[i].as_ref()) {
                        (&Value::Null, _) => {}
                        (_, &Value::Null) => {
                            result[i] = value;
                        }
                        (l, r) => {
                            if let Some(ordering) = order_values(&l, &r) {
                                if ordering == replace_if {
                                    result[i] = value;
                                }
                            }
                        }
                    }
                }
            }

            Ok(result)
        }
        _ => Err(TypesystemError::Unsupported {
            msg: format!("function: {}", name),
            value: Value::String(name.to_string()),
        }),
    }
}

fn perform_float_op(l_float: f64, r_float: f64, op: &ArithmeticOp) -> Result<Value> {
    let result = match op {
        ArithmeticOp::Add => l_float + r_float,
        ArithmeticOp::Sub => l_float - r_float,
        ArithmeticOp::Mul => l_float * r_float,
        ArithmeticOp::Div => l_float / r_float,
        ArithmeticOp::Mod => l_float % r_float,
    };

    Ok(serde_json::Number::from_f64(result).map_or(Value::Null, Value::Number))
}

fn perform_includes(ctx: &ExprContext, haystack: &Value, needle: &Value) -> Result<bool> {
    match needle {
        // NOTE(austin): `includes []` returns true because it's an AND op across each
        // element of the needle array. For consistency, `includes null` must also return
        // true since `[]` and `null` are semantically equivalent.
        Value::Null => Ok(true),
        Value::Array(array) => {
            if array.is_empty() {
                Ok(true)
            } else {
                Ok(array
                    .iter()
                    .all(|item| perform_match(ctx, haystack, item).unwrap_or(false)))
            }
        }
        Value::String(_) | Value::Number(_) | Value::Bool(_) => {
            Ok(perform_match(ctx, haystack, needle).unwrap_or(false))
        }
        _ => Err(TypesystemError::Unsupported {
            msg: format!("includes with unsupported needle"),
            value: needle.clone(),
        }),
    }
}

pub fn truncate_timestamp_with_tz_offset(
    timestamp: OffsetDateTime,
    truncate_to: &str,
    tz_offset: Option<i16>,
) -> Result<OffsetDateTime> {
    let timestamp = timestamp.to_offset(time::UtcOffset::UTC);

    let truncated = if let Some(tz_offset) = tz_offset {
        let adjusted = timestamp.saturating_sub(Duration::minutes(tz_offset as i64));
        let truncated = truncate_timestamp(adjusted, truncate_to)?;
        truncated.saturating_add(Duration::minutes(tz_offset as i64))
    } else {
        truncate_timestamp(timestamp, truncate_to)?
    };

    Ok(truncated)
}

pub fn truncate_timestamp(
    timestamp: time::OffsetDateTime,
    truncate_to: &str,
) -> Result<time::OffsetDateTime> {
    match truncate_to {
        "year" => Ok(timestamp
            .replace_month(Month::January)?
            .replace_day(1)?
            .replace_hour(0)?
            .replace_minute(0)?
            .replace_second(0)?
            .replace_nanosecond(0)?),
        "month" => Ok(timestamp
            .replace_day(1)?
            .replace_hour(0)?
            .replace_minute(0)?
            .replace_second(0)?
            .replace_nanosecond(0)?),
        "week" => {
            let days_since_monday = timestamp.weekday().number_days_from_monday();
            Ok(timestamp
                .saturating_sub(Duration::days(days_since_monday as i64))
                .replace_hour(0)?
                .replace_minute(0)?
                .replace_second(0)?
                .replace_nanosecond(0)?)
        }
        "day" => Ok(timestamp
            .replace_hour(0)?
            .replace_minute(0)?
            .replace_second(0)?
            .replace_nanosecond(0)?),
        "hour" => Ok(timestamp
            .replace_minute(0)?
            .replace_second(0)?
            .replace_nanosecond(0)?),
        "minute" => Ok(timestamp.replace_second(0)?.replace_nanosecond(0)?),
        "second" => Ok(timestamp.replace_nanosecond(0)?),
        unsupported => {
            return Err(TypesystemError::InternalError(format!(
                "Unsupported date truncation: {}",
                unsupported
            )))
        }
    }
    .map_err(|e: time::error::ComponentRange| e.into())
}

impl From<time::error::ComponentRange> for TypesystemError {
    fn from(err: time::error::ComponentRange) -> Self {
        TypesystemError::InternalError(err.to_string())
    }
}
