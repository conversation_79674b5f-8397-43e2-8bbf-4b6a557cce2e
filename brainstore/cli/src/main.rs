use std::borrow::Cow;

use base::{BaseArgs, CLIArgs};
use clap::{Parser, Subcommand};
use command_proc::{CommandProc, WorkerCommandArgs};
use otel_common::{opentelemetry, opentelemetry::trace::Tracer<PERSON>rovider};
use serde::{Deserialize, Serialize};
use tracing_subscriber::{layer::SubscriberExt, Registry};
use util::{anyhow::Result, futures::pin_mut};

pub mod base;
pub mod bench;
pub mod btql;
mod cleanup;
pub mod command_proc;
pub mod compaction_worker;
pub mod executor_pool;
pub mod healthcheck;
pub mod index;
pub mod metrics;
pub mod pg_pool;
pub mod ping;
pub mod retention;
mod setup_tracing;
pub mod utilities;
pub mod vacuum;
pub mod wal;
pub mod web;

pub mod memprof;

#[cfg(test)]
pub mod stress;

use setup_tracing::{make_env_filter, make_stderr_layer, setup_log_tracer, setup_otel_provider};

#[cfg(not(target_env = "msvc"))]
use tikv_jemallocator::Jemalloc;

#[cfg(not(target_env = "msvc"))]
#[global_allocator]
static GLOBAL: Jemalloc = Jemalloc;

// Include the generated git commit information
include!(concat!(env!("OUT_DIR"), "/git_commit.rs"));

#[derive(Debug, Parser)]
#[command(author, version, about)]
struct Args {
    #[command(subcommand)]
    cmd: Commands,

    #[cfg(feature = "distribute")]
    #[arg(
        long,
        env = "BRAINTRUST_APP_URL",
        default_value = "https://www.braintrust.dev"
    )]
    app_url: String,

    #[cfg(feature = "distribute")]
    #[arg(long, env = "BRAINSTORE_LICENSE_KEY")]
    license_key: Option<String>,
}

#[derive(Subcommand, Debug, Clone, Serialize, Deserialize)]
pub enum Commands {
    Ping(ping::PingCommand),
    Cleanup(CLIArgs<cleanup::CleanupCommand>),
    #[command(subcommand)]
    Btql(crate::btql::Commands),
    #[command(subcommand)]
    Wal(wal::WalCommands),
    #[command(subcommand)]
    Index(index::IndexCommands),
    Web(web::WebCommand),
    Worker(command_proc::WorkerCommand),
    Xact(utilities::XactCommand),
    #[command(subcommand)]
    Retention(retention::RetentionCommands),
    #[command(subcommand)]
    Vacuum(vacuum::VacuumCommands),
    #[cfg(feature = "bench")]
    #[command(subcommand)]
    Bench(bench::BenchCommands),
}

#[allow(unused_variables)]
fn is_bench_command(cmd: &Commands) -> bool {
    #[cfg(feature = "bench")]
    {
        matches!(cmd, Commands::Bench(_))
    }
    #[cfg(not(feature = "bench"))]
    {
        false
    }
}

fn main() -> Result<()> {
    let args = Args::parse();

    #[cfg(feature = "distribute")]
    {
        use backoff::{retry, Error as BackoffError, ExponentialBackoff};
        use std::time::Duration;

        let backoff_policy = ExponentialBackoff {
            max_elapsed_time: Some(Duration::from_secs(60)),
            initial_interval: Duration::from_millis(100),
            max_interval: Duration::from_secs(5),
            ..Default::default()
        };

        let op = || {
            license_check::check_license(
                &args.app_url,
                args.license_key.as_ref().map(|s| s.as_str()),
            )
            .map_err(|e| {
                eprintln!("Failed to verify Brainstore license: {:?}", e);
                BackoffError::transient(e)
            }) // Treat all license check errors as transient
        };

        if let Err(e) = retry(backoff_policy, op) {
            match e {
                BackoffError::Permanent(e) => {
                    return Err(e.into());
                }
                BackoffError::Transient { err, .. } => {
                    return Err(err.into());
                }
            }
        }
    }

    let ret = main_inner(args.cmd)?;
    if !matches!(ret, util::Value::Null) {
        println!("{}", ret);
    }
    Ok(())
}

#[cfg(feature = "distribute")]
mod license_check {
    use base64::{prelude::BASE64_STANDARD, Engine};
    use reqwest::blocking::Client;
    use ring::{
        rand::{SecureRandom, SystemRandom},
        signature::{self, UnparsedPublicKey},
    };
    use serde::Deserialize;
    use util::anyhow::Result;

    #[derive(Debug, Deserialize)]
    struct AuthorizeResponse {
        nonce: String,
        authorized: bool,
        signature: String,
    }

    pub fn check_license(app_url: &str, license: Option<&str>) -> Result<()> {
        // NOTE: While this mechanism could be vulnerable to tampering via public key replacement in the binary,
        // we accept this risk since it would require deliberate malicious modification by the user.
        let license = license.ok_or_else(|| {
            util::anyhow::anyhow!(
                "license must be provided via command line or environment variable"
            )
        })?;

        // Generate a 32-byte cryptographically secure random nonce.
        let mut nonce = [0u8; 32];
        let rng = SystemRandom::new();
        rng.fill(&mut nonce)?;
        let nonce = BASE64_STANDARD.encode(nonce);

        let client = Client::new();
        let response = client
            .post(format!("{}/api/brainstore/authorize", app_url))
            .json(&serde_json::json!({
                "version": 1,
                "nonce": nonce,
                "license": license,
            }))
            .send()?;
        if !response.status().is_success() {
            return Err(util::anyhow::anyhow!(
                "failed to validate license: {}",
                response.status()
            ));
        }

        let AuthorizeResponse {
            nonce: response_nonce,
            authorized,
            signature: sig,
        } = response.json()?;

        // The logic to construct the signed payload should match the logic in the server at
        // //app/pages/api/brainstore/authorize.ts.
        let signed_payload = serde_json::json!({
            "nonce": nonce,
            "authorized": authorized,
        })
        .to_string();

        // The base64-encoded ed25519 public key for verifying brainstore authorization responses.
        // It is split into parts to make it nontrivial to replace in the binary.
        let brainstore_authorize_public_key = [
            String::from("d5c2NDlP"),
            String::from("8VHGWTTi"),
            String::from("WIxF/2uX"),
            String::from("21BLpk0b"),
            String::from("19iZsduP"),
            String::from("ZSs="),
        ]
        .join("");
        UnparsedPublicKey::new(
            &signature::ED25519,
            &BASE64_STANDARD.decode(brainstore_authorize_public_key)?,
        )
        .verify(
            signed_payload.as_bytes(),
            BASE64_STANDARD.decode(sig)?.as_ref(),
        )?;

        if nonce != response_nonce {
            return Err(util::anyhow::anyhow!("failed to verify response"));
        }

        if authorized {
            Ok(())
        } else {
            Err(util::anyhow::anyhow!(
                "license is not authorized for Brainstore"
            ))
        }
    }
}

pub fn main_inner(cmd: Commands) -> Result<util::Value> {
    let worker_args = match &cmd {
        Commands::Worker(a) => {
            let args: WorkerCommandArgs = serde_json::from_str(&a.arg)?;
            Some(args)
        }
        _ => None,
    };

    let base_args = match &cmd {
        Commands::Ping(_) => Cow::Owned(BaseArgs::default()),
        Commands::Cleanup(a) => Cow::Borrowed(&a.base),
        Commands::Wal(a) => Cow::Borrowed(wal::base_args(a)),
        Commands::Index(a) => Cow::Borrowed(index::base_args(a)),
        Commands::Btql(a) => Cow::Borrowed(btql::base_args(a)),
        Commands::Web(a) => Cow::Borrowed(&a.base),
        Commands::Worker(_) => Cow::Owned(worker_args.as_ref().unwrap().base.clone()),
        Commands::Xact(a) => Cow::Borrowed(&a.base),
        Commands::Retention(a) => Cow::Borrowed(retention::base_args(a)),
        Commands::Vacuum(a) => Cow::Borrowed(vacuum::base_args(a)),
        #[cfg(feature = "bench")]
        Commands::Bench(a) => Cow::Borrowed(bench::base_args(a)),
    };

    let verbose = base_args.verbose;
    util::global_opts::SUPPRESS_VERBOSE_INFO.store(
        verbose == 1 && base_args.suppress_verbose_info,
        std::sync::atomic::Ordering::Relaxed,
    );

    // This should be done _after_ logging is set up.
    let command_proc = match &cmd {
        Commands::Web(a) => Some(CommandProc::new(
            base_args.clone().into_owned(),
            a.command_proc_lifetime_executions,
            None,
        )?),
        _ => None,
    };

    let pretty_logging = match (base_args.pretty_logging, &cmd) {
        (Some(pretty_logging), _) => pretty_logging,
        (None, Commands::Web(_)) => false,
        (None, _) => true, /* non-web commands default to pretty logging */
    };

    setup_log_tracer(verbose)?;
    let env_filter = make_env_filter(verbose);
    // Only do JSON-logging if this is web
    let stderr_layer = make_stderr_layer(pretty_logging, verbose, base_args.suppress_verbose_info);

    let use_local_otlp = base_args.as_ref().local_otlp;
    let disable_metrics_otlp = base_args.as_ref().disable_metrics_otlp;
    let o11y_otlp_http_endpoint = base_args.o11y_otlp_http_endpoint.clone();

    let runtime = storage::merge::make_runtime()?;

    #[allow(unused)]
    let (memprof_flusher, memprof_flush_handle) = memprof::make_flusher();
    #[cfg(feature = "enable_memprof")]
    if matches!(
        cmd,
        Commands::Wal(_)
            | Commands::Index(_)
            | Commands::Btql(_)
            | Commands::Web(_)
            | Commands::Worker(_)
    ) || is_bench_command(&cmd)
    {
        crate::memprof::run_memprof(base_args.memprof_args.clone(), memprof_flusher);
    }

    let cmd_fut = async move {
        // Create a new OpenTelemetry trace pipeline that exports to OTLP endpoint
        let (tracing_provider, _metrics_provider) = setup_otel_provider(
            use_local_otlp,
            disable_metrics_otlp,
            o11y_otlp_http_endpoint,
        )?;
        let otel_tracer = tracing_provider.tracer("brainstore");
        let otel_layer = tracing_opentelemetry::layer().with_tracer(otel_tracer);

        let subscriber = Registry::default()
            .with(env_filter)
            .with(otel_layer)
            .with(stderr_layer);

        tracing::subscriber::set_global_default(subscriber)?;

        let result = match cmd {
            Commands::Ping(p) => ping::main(p).await,
            Commands::Cleanup(cleanup) => cleanup::main(cleanup).await,
            Commands::Wal(wal) => wal::main(wal).await,
            Commands::Index(index) => index::main(index).await,
            Commands::Btql(btql) => btql::main(btql).await,
            Commands::Web(web) => web::main(web, command_proc.unwrap()).await,
            Commands::Worker(worker) => command_proc::worker(worker).await,
            Commands::Xact(xact) => utilities::main(xact).await,
            Commands::Retention(retention) => retention::main(retention).await,
            Commands::Vacuum(vacuum) => vacuum::main(vacuum).await,
            #[cfg(feature = "bench")]
            Commands::Bench(bench) => bench::main(bench, memprof_flush_handle).await,
        }?;

        // Let OTEL flush
        tracing_provider.force_flush();
        opentelemetry::global::shutdown_tracer_provider();

        Ok::<util::Value, util::anyhow::Error>(result)
    };

    let result = runtime.block_on(async move {
        pin_mut!(cmd_fut);
        cmd_fut.await
    })?;

    {
        let max_wal_in_flight_bytes = storage::wal_stats::MAX_WAL_IN_FLIGHT_BYTES.max();
        if max_wal_in_flight_bytes > 0 {
            tracing::debug!("MAX_WAL_IN_FLIGHT_BYTES: {}", max_wal_in_flight_bytes);
        }
    }
    {
        let max_wal_batch_size =
            storage::wal_stats::MAX_WAL_BATCH_SIZE.load(std::sync::atomic::Ordering::Relaxed);
        if max_wal_batch_size > 0 {
            tracing::debug!("MAX_WAL_BATCH_SIZE: {}", max_wal_batch_size);
        }
    }
    {
        let total_wal_read_bytes =
            storage::wal_stats::TOTAL_WAL_READ_BYTES.load(std::sync::atomic::Ordering::Relaxed);
        if total_wal_read_bytes > 0 {
            tracing::debug!("TOTAL_WAL_READ_BYTES: {}", total_wal_read_bytes);
        }
    }

    Ok(result)
}
