// To run an individual expect test, navigate to the expect test directory, e.g. brainstore/examples/scores, and run
//
// ```
// ../../target/release/brainstore btql test [expect-test-name].btql
// ```
//
// You can also pass `.` to run over all expect tests in the directory and any subdirectories.

use btql::binder::ast::SortExpr;
use btql::binder::ast::SortItem;
use btql::binder::BindRepl;
use btql::binder::Expr;
use btql::interpreter::context::ExprContext;
use clap::Parser;
use itertools::iproduct;
use log::info;
use rand::Rng;
use rayon::prelude::*;
use serde::Deserialize;
use serde::Serialize;
use sha2::Digest;
use sha2::Sha256;
use std::fs;
use std::io::Write;
use std::path::Path;
use std::path::PathBuf;
use std::str::FromStr;
use std::sync::Arc;
use storage::config_with_store::ConfigWithStore;
use storage::index_document::make_full_schema;
use storage::process_wal::CompactSegmentWalInput;
use storage::process_wal::CompactSegmentWalOptions;
use storage::process_wal::ProcessObjectWalInput;
use storage::process_wal::ProcessObjectWalOptions;
use storage::process_wal::ProcessObjectWalStreamOptions;
use storage::process_wal::PAGINATION_KEY_FIELD;
use storage::process_wal::XACT_ID_FIELD;
use storage::wal::fill_default_object_id;
use storage::wal::wal_insert_unnormalized;
use storage::xact_manager::MemoryTransactionManager;
use tempfile::tempdir;
use tokio::runtime::Handle;
use tokio_stream::StreamExt;
use util::anyhow::Context;
use util::bail;
use util::config::ConfigFile;
use util::config::StorageConfig;
use util::config::StorageConfigFile;
use util::futures::future::join_all;
use util::json::PathPiece;
use util::new_id;
use util::serde_yaml;
use util::system_types::make_object_schema;
use util::system_types::FullObjectId;
use util::system_types::FullObjectIdOwned;
use util::tracer::set_override_always_trace;
use util::url::Url;
use util::Value;

use util::{anyhow::Result, config::Config, url_util::str_to_url};

use crate::interpreter::context::InterpreterOpts;
use crate::interpreter::error::InterpreterError;
use crate::interpreter::op::Operator;
use crate::interpreter::InterpreterContext;
use crate::jaq::run_jaq;
use crate::optimizer::error::OptimizerError;
use crate::optimizer::optimizer::OptimizerOpts;
use crate::plan;
use crate::planner::context::PlanContext;
use crate::planner::error::PlannerError;
use btql::typesystem::coerce::normalize_value_for_comparison;
use btql::typesystem::CastInto;

pub struct TestArgs {
    pub config_paths: Vec<PathBuf>,
    pub query_paths: Vec<PathBuf>,
    pub update: bool,
    pub use_tmp_dir: bool,
    pub parallel: bool,
    pub handle: Handle,
    pub storage_opts: StorageTestOpts,
    pub optimizer_opts: OptimizerOpts,
    pub interpreter_opts: InterpreterOpts,
    pub randomize_events: Option<bool>,
    pub default_opts: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Default)]
pub struct ParameterizedOpts {
    #[serde(default)]
    pub batch_size: Vec<usize>,

    #[serde(default)]
    pub no_pushdown: Vec<bool>,

    #[serde(default)]
    pub randomize_events: Vec<bool>,

    #[serde(default)]
    pub partial_compact: Vec<bool>,

    #[serde(default)]
    pub small_segments: Vec<bool>,

    #[serde(default)]
    pub object_id: FullObjectIdOwned,
}

impl ParameterizedOpts {
    fn apply_defaults(&mut self) {
        if self.batch_size.is_empty() {
            self.batch_size = vec![1];
            // Don't think we're getting much value from testing this
            // self.batch_size = vec![1, 100];
        }
        if self.no_pushdown.is_empty() {
            self.no_pushdown = vec![false, true];
            // self.no_pushdown = vec![true];
        }
        if self.randomize_events.is_empty() {
            self.randomize_events = vec![false, true];
        }

        // These are rarely worth testing unless explicitly enabled
        if self.small_segments.is_empty() {
            self.small_segments = vec![false];
        }
        if self.partial_compact.is_empty() {
            self.partial_compact = vec![false];
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Default, Parser)]
pub struct StorageTestOpts {
    #[arg(long, help = "Create many, tiny segments")]
    #[serde(default)]
    pub small_segments: bool,

    #[arg(long, help = "Partially compact segments")]
    #[serde(default)]
    pub partial_compact: bool,
}

#[cfg(test)]
mod tests {
    use std::env;
    use std::path::PathBuf;

    use util::anyhow::Result;

    use crate::interpreter::context::InterpreterOpts;
    use crate::optimizer::optimizer::OptimizerOpts;

    use super::StorageTestOpts;

    #[test]
    fn test_examples() -> Result<()> {
        let _ = env_logger::builder()
            .is_test(true)
            .filter_level(log::LevelFilter::Error)
            .try_init()
            .unwrap();

        let examples_dir = PathBuf::from(env!("CARGO_MANIFEST_DIR")).join("../examples");

        let update = env::var("UPDATE").is_ok();
        let use_tmp_dir = env::var("NO_TMP_DIR").is_err();
        let parallel = env::var("SERIAL").is_err();
        let randomize_events = env::var("RANDOMIZE_EVENTS")
            .ok()
            .map(|s| s == "true" || s == "1");
        let rt = tokio::runtime::Runtime::new()?;
        super::process_directory(
            &examples_dir,
            &super::TestArgs {
                config_paths: vec![],
                query_paths: vec![],
                update,
                use_tmp_dir,
                parallel,
                randomize_events,
                handle: rt.handle().clone(),
                optimizer_opts: OptimizerOpts::default(),
                interpreter_opts: InterpreterOpts::default(),
                storage_opts: StorageTestOpts::default(),
                default_opts: false,
            },
        )
    }
}

fn process_directory(dir: &Path, args: &TestArgs) -> Result<()> {
    let entries: Vec<_> = fs::read_dir(dir)?.collect::<std::result::Result<_, _>>()?;

    if args.parallel {
        entries
            .par_iter()
            .try_for_each(|entry| process_entry(&entry.path(), args))
    } else {
        entries
            .iter()
            .try_for_each(|entry| process_entry(&entry.path(), args))
    }
}

pub fn process_entry(path: &Path, args: &TestArgs) -> Result<()> {
    if path.is_dir() {
        process_directory(&path, args)?;
    } else {
        return Ok(());
    }

    let schema_path = path.join("schema.json");
    let events_path = path.join("events.jsonl");
    let params_path = path.join("params.yaml");

    let mut params: ParameterizedOpts = if params_path.exists() {
        serde_yaml::from_str(&fs::read_to_string(params_path)?)?
    } else {
        ParameterizedOpts::default()
    };
    params.apply_defaults();

    let mut optimizer_opts_settings = Vec::new();
    if OptimizerOpts::default() == args.optimizer_opts && !args.default_opts {
        for no_pushdown in params.no_pushdown {
            optimizer_opts_settings.push(OptimizerOpts { no_pushdown });
        }
    } else {
        optimizer_opts_settings.push(args.optimizer_opts.clone());
    }

    let mut interpreter_opts_settings = Vec::new();
    if InterpreterOpts::default() == args.interpreter_opts && !args.default_opts {
        for batch_size in params.batch_size {
            interpreter_opts_settings.push(InterpreterOpts {
                batch_size: Some(batch_size),
                index_wal_reader_opts: args.interpreter_opts.index_wal_reader_opts.clone(),
                tz_offset: args.interpreter_opts.tz_offset,
                query_timeout_seconds: args.interpreter_opts.query_timeout_seconds,
            });
        }
    } else {
        interpreter_opts_settings.push(args.interpreter_opts.clone());
    }

    let mut storage_opts_settings = Vec::new();
    if StorageTestOpts::default() == args.storage_opts && !args.default_opts {
        for small_segments in &params.small_segments {
            for partial_process in &params.partial_compact {
                storage_opts_settings.push(StorageTestOpts {
                    small_segments: *small_segments,
                    partial_compact: *partial_process,
                });
            }
        }
    } else {
        storage_opts_settings.push(args.storage_opts.clone());
    }

    let mut randomize_events_settings = Vec::new();
    match (
        args.randomize_events,
        args.default_opts,
        params.randomize_events,
    ) {
        (_, _, v) if v.len() != 2 => {
            randomize_events_settings.extend(v.clone());
        }
        (None, false, _) => {
            randomize_events_settings.push(false);
            randomize_events_settings.push(true);
        }
        (None, true, _) => {
            randomize_events_settings.push(false);
        }
        (Some(b), _, _) => {
            randomize_events_settings.push(b);
        }
    }

    if schema_path.exists() && events_path.exists() {
        let mut config_paths = Vec::new();
        let mut query_paths = Vec::new();

        for entry in fs::read_dir(&path)? {
            let entry = entry?;
            let path = entry.path();
            if path.is_file() && path.extension().map_or(false, |ext| ext == "btql") {
                if args.query_paths.is_empty()
                    || args
                        .query_paths
                        .iter()
                        .any(|filter| path.canonicalize().unwrap().starts_with(filter))
                {
                    query_paths.push(path);
                }
            } else if path.is_file()
                && path
                    .as_os_str()
                    .to_str()
                    .unwrap()
                    .ends_with("brainstore.yaml")
            {
                if args.config_paths.is_empty()
                    || args
                        .config_paths
                        .iter()
                        .any(|filter| path.canonicalize().unwrap().starts_with(filter))
                {
                    config_paths.push(path);
                }
            }
        }

        let object_id = params.object_id.as_ref();

        // For each *.btql file in the directory, run process_example
        let process_config = move |((c_i, config_path), randomize_events, storage_opts): &(
            (usize, &PathBuf),
            bool,
            StorageTestOpts,
        )|
              -> Result<()> {
            info!("Processing config: {}", config_path.to_str().unwrap());

            // Load config
            let config = Config::from_file(config_path.to_str().unwrap())?;

            let mut events_path = events_path.clone();
            let mut events_process_path = path.join("events_process.jsonl");
            let mut events_insert_path = path.join("events_insert.jsonl");

            // Replace the URIs with files in a temporary directory if use_tmp_dir is true
            let mut config = if *randomize_events
                || storage_opts.small_segments
                || storage_opts.partial_compact
            {
                let (config_dir, config) =
                    create_randomize_dir(config_path, *randomize_events, &storage_opts)?;

                // Only randomize events if we don't explicitly have events_process.jsonl or events_insert.jsonl
                // to carefully control realtime.
                if !(events_process_path.exists() || events_insert_path.exists()) {
                    (events_path, events_process_path, events_insert_path) = args
                        .handle
                        .block_on(randomize_load_events(&config_dir, &events_path, object_id))?;
                }
                config
            } else if args.use_tmp_dir {
                // Create a temporary directory and update URIs
                // This is a placeholder for the actual implementation
                create_temp_dir_and_update_config(config)?
            } else {
                config
            };

            if config.schema.is_none() {
                config.schema = Some(make_object_schema(object_id.object_type)?);
            }

            let schema = config.schema.clone().unwrap();

            // Load data
            let events_path_str = events_path.to_str().unwrap().to_string();
            let config_with_store = ConfigWithStore::from_config(
                config
                    .storage
                    .clone()
                    .expect("Unit test without a schema in config"),
                Default::default(),
            )?;
            clean_index(config_with_store.clone())?;
            args.handle
                .block_on(async move {
                    run_load(
                        config_with_store.clone(),
                        &schema,
                        &[events_path_str],
                        LoadPhase::Compact,
                        &storage_opts,
                        object_id,
                    )
                    .await?;

                    if events_process_path.exists() {
                        run_load(
                            config_with_store.clone(),
                            &schema,
                            &[events_process_path.to_str().unwrap().to_string()],
                            LoadPhase::Process,
                            &storage_opts,
                            object_id,
                        )
                        .await?;
                    }

                    if events_insert_path.exists() {
                        run_load(
                            config_with_store.clone(),
                            &schema,
                            &[events_insert_path.to_str().unwrap().to_string()],
                            LoadPhase::Insert,
                            &storage_opts,
                            object_id,
                        )
                        .await?;
                    }

                    Ok::<(), util::anyhow::Error>(())
                })
                .with_context(|| path.to_string_lossy().to_string())?;

            let run_process_example = |(
                query_path,
                (oo_i, optimizer_opts),
                (io_i, interpreter_opts),
            ): &(
                &PathBuf,
                (usize, OptimizerOpts),
                (usize, InterpreterOpts),
            )|
             -> Result<()> {
                log::info!(
                    "Processing queries at {} with config {}",
                    query_path.to_str().unwrap(),
                    config_path.to_str().unwrap()
                );
                let update = args.update && *c_i == 0 && *oo_i == 0 && *io_i == 0;

                let mut interpreter_opts = interpreter_opts.clone();
                interpreter_opts
                    .index_wal_reader_opts
                    .initial_segment_batch_size = Some(1);

                process_example(
                        &config,
                        &config_path, /* for debugging */
                        &schema_path,
                        query_path,
                        &query_path.with_extension("btql.expected"),
                        update,
                        args.handle.clone(),
                        optimizer_opts.clone(),
                        interpreter_opts.clone(),
                        *randomize_events,
                    )
                    .with_context(|| {
                        format!(
                            "config_path: {:?}, query_path: {:?}, optimizer_opts: {:?}, interpreter_opts: {:?}",
                            config_path, query_path, optimizer_opts, interpreter_opts
                        )
                    })
            };

            let all_opts = iproduct!(
                query_paths.iter(),
                optimizer_opts_settings.clone().into_iter().enumerate(),
                interpreter_opts_settings.clone().into_iter().enumerate(),
            )
            .collect::<Vec<_>>();

            if args.parallel {
                all_opts.par_iter().try_for_each(run_process_example)?;
            } else {
                all_opts.iter().try_for_each(run_process_example)?;
            }

            Ok(())
        };

        let config_opts = iproduct!(
            config_paths.iter().enumerate(),
            randomize_events_settings.clone().into_iter(),
            storage_opts_settings.clone().into_iter(),
        )
        .collect::<Vec<_>>();

        if args.parallel {
            config_opts.par_iter().try_for_each(process_config)?;
        } else {
            config_opts.iter().try_for_each(process_config)?;
        }
    }

    Ok(())
}

fn process_example(
    config: &Config,
    config_path: &Path,
    schema_path: &Path,
    queries_path: &Path,
    expected_path: &Path,
    update: bool,
    handle: Handle,
    optimizer_opts: OptimizerOpts,
    interpreter_opts: InterpreterOpts,
    randomized: bool,
) -> Result<()> {
    let storage_options = config.storage.clone().expect(&format!(
        "Unit test without a schema in config: {}",
        config_path.to_str().unwrap(),
    ));
    let schema = config.schema.clone().expect(&format!(
        "Unit test without a schema in config: {}",
        config_path.to_str().unwrap(),
    ));
    let config_with_store = ConfigWithStore::from_config(storage_options, Default::default())?;

    let full_schema = make_full_schema(&schema)?;

    set_override_always_trace(true);

    // Read and run queries.
    // TODO: Do a streaming parse,
    let queries = fs::read_to_string(queries_path)?;
    let mut results = Vec::new();
    let mut binder = BindRepl::new(&schema_path.to_str().unwrap())?;
    for query_str in queries.split(';') {
        let query_str = query_str.trim();
        if query_str.is_empty() {
            continue;
        }
        let assertions = parse_assertions(query_str)?;
        let result: Result<Vec<Value>> = (|| {
            let query_str = query_str.trim();
            // Bind query
            let bound_query = binder.bind(query_str)?;

            let expr_ctx = ExprContext::new(interpreter_opts.tz_offset);

            let optimized_query = crate::optimizer::optimize(
                &full_schema,
                &bound_query,
                optimizer_opts.clone(),
                &expr_ctx,
            )?;

            // Plan and interpret query
            let plan_ctx = PlanContext::new(full_schema.clone())?;
            let plan = plan(&plan_ctx, &optimized_query)?;
            let mut ctx = InterpreterContext::new(
                config_with_store.clone(),
                full_schema.clone(),
                Arc::new(tantivy::Executor::SingleThread),
                handle.clone(),
                interpreter_opts.clone(),
                expr_ctx,
                None,
                None,
            );
            Arc::get_mut(&mut ctx)
                .unwrap()
                .testing_set_inference_collapse_numbers();

            let mut result = plan.interpret(ctx.clone())?;

            let mut rows: Vec<Value> = handle.block_on(async move {
                let mut rows = Vec::new();
                while let Some(row) = result.next().await {
                    rows.push(row?);
                }
                Ok::<Vec<Value>, InterpreterError>(rows)
            })?;

            rows = rows
                .into_iter()
                .map(normalize_value_for_comparison)
                .collect();

            // _xact_id sorts are not guaranteed to distinguish each row, so we need to sort them.
            if bound_query.sort.len() == 0 || is_xact_id_sort(&bound_query.sort) {
                rows = sort_values(rows);
            }

            let optimizer_json = serde_json::to_value(&optimized_query)?;
            let execution_tree = ctx.finish().into_tree();
            let execution_plan_json = serde_json::to_value(&execution_tree)?;

            if !optimizer_opts.no_pushdown {
                for assertion in assertions {
                    match assertion.phase {
                        AssertionPhase::Optimizer => {
                            let res = run_jaq(&assertion.assertion, &optimizer_json)?;
                            log::info!("produced: {:?}", res);
                            let res_bool: bool = res
                                .cast()
                                .expect("assertion should return a boolean-able value");
                            if !res_bool {
                                panic!(
                                    "\nquery: {}\n-- assertion failed: {} with config {} ({:?} {:?})",
                                    query_str,
                                    assertion.description.unwrap_or(assertion.assertion),
                                    config_path.to_str().unwrap(),
                                    optimizer_opts,
                                    interpreter_opts,
                                );
                            }
                        }
                        AssertionPhase::Execution => {
                            let res = run_jaq(&assertion.assertion, &execution_plan_json)?;
                            log::info!("produced: {:?}", res);
                            let res_bool: bool = res
                                .cast()
                                .expect("assertion should return a boolean-able value");
                            if !res_bool {
                                panic!(
                                    "\nquery: {}\n-- assertion failed: {} with config {} ({:?} {:?})",
                                    query_str,
                                    assertion.description.unwrap_or(assertion.assertion),
                                    config_path.to_str().unwrap(),
                                    optimizer_opts,
                                    interpreter_opts,
                                );
                            }
                        }
                        AssertionPhase::Result => {
                            let res = run_jaq(&assertion.assertion, &Value::Array(rows.clone()))?;
                            log::info!("produced: {:?}", res);
                            let res_bool: bool = res
                                .cast()
                                .expect("assertion should return a boolean-able value");
                            if !res_bool {
                                panic!(
                                    "\nquery: {}\n-- assertion failed: {} with config {} ({:?} {:?})",
                                    query_str,
                                    assertion.description.unwrap_or(assertion.assertion),
                                    config_path.to_str().unwrap(),
                                    optimizer_opts,
                                    interpreter_opts,
                                );
                            }
                        }
                    }
                }
            }

            Ok(rows)
        })();

        results.push(match result {
            Ok(rows) => SnapshotEntry {
                query: query_str.to_string(),
                result_rows: rows,
                error: None,
                skip: false,
            },
            Err(e) => SnapshotEntry {
                query: query_str.to_string(),
                result_rows: vec![],
                error: Some(e.to_string()),

                skip: matches!(
                    e.downcast_ref::<OptimizerError>(),
                    Some(OptimizerError::Unsupported { .. })
                ) || matches!(
                    e.downcast_ref::<PlannerError>(),
                    Some(PlannerError::Unsupported { .. })
                ) || matches!(
                    e.downcast_ref::<InterpreterError>(),
                    Some(InterpreterError::Unsupported { .. })
                ),
            },
        });
    }

    if !expected_path.exists() || update {
        let mut file = fs::File::create(expected_path)?;
        let json_results: Vec<serde_json::Value> = results
            .into_iter()
            .map(|entry| {
                entry.normalize(NormalizeOpts {
                    strip_pagination_key: randomized,
                })
            })
            .map(|entry| serde_json::json!(entry))
            .collect();
        serde_json::to_writer_pretty(&mut file, &json_results)?;
        writeln!(file)?;
    } else {
        let expected_results = fs::read_to_string(expected_path)?;
        let expected_entries: Vec<SnapshotEntry> = serde_json::from_str(&expected_results)?;

        if expected_entries.len() != results.len() {
            panic!(
                "expected_entries.len() != results.len(). Perhaps {} needs to be updated?",
                expected_path.to_str().unwrap()
            );
        }

        let mut expected_entries_iter = expected_entries.iter();
        for result in results {
            let expected_entry = expected_entries_iter.next().unwrap().clone();
            if result.skip || expected_entry.skip {
                continue;
            }
            let expected_entry = expected_entry.normalize(NormalizeOpts {
                strip_pagination_key: randomized,
            });
            let result = result.normalize(NormalizeOpts {
                strip_pagination_key: randomized,
            });
            similar_asserts::assert_eq!(
                expected_entry,
                result,
                "\nquery: {}\n-- while comparing results for {} via {} with config {} ({:?} {:?} randomized={})",
                result.query,
                queries_path.to_str().unwrap(),
                expected_path.to_str().unwrap(),
                config_path.to_str().unwrap(),
                optimizer_opts,
                interpreter_opts,
                randomized
            );
        }
    }

    Ok(())
}

const FLOAT_PRECISION_DIGITS: i32 = 6;

fn round_number_value(value: Value) -> Value {
    if let Value::Number(num) = value {
        if !num.is_f64() {
            return Value::Number(num);
        }
        let scaling_factor = 10f64.powi(FLOAT_PRECISION_DIGITS);
        let num_f64 = num
            .as_f64()
            .map(|n| (n * scaling_factor).round() / scaling_factor);
        let num_i64 = num_f64.map(|n| (n as i64) as f64);
        match (num_i64, num_f64) {
            (Some(n), Some(f)) if n == f => Value::Number((f as i64).into()),
            (_, Some(f)) => Value::Number(serde_json::Number::from_f64(f).unwrap_or(num)),
            _ => Value::Number(num),
        }
    } else {
        value
    }
}

pub fn round_value(value: Value) -> Value {
    match value {
        Value::Object(map) => {
            let mut rounded_map = serde_json::Map::new();
            for (key, value) in map {
                rounded_map.insert(key, round_value(value));
            }
            Value::Object(rounded_map)
        }
        Value::Array(arr) => Value::Array(arr.into_iter().map(round_value).collect()),
        Value::Number(num) => round_number_value(Value::Number(num)),
        _ => value,
    }
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
struct SnapshotEntry {
    query: String,
    result_rows: Vec<Value>,
    error: Option<String>,
    #[serde(default)]
    skip: bool,
}

struct NormalizeOpts {
    strip_pagination_key: bool,
}

impl SnapshotEntry {
    fn normalize(self, opts: NormalizeOpts) -> Self {
        SnapshotEntry {
            result_rows: Self::normalize_rows(self.result_rows, opts),
            ..self
        }
    }

    fn normalize_rows(rows: Vec<Value>, opts: NormalizeOpts) -> Vec<Value> {
        let normalized_rows = rows
            .into_iter()
            .map(round_value)
            .map(normalize_value_for_comparison)
            .map(|mut v| {
                if opts.strip_pagination_key {
                    let obj = v.as_object_mut();
                    if let Some(obj) = obj {
                        obj.remove(PAGINATION_KEY_FIELD);
                    }
                    v
                } else {
                    v
                }
            })
            .map(|v| match v {
                Value::Object(mut map) => {
                    match map.entry("top_values".to_string()) {
                        serde_json::map::Entry::Occupied(mut v) => {
                            v.insert(Value::Array(sort_values(
                                v.get().as_array().unwrap().iter().cloned().collect(),
                            )));
                        }
                        serde_json::map::Entry::Vacant(_) => {}
                    }

                    Value::Object(map)
                }
                _ => v,
            })
            .collect();

        if opts.strip_pagination_key {
            // This will affect the sort order, so we need to sort again.
            sort_values(normalized_rows)
        } else {
            normalized_rows
        }
    }
}

fn sort_values(values: Vec<Value>) -> Vec<Value> {
    let mut sorted_values = values;
    sorted_values.sort_by_key(|a| {
        let a_sorted = sort_json_value(a);
        a_sorted.to_string()
    });
    sorted_values
}

fn sort_json_value(value: &Value) -> Value {
    match value {
        Value::Object(map) => {
            let mut sorted_map = serde_json::Map::new();
            let mut keys: Vec<&String> = map.keys().collect();
            keys.sort();
            for key in keys {
                let sorted_value = sort_json_value(map.get(key).unwrap());
                sorted_map.insert(key.clone(), sorted_value);
            }
            Value::Object(sorted_map)
        }
        Value::Array(arr) => {
            let mut sorted_arr: Vec<Value> = arr.iter().map(sort_json_value).collect();
            sorted_arr.sort_by(|a, b| a.to_string().cmp(&b.to_string()));
            Value::Array(sorted_arr)
        }
        _ => value.clone(),
    }
}

// Helper function to create a temporary directory and update the config
fn create_temp_dir_and_update_config(mut config: Config) -> Result<Config> {
    // Create a temporary directory
    let temp_dir = tempdir()?;

    let wal_uri = str_to_url(
        &format!("file://{}/wal", temp_dir.path().to_str().unwrap()),
        None,
    )?;
    config.storage = Some(StorageConfig {
        index_uri: str_to_url(
            &format!("file://{}/index", temp_dir.path().to_str().unwrap()),
            None,
        )?,
        wal_uri: wal_uri.clone(),
        realtime_wal_uri: Some(wal_uri),
        metadata_uri: str_to_url(
            &format!("file://{}/metadata", temp_dir.path().to_str().unwrap()),
            None,
        )?,
        locks_uri: Url::from_str("memory://").unwrap(),
        locks_manager_enable_bookkeeping: false,
        xact_manager_uri: Url::from_str("memory://").unwrap(),
    });

    Ok(config)
}

fn create_randomize_dir(
    config_path: &Path,
    randomize: bool,
    storage_opts: &StorageTestOpts,
) -> Result<(PathBuf, Config)> {
    let config_str = fs::read_to_string(config_path)
        .with_context(|| format!("Failed to open config file: {:?}", config_path))?;
    let mut config_file: ConfigFile = if config_path.ends_with(".json") {
        serde_json::from_str(&config_str)
            .with_context(|| format!("Failed to parse json config file: {:?}", config_path))?
    } else {
        serde_yaml::from_str(&config_str)
            .with_context(|| format!("Failed to parse yaml config file: {:?}", config_path))?
    };

    // Hash the config
    let mut hasher = Sha256::new();
    hasher.update(serde_json::to_string(&config_file).unwrap().as_bytes());
    hasher.update(serde_json::to_string(&randomize).unwrap().as_bytes());
    hasher.update(serde_json::to_string(&storage_opts).unwrap().as_bytes());
    let config_hash = format!("{:x}", hasher.finalize())
        .drain(0..16)
        .collect::<String>();

    let config_dir = config_path
        .parent()
        .unwrap()
        .join("index")
        .join(config_hash);
    std::fs::create_dir_all(&config_dir)?;

    let wal_uri = "./data/wal".to_string();
    config_file.storage = Some(StorageConfigFile {
        index_uri: "./data/index".to_string(),
        wal_uri: wal_uri.clone(),
        realtime_wal_uri: Some(wal_uri),
        metadata_uri: "./data/metadata".to_string(),
        locks_uri: None,
        locks_manager_enable_bookkeeping: None,
        xact_manager_uri: None,
    });

    let config_file_path = config_dir.join("brainstore.yaml");
    std::fs::write(&config_file_path, serde_yaml::to_string(&config_file)?)?;

    let config = Config::from_file(config_file_path.to_str().unwrap())?;

    Ok((config_dir, config))
}

async fn randomize_load_events(
    dir: &Path,
    events_path: &Path,
    default_object_id: FullObjectId<'_>,
) -> Result<(PathBuf, PathBuf, PathBuf)> {
    let events_path_string = [events_path.to_str().unwrap().to_string()];
    let mut stream = storage::file_stream::open(&events_path_string, 1000, vec![]).await?;
    let mut values = Vec::new();
    while let Some(value) = stream.next().await {
        let value = value?;
        values.push(fill_default_object_id(value, default_object_id));
    }

    let mut rng = rand::thread_rng();

    let mut original_xact_ids = Vec::new();

    let xact_0 = "1000000000000000000";
    let xact_1 = "1000000000000000001";
    let xact_2 = "1000000000000000002";

    // If the rows do not have ids, add a random one
    for value in values.iter_mut() {
        match value {
            Value::Object(o) => {
                o.entry("id").or_insert_with(|| Value::String(new_id()));

                match o.entry("_xact_id") {
                    serde_json::map::Entry::Occupied(mut v) => {
                        original_xact_ids.push(v.get().clone());
                        v.insert(Value::String(xact_0.to_string()));
                    }
                    serde_json::map::Entry::Vacant(e) => {
                        e.insert(Value::String(xact_0.to_string()));
                    }
                }
            }
            _ => bail!("expected object"),
        }
    }

    let base_events_path = dir.join("events.jsonl");
    let process_events_path = dir.join("events_process.jsonl");
    let insert_events_path = dir.join("events_insert.jsonl");

    let base_values = values
        .iter()
        .map(|v| serde_json::to_string(v).context("failed to serialize"))
        .collect::<Result<Vec<_>>>()?;

    // Randomly prefix the base values
    std::fs::write(
        &base_events_path,
        base_values[..rng.gen_range(1..=base_values.len())].join("\n"),
    )
    .context("failed to write")?;

    // Then, for each base value, randomly change some of its values, and increment the transaction number
    let process_values = values
        .iter()
        .map(|v| {
            let mut v = v.clone();
            let o = v.as_object_mut().unwrap();
            o.insert("_xact_id".to_string(), Value::String(xact_1.to_string()));

            let keys = o
                .keys()
                .filter(|k| {
                    *k != "id"
                        && *k != "_xact_id"
                        && *k != "_object_type"
                        && *k != "_object_id"
                        && *k != "span_id"
                        && *k != "root_span_id"
                        && *k != "span_parents"
                        && *k != "created"
                })
                .map(|k| k.clone())
                .collect::<Vec<_>>();
            for k in keys {
                if rng.gen_bool(0.5) {
                    o.insert(k, Value::Null);
                }
            }

            v
        })
        .collect::<Vec<_>>();

    std::fs::write(
        &process_events_path,
        process_values
            .iter()
            .map(|v| serde_json::to_string(v).context("failed to serialize"))
            .collect::<Result<Vec<_>>>()?
            .join("\n"),
    )?;

    // Finally, insert the original values, but with transaction id 2
    let insert_values = values
        .iter()
        .enumerate()
        .map(|(i, v)| {
            let mut v = v.clone();
            let o = v.as_object_mut().unwrap();
            if original_xact_ids.is_empty() {
                o.insert("_xact_id".to_string(), Value::String(xact_2.to_string()));
            } else {
                o.insert("_xact_id".to_string(), original_xact_ids[i].clone());
            }
            v
        })
        .collect::<Vec<_>>();

    std::fs::write(
        &insert_events_path,
        insert_values
            .iter()
            .map(|v| serde_json::to_string(v).context("failed to serialize"))
            .collect::<Result<Vec<_>>>()?
            .join("\n"),
    )?;

    Ok((base_events_path, process_events_path, insert_events_path))
}

fn clean_index(config: ConfigWithStore) -> Result<()> {
    // If the directory path exists, delete it
    config.index.remove_local()?;
    config.wal.remove_local()?;
    if let Some(metadata) = &config.global_store_info {
        metadata.remove_local()?;
    }

    Ok(())
}

enum LoadPhase {
    Insert,
    Process,
    Compact,
}

async fn run_load(
    config: ConfigWithStore,
    schema: &util::schema::Schema,
    paths: &[String],
    phase: LoadPhase,
    storage_opts: &StorageTestOpts,
    default_object_id: FullObjectId<'_>,
) -> Result<()> {
    let wal = &config.wal;
    let global_store = &config.global_store;
    let locks_manager = &config.locks_manager;

    let mut stream = storage::file_stream::open(paths, 1000, vec![]).await?;
    let mut values = Vec::new();
    while let Some(value) = stream.next().await {
        let value = value?;
        values.push(fill_default_object_id(value, default_object_id));
    }

    let xact_manager = MemoryTransactionManager::default();

    wal_insert_unnormalized(wal.as_ref(), &**global_store, values, &xact_manager).await?;

    if matches!(phase, LoadPhase::Insert) {
        return Ok(());
    }

    let process_wal_output = storage::process_wal::process_object_wal(
        ProcessObjectWalInput {
            object_id: default_object_id,
            config: &config,
        },
        Default::default(),
        ProcessObjectWalOptions {
            max_rows_per_segment: if storage_opts.small_segments {
                1
            } else {
                1_000_000
            },
            wal_stream_options: ProcessObjectWalStreamOptions {
                process_object_wal_target_batch_size: if storage_opts.small_segments {
                    1
                } else {
                    ProcessObjectWalStreamOptions::default().process_object_wal_target_batch_size
                },
                ..Default::default()
            },
            ..Default::default()
        },
    )
    .await?;

    if matches!(phase, LoadPhase::Process) {
        return Ok(());
    }

    let to_compact = if storage_opts.partial_compact {
        process_wal_output
            .modified_segment_ids
            .iter()
            .take(2)
            .cloned()
            .collect()
    } else {
        process_wal_output.modified_segment_ids.clone()
    };

    // Compact all the modified segment WALs.
    let full_schema = make_full_schema(schema)?;
    join_all(to_compact.iter().map(|segment_id| async {
        storage::process_wal::compact_segment_wal(
            CompactSegmentWalInput {
                segment_id: *segment_id,
                index_store: config.index.clone(),
                schema: full_schema.clone(),
                global_store: global_store.clone(),
                locks_manager: &**locks_manager,
            },
            Default::default(),
            CompactSegmentWalOptions::parse_from([""]),
        )
        .await?;
        Ok(())
    }))
    .await
    .into_iter()
    .collect::<Result<()>>()?;

    Ok(())
}

#[derive(Debug)]
pub enum AssertionPhase {
    Optimizer,
    Execution,
    Result,
}

/// Assertions allow us to test the optimized plans or even the result sets themselves, and write
/// code that validates they return what we expect.
///
/// You can write an assertion by writing a block comment in front of a query, like
/// ```
/// /*!optimizer -- This is testing that the optimizer works
///    [.. | objects | select(has("TantivySearch"))] | length == 1
/// */
/// ```
///
/// To test optimizer assertions locally, you can use the `--stage optimize` flag.
/// ```bash
/// brainstore btql query "select: * | from: dataset('singleton') | filter: _pagination_key < 'p00000000000006815752' and _pagination_key > 'p00000000000006815752'" --stage optimize | jq ' [.. | objects | select(has("TantivySearch"))] | length == 1"
/// ```
#[derive(Debug)]
struct Assertion {
    phase: AssertionPhase,
    assertion: String,
    description: Option<String>,
}

fn parse_assertions(query: &str) -> Result<Vec<Assertion>> {
    let mut assertions = Vec::new();

    // Regular expression to match assertion comments
    // Matches /*!phase -- description\n assertion \n*/
    let re = regex::Regex::new(r"(?s)/\*!(.*?)\*/").unwrap();

    for capture in re.captures_iter(query) {
        let content = capture[1].trim();

        // Split the content into parts
        let parts: Vec<&str> = content.splitn(2, "--").collect();

        // Parse the phase
        let phase = match parts.get(0).map(|s| s.trim()) {
            Some("optimizer") => AssertionPhase::Optimizer,
            Some("execution") => AssertionPhase::Execution,
            Some("result") => AssertionPhase::Result,
            Some(unknown) => bail!("Unknown assertion phase: '{}'", unknown),
            None => bail!("Missing assertion phase"),
        };

        // Get description and assertion
        let (description, assertion) = match parts.get(1) {
            Some(rest) => {
                let mut lines = rest.lines();

                // First line is always the description
                let description = lines
                    .next()
                    .map(|s| s.trim())
                    .filter(|s| !s.is_empty())
                    .map(|s| s.to_string());

                // Rest is the assertion
                let assertion = lines
                    .map(|s| s.trim())
                    .filter(|s| !s.is_empty())
                    .collect::<Vec<_>>()
                    .join("\n");

                if assertion.is_empty() {
                    bail!("Empty assertion");
                }

                (description, assertion)
            }
            None => bail!("Missing assertion content"),
        };

        assertions.push(Assertion {
            phase,
            assertion,
            description,
        });
    }

    Ok(assertions)
}

fn is_xact_id_sort(sort: &Vec<SortItem>) -> bool {
    sort.len() == 1
        && match &sort[0].expr {
            SortExpr::Alias(alias) => alias == XACT_ID_FIELD,
            SortExpr::Expr(expr) => match expr.as_ref() {
                Expr::Field(field) => field.name == vec![PathPiece::Key(XACT_ID_FIELD.to_string())],
                _ => false,
            },
        }
}
