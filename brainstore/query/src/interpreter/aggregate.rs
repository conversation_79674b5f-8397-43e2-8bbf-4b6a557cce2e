use std::{borrow::Cow, sync::Arc};

use util::{
    ptree::{MakeP<PERSON>ree, TreeBuilder},
    Value,
};

use btql::schema::ScalarType;
use btql::{
    binder::types::type_rank,
    interpreter::{context::ExprContext, expr::interpret_expr},
};
use serde_json::Number;

use crate::interpreter::error::Result;
use btql::typesystem::cast::{CastFrom, CastInto};
use btql::typesystem::coerce::{cast_to_scalar_type, normalize_to_scalar_type};
use sketches_ddsketch::{Config, DDSketch};

pub trait Aggregator: std::fmt::Debug + Clone + Send + Sync + MakePTree + 'static {
    fn aggregate<'a>(
        &mut self,
        ctx: &ExprContext,
        input: impl IntoIterator<Item = &'a Value>,
    ) -> Result<()>;
    fn combine(&mut self, other: Self) -> Result<()>;

    fn collect(&self) -> Result<util::Value>;

    fn aggregate_value(&mut self, ctx: &ExprContext, value: &Value) -> Result<()> {
        self.aggregate(ctx, [value])
    }
}

// This enum allows us to have multiple aggregators in a single struct without using
// a trait object (which must _not_ have any Sized bounds, and therefore cannot really
// accept generic parameters).
#[derive(Debug, Clone)]
pub enum DynamicAggregator {
    ConstantCount(ConstantCount),
    ExprCount(ExprCount),
    IntSum(IntSum),
    FloatSum(FloatSum),
    IntAvg(IntAvg),
    FloatAvg(FloatAvg),
    NullMin(Min<()>),
    BoolMin(Min<bool>),
    IntMin(Min<i128>),
    FloatMin(Min<f64>),
    StringMin(Min<String>),
    JsonMin(JsonMin),
    UnknownMin(UnknownMin),
    NullMax(Max<()>),
    BoolMax(Max<bool>),
    IntMax(Max<i128>),
    FloatMax(Max<f64>),
    StringMax(Max<String>),
    JsonMax(JsonMax),
    UnknownMax(UnknownMax),
    Percentile(Percentile),
}

impl Aggregator for DynamicAggregator {
    fn aggregate<'a>(
        &mut self,
        ctx: &ExprContext,
        input: impl IntoIterator<Item = &'a Value>,
    ) -> Result<()> {
        match self {
            DynamicAggregator::ConstantCount(agg) => agg.aggregate(ctx, input),
            DynamicAggregator::ExprCount(agg) => agg.aggregate(ctx, input),
            DynamicAggregator::IntSum(agg) => agg.aggregate(ctx, input),
            DynamicAggregator::FloatSum(agg) => agg.aggregate(ctx, input),
            DynamicAggregator::IntAvg(agg) => agg.aggregate(ctx, input),
            DynamicAggregator::FloatAvg(agg) => agg.aggregate(ctx, input),
            DynamicAggregator::NullMin(agg) => agg.aggregate(ctx, input),
            DynamicAggregator::BoolMin(agg) => agg.aggregate(ctx, input),
            DynamicAggregator::IntMin(agg) => agg.aggregate(ctx, input),
            DynamicAggregator::FloatMin(agg) => agg.aggregate(ctx, input),
            DynamicAggregator::StringMin(agg) => agg.aggregate(ctx, input),
            DynamicAggregator::UnknownMin(agg) => agg.aggregate(ctx, input),
            DynamicAggregator::NullMax(agg) => agg.aggregate(ctx, input),
            DynamicAggregator::BoolMax(agg) => agg.aggregate(ctx, input),
            DynamicAggregator::IntMax(agg) => agg.aggregate(ctx, input),
            DynamicAggregator::FloatMax(agg) => agg.aggregate(ctx, input),
            DynamicAggregator::StringMax(agg) => agg.aggregate(ctx, input),
            DynamicAggregator::UnknownMax(agg) => agg.aggregate(ctx, input),
            DynamicAggregator::JsonMin(agg) => agg.aggregate(ctx, input),
            DynamicAggregator::JsonMax(agg) => agg.aggregate(ctx, input),
            DynamicAggregator::Percentile(agg) => agg.aggregate(ctx, input),
        }
    }

    fn combine(&mut self, other: Self) -> Result<()> {
        match (self, other) {
            (DynamicAggregator::ConstantCount(a), DynamicAggregator::ConstantCount(b)) => {
                a.combine(b)
            }
            (DynamicAggregator::ExprCount(a), DynamicAggregator::ExprCount(b)) => a.combine(b),
            (DynamicAggregator::IntSum(a), DynamicAggregator::IntSum(b)) => a.combine(b),
            (DynamicAggregator::FloatSum(a), DynamicAggregator::FloatSum(b)) => a.combine(b),
            (DynamicAggregator::IntAvg(a), DynamicAggregator::IntAvg(b)) => a.combine(b),
            (DynamicAggregator::FloatAvg(a), DynamicAggregator::FloatAvg(b)) => a.combine(b),
            (DynamicAggregator::NullMin(a), DynamicAggregator::NullMin(b)) => a.combine(b),
            (DynamicAggregator::BoolMin(a), DynamicAggregator::BoolMin(b)) => a.combine(b),
            (DynamicAggregator::IntMin(a), DynamicAggregator::IntMin(b)) => a.combine(b),
            (DynamicAggregator::FloatMin(a), DynamicAggregator::FloatMin(b)) => a.combine(b),
            (DynamicAggregator::StringMin(a), DynamicAggregator::StringMin(b)) => a.combine(b),
            (DynamicAggregator::UnknownMin(a), DynamicAggregator::UnknownMin(b)) => a.combine(b),
            (DynamicAggregator::NullMax(a), DynamicAggregator::NullMax(b)) => a.combine(b),
            (DynamicAggregator::BoolMax(a), DynamicAggregator::BoolMax(b)) => a.combine(b),
            (DynamicAggregator::IntMax(a), DynamicAggregator::IntMax(b)) => a.combine(b),
            (DynamicAggregator::FloatMax(a), DynamicAggregator::FloatMax(b)) => a.combine(b),
            (DynamicAggregator::StringMax(a), DynamicAggregator::StringMax(b)) => a.combine(b),
            (DynamicAggregator::UnknownMax(a), DynamicAggregator::UnknownMax(b)) => a.combine(b),
            (DynamicAggregator::JsonMin(a), DynamicAggregator::JsonMin(b)) => a.combine(b),
            (DynamicAggregator::JsonMax(a), DynamicAggregator::JsonMax(b)) => a.combine(b),
            (DynamicAggregator::Percentile(a), DynamicAggregator::Percentile(b)) => a.combine(b),
            (_, _) => {
                Err(util::anyhow::anyhow!("cannot combine different types of aggregators").into())
            }
        }
    }

    fn collect(&self) -> Result<Value> {
        match self {
            DynamicAggregator::ConstantCount(agg) => agg.collect(),
            DynamicAggregator::ExprCount(agg) => agg.collect(),
            DynamicAggregator::IntSum(agg) => agg.collect(),
            DynamicAggregator::FloatSum(agg) => agg.collect(),
            DynamicAggregator::IntAvg(agg) => agg.collect(),
            DynamicAggregator::FloatAvg(agg) => agg.collect(),
            DynamicAggregator::NullMin(agg) => agg.collect(),
            DynamicAggregator::BoolMin(agg) => agg.collect(),
            DynamicAggregator::IntMin(agg) => agg.collect(),
            DynamicAggregator::FloatMin(agg) => agg.collect(),
            DynamicAggregator::StringMin(agg) => agg.collect(),
            DynamicAggregator::UnknownMin(agg) => agg.collect(),
            DynamicAggregator::NullMax(agg) => agg.collect(),
            DynamicAggregator::BoolMax(agg) => agg.collect(),
            DynamicAggregator::IntMax(agg) => agg.collect(),
            DynamicAggregator::FloatMax(agg) => agg.collect(),
            DynamicAggregator::StringMax(agg) => agg.collect(),
            DynamicAggregator::UnknownMax(agg) => agg.collect(),
            DynamicAggregator::JsonMin(agg) => agg.collect(),
            DynamicAggregator::JsonMax(agg) => agg.collect(),
            DynamicAggregator::Percentile(agg) => agg.collect(),
        }
    }
}

impl MakePTree for DynamicAggregator {
    fn label(&self) -> String {
        match self {
            DynamicAggregator::ConstantCount(c) => c.label(),
            DynamicAggregator::ExprCount(e) => e.label(),
            DynamicAggregator::IntSum(s) => s.label(),
            DynamicAggregator::FloatSum(s) => s.label(),
            DynamicAggregator::IntAvg(s) => s.label(),
            DynamicAggregator::FloatAvg(s) => s.label(),
            DynamicAggregator::NullMin(s) => s.label(),
            DynamicAggregator::BoolMin(s) => s.label(),
            DynamicAggregator::IntMin(s) => s.label(),
            DynamicAggregator::FloatMin(s) => s.label(),
            DynamicAggregator::StringMin(s) => s.label(),
            DynamicAggregator::UnknownMin(s) => s.label(),
            DynamicAggregator::NullMax(s) => s.label(),
            DynamicAggregator::BoolMax(s) => s.label(),
            DynamicAggregator::IntMax(s) => s.label(),
            DynamicAggregator::FloatMax(s) => s.label(),
            DynamicAggregator::StringMax(s) => s.label(),
            DynamicAggregator::UnknownMax(s) => s.label(),
            DynamicAggregator::JsonMin(s) => s.label(),
            DynamicAggregator::JsonMax(s) => s.label(),
            DynamicAggregator::Percentile(s) => s.label(),
        }
    }

    fn make_ptree(&self, builder: &mut TreeBuilder) {
        match self {
            DynamicAggregator::ConstantCount(c) => c.make_ptree(builder),
            DynamicAggregator::ExprCount(e) => e.make_ptree(builder),
            DynamicAggregator::IntSum(s) => s.make_ptree(builder),
            DynamicAggregator::FloatSum(s) => s.make_ptree(builder),
            DynamicAggregator::IntAvg(s) => s.make_ptree(builder),
            DynamicAggregator::FloatAvg(s) => s.make_ptree(builder),
            DynamicAggregator::NullMin(s) => s.make_ptree(builder),
            DynamicAggregator::BoolMin(s) => s.make_ptree(builder),
            DynamicAggregator::IntMin(s) => s.make_ptree(builder),
            DynamicAggregator::FloatMin(s) => s.make_ptree(builder),
            DynamicAggregator::StringMin(s) => s.make_ptree(builder),
            DynamicAggregator::UnknownMin(s) => s.make_ptree(builder),
            DynamicAggregator::NullMax(s) => s.make_ptree(builder),
            DynamicAggregator::BoolMax(s) => s.make_ptree(builder),
            DynamicAggregator::IntMax(s) => s.make_ptree(builder),
            DynamicAggregator::FloatMax(s) => s.make_ptree(builder),
            DynamicAggregator::StringMax(s) => s.make_ptree(builder),
            DynamicAggregator::UnknownMax(s) => s.make_ptree(builder),
            DynamicAggregator::JsonMin(s) => s.make_ptree(builder),
            DynamicAggregator::JsonMax(s) => s.make_ptree(builder),
            DynamicAggregator::Percentile(s) => s.make_ptree(builder),
        }
    }
}

#[derive(Debug, Clone, Default)]
pub struct ConstantCount(u64);

impl Aggregator for ConstantCount {
    fn aggregate<'a>(
        &mut self,
        _ctx: &ExprContext,
        input: impl IntoIterator<Item = &'a Value>,
    ) -> Result<()> {
        self.0 += input.into_iter().count() as u64;
        Ok(())
    }

    fn combine(&mut self, other: Self) -> Result<()> {
        self.0 += other.0;
        Ok(())
    }

    fn collect(&self) -> Result<Value> {
        Ok(self.0.into())
    }
}

impl MakePTree for ConstantCount {
    fn label(&self) -> String {
        "Constant count".to_string()
    }

    fn make_ptree(&self, _builder: &mut TreeBuilder) {}
}

#[derive(Debug, Clone)]
pub struct ExprCount {
    expr: Arc<btql::binder::ast::Expr>,
    count: u64,
}

impl ExprCount {
    pub fn new(expr: &btql::binder::ast::Expr) -> Self {
        Self {
            expr: Arc::new(expr.clone()),
            count: 0,
        }
    }
}

impl Aggregator for ExprCount {
    fn aggregate<'a>(
        &mut self,
        ctx: &ExprContext,
        input: impl IntoIterator<Item = &'a Value>,
    ) -> Result<()> {
        let values = input
            .into_iter()
            .map(|v| Cow::Borrowed(v))
            .collect::<Vec<_>>();
        let result = interpret_expr(ctx, &self.expr, &values)?;
        self.count += result
            .into_iter()
            .filter(|v| !matches!(v.as_ref(), Value::Null))
            .count() as u64;
        Ok(())
    }

    fn combine(&mut self, other: Self) -> Result<()> {
        self.count += other.count;
        Ok(())
    }

    fn collect(&self) -> Result<Value> {
        Ok(self.count.into())
    }
}

impl MakePTree for ExprCount {
    fn label(&self) -> String {
        "Expr count".to_string()
    }

    fn make_ptree(&self, builder: &mut TreeBuilder) {
        self.add_child(builder, self.expr.as_ref());
    }
}

#[derive(Debug, Clone)]
pub struct IntSum {
    expr: Arc<btql::binder::ast::Expr>,
    sum: i128,
    has_value: bool,
}

impl IntSum {
    pub fn new(expr: &btql::binder::ast::Expr) -> Self {
        Self {
            expr: Arc::new(expr.clone()),
            sum: 0,
            has_value: false,
        }
    }
}

impl Aggregator for IntSum {
    fn aggregate<'a>(
        &mut self,
        ctx: &ExprContext,
        input: impl IntoIterator<Item = &'a Value>,
    ) -> Result<()> {
        let values = input
            .into_iter()
            .map(|v| Cow::Borrowed(v))
            .collect::<Vec<_>>();
        let result = interpret_expr(ctx, &self.expr, &values)?;

        // Pre-allocate vector with capacity
        let mut numbers = Vec::with_capacity(result.len());

        // First pass: collect valid numbers
        for v in result.iter() {
            if let Value::Number(n) = v.as_ref() {
                if let Some(i) = n.as_i64() {
                    numbers.push(i as i128);
                } else if let Some(u) = n.as_u64() {
                    numbers.push(u as i128);
                } else {
                    return Err(util::anyhow::anyhow!("Invalid integer in sum: {}", v).into());
                }
            }
        }

        if !numbers.is_empty() {
            self.has_value = true;
            self.sum += numbers.iter().sum::<i128>();
        }

        Ok(())
    }

    fn combine(&mut self, other: Self) -> Result<()> {
        self.sum += other.sum;
        self.has_value = self.has_value || other.has_value;
        Ok(())
    }

    fn collect(&self) -> Result<Value> {
        if !self.has_value {
            return Ok(Value::Null);
        }

        if let Ok(i) = i64::try_from(self.sum) {
            return Ok(Value::Number(serde_json::Number::from(i)));
        }

        if let Ok(u) = u64::try_from(self.sum) {
            return Ok(Value::Number(serde_json::Number::from(u)));
        }

        Err(util::anyhow::anyhow!(
            "Integer sum {} is too large to represent as a number",
            self.sum
        )
        .into())
    }
}

impl MakePTree for IntSum {
    fn label(&self) -> String {
        "Int sum".to_string()
    }

    fn make_ptree(&self, builder: &mut TreeBuilder) {
        self.add_child(builder, self.expr.as_ref());
    }
}

#[inline(always)]
pub fn add_kahan(current_sum: f64, current_compensation: f64, value: f64) -> (f64, f64) {
    let y = value - current_compensation;
    let t = current_sum + y;
    let new_compensation = (t - current_sum) - y;
    (t, new_compensation)
}

#[derive(Debug, Clone)]
pub struct FloatSum {
    expr: Arc<btql::binder::ast::Expr>,
    sum: f64,
    compensation: f64,
    has_value: bool,
}

impl FloatSum {
    pub fn new(expr: &btql::binder::ast::Expr) -> Self {
        Self {
            expr: Arc::new(expr.clone()),
            sum: 0.0,
            compensation: 0.0,
            has_value: false,
        }
    }
}

impl Aggregator for FloatSum {
    fn aggregate<'a>(
        &mut self,
        ctx: &ExprContext,
        input: impl IntoIterator<Item = &'a Value>,
    ) -> Result<()> {
        let values: Vec<_> = input.into_iter().map(|v| Cow::Borrowed(v)).collect();
        let result = interpret_expr(ctx, &self.expr, &values)?;

        for value in result {
            let n = match value.as_ref() {
                Value::Number(n) => match n.as_f64() {
                    Some(f) if f.is_finite() => f,
                    _ => continue,
                },
                _ => continue,
            };

            (self.sum, self.compensation) = add_kahan(self.sum, self.compensation, n);
            self.has_value = true;
        }
        Ok(())
    }

    fn combine(&mut self, other: Self) -> Result<()> {
        (self.sum, self.compensation) = {
            let (new_sum, new_comp) = add_kahan(self.sum, self.compensation, other.sum);
            add_kahan(new_sum, new_comp, other.compensation)
        };
        self.has_value = self.has_value || other.has_value;
        Ok(())
    }

    fn collect(&self) -> Result<Value> {
        if !self.has_value {
            return Ok(Value::Null);
        }
        let final_sum = self.sum + self.compensation;
        Ok(Value::Number(
            serde_json::Number::from_f64(final_sum).unwrap_or(serde_json::Number::from(0)),
        ))
    }
}

impl MakePTree for FloatSum {
    fn label(&self) -> String {
        "Float sum".to_string()
    }

    fn make_ptree(&self, builder: &mut TreeBuilder) {
        self.add_child(builder, self.expr.as_ref());
    }
}

#[derive(Debug, Clone)]
pub struct IntAvg {
    expr: Arc<btql::binder::ast::Expr>,
    sum: i128,
    count: usize,
}

impl IntAvg {
    pub fn new(expr: &btql::binder::ast::Expr) -> Self {
        Self {
            expr: Arc::new(expr.clone()),
            sum: 0,
            count: 0,
        }
    }
}

impl Aggregator for IntAvg {
    fn aggregate<'a>(
        &mut self,
        ctx: &ExprContext,
        input: impl IntoIterator<Item = &'a Value>,
    ) -> Result<()> {
        let values: Vec<_> = input.into_iter().map(|v| Cow::Borrowed(v)).collect();
        let result = interpret_expr(ctx, &self.expr, &values)?;

        for value in result {
            if let Value::Number(n) = value.as_ref() {
                if let Some(i) = n.as_i64() {
                    self.sum += i as i128;
                    self.count += 1;
                } else if let Some(u) = n.as_u64() {
                    self.sum += u as i128;
                    self.count += 1;
                } else {
                    return Err(
                        util::anyhow::anyhow!("Invalid integer in average: {}", value).into(),
                    );
                }
            }
        }
        Ok(())
    }

    fn combine(&mut self, other: Self) -> Result<()> {
        self.sum += other.sum;
        self.count += other.count;
        Ok(())
    }

    fn collect(&self) -> Result<Value> {
        if self.count == 0 {
            return Ok(Value::Null);
        }
        let avg = (self.sum as f64) / (self.count as f64);
        Ok(Value::Number(
            serde_json::Number::from_f64(avg).unwrap_or(serde_json::Number::from(0)),
        ))
    }
}

impl MakePTree for IntAvg {
    fn label(&self) -> String {
        "Int avg".to_string()
    }

    fn make_ptree(&self, builder: &mut TreeBuilder) {
        self.add_child(builder, self.expr.as_ref());
    }
}

#[derive(Debug, Clone)]
pub struct FloatAvg {
    expr: Arc<btql::binder::ast::Expr>,
    sum: f64,
    compensation: f64,
    count: usize,
}

impl FloatAvg {
    pub fn new(expr: &btql::binder::ast::Expr) -> Self {
        Self {
            expr: Arc::new(expr.clone()),
            sum: 0.0,
            compensation: 0.0,
            count: 0,
        }
    }

    // Unfortunately this has to be duplicated with the logic in aggregate() below, because the borrow checker
    // gets confused.
    #[inline(always)]
    pub fn add_value(&mut self, value: f64) {
        (self.sum, self.compensation) = add_kahan(self.sum, self.compensation, value);
        self.count += 1;
    }
}

impl Aggregator for FloatAvg {
    fn aggregate<'a>(
        &mut self,
        ctx: &ExprContext,
        input: impl IntoIterator<Item = &'a Value>,
    ) -> Result<()> {
        let values: Vec<_> = input.into_iter().map(|v| Cow::Borrowed(v)).collect();
        let result = interpret_expr(ctx, &self.expr, &values)?;

        for value in result {
            let n = match value.as_ref() {
                Value::Number(n) => match n.as_f64() {
                    Some(f) if f.is_finite() => f,
                    _ => continue,
                },
                _ => continue,
            };

            (self.sum, self.compensation) = add_kahan(self.sum, self.compensation, n);
            self.count += 1;
        }
        Ok(())
    }

    fn combine(&mut self, other: Self) -> Result<()> {
        (self.sum, self.compensation) = {
            let (new_sum, new_comp) = add_kahan(self.sum, self.compensation, other.sum);
            add_kahan(new_sum, new_comp, other.compensation)
        };
        self.count += other.count;
        Ok(())
    }

    fn collect(&self) -> Result<Value> {
        if self.count == 0 {
            return Ok(Value::Null);
        }
        let final_sum = self.sum + self.compensation;
        let avg = final_sum / self.count as f64;
        Ok(Value::Number(
            serde_json::Number::from_f64(avg).unwrap_or(serde_json::Number::from(0)),
        ))
    }
}

impl MakePTree for FloatAvg {
    fn label(&self) -> String {
        "Float avg".to_string()
    }

    fn make_ptree(&self, builder: &mut TreeBuilder) {
        self.add_child(builder, self.expr.as_ref());
    }
}

pub trait MinMaxValue:
    Clone
    + PartialOrd
    + Send
    + Sync
    + std::fmt::Debug
    + 'static
    + CastFrom<util::serde_json::Value>
    + CastInto<util::Value>
{
}

impl MinMaxValue for () {}
impl MinMaxValue for bool {}
impl MinMaxValue for i128 {}
impl MinMaxValue for f64 {}
impl MinMaxValue for String {}

#[derive(Debug, Clone)]
pub struct Min<T: MinMaxValue> {
    expr: Arc<btql::binder::ast::Expr>,
    min_value: Option<T>,
}

impl<T: MinMaxValue> Min<T> {
    pub fn new(expr: &btql::binder::ast::Expr) -> Self {
        Self {
            expr: Arc::new(expr.clone()),
            min_value: None,
        }
    }
}

impl<T: MinMaxValue> Aggregator for Min<T> {
    fn aggregate<'a>(
        &mut self,
        ctx: &ExprContext,
        input: impl IntoIterator<Item = &'a Value>,
    ) -> Result<()> {
        let values: Vec<_> = input.into_iter().map(|v| Cow::Borrowed(v)).collect();
        let result = interpret_expr(ctx, &self.expr, &values)?;

        let min = result
            .into_iter()
            .filter(|v| !v.is_null())
            .filter_map(|v| T::cast_from(&v).ok())
            .min_by(|a, b| a.partial_cmp(b).unwrap_or(std::cmp::Ordering::Equal));

        if let Some(val) = min {
            self.min_value = Some(match &self.min_value {
                None => val,
                Some(current) if val < *current => val,
                Some(current) => current.clone(),
            });
        }
        Ok(())
    }

    fn combine(&mut self, other: Self) -> Result<()> {
        match (self.min_value.take(), other.min_value) {
            (Some(current), Some(other)) => {
                self.min_value = Some(if other < current { other } else { current });
            }
            (None, some) | (some, None) => self.min_value = some,
        }
        Ok(())
    }

    fn collect(&self) -> Result<Value> {
        match &self.min_value {
            Some(v) => v.cast().map_err(|e| e.into()),
            None => Ok(Value::Null),
        }
    }
}

impl<T: MinMaxValue> MakePTree for Min<T> {
    fn label(&self) -> String {
        format!("Min<{}>", std::any::type_name::<T>())
    }

    fn make_ptree(&self, builder: &mut TreeBuilder) {
        self.add_child(builder, self.expr.as_ref());
    }
}

pub type BoolMin = Min<bool>;
pub type IntMin = Min<i128>;
pub type FloatMin = Min<f64>;
pub type StringMin = Min<String>;

#[derive(Debug, Clone)]
pub struct Max<T: MinMaxValue> {
    expr: Arc<btql::binder::ast::Expr>,
    max_value: Option<T>,
}

impl<T: MinMaxValue> Max<T> {
    pub fn new(expr: &btql::binder::ast::Expr) -> Self {
        Self {
            expr: Arc::new(expr.clone()),
            max_value: None,
        }
    }
}

impl<T: MinMaxValue> Aggregator for Max<T> {
    fn aggregate<'a>(
        &mut self,
        ctx: &ExprContext,
        input: impl IntoIterator<Item = &'a Value>,
    ) -> Result<()> {
        let values: Vec<_> = input.into_iter().map(|v| Cow::Borrowed(v)).collect();
        let result = interpret_expr(ctx, &self.expr, &values)?;

        let max = result
            .into_iter()
            .filter(|v| !v.is_null())
            .filter_map(|v| T::cast_from(&v).ok())
            .max_by(|a, b| a.partial_cmp(b).unwrap_or(std::cmp::Ordering::Equal));

        if let Some(val) = max {
            self.max_value = Some(match &self.max_value {
                None => val,
                Some(current) if val > *current => val,
                Some(current) => current.clone(),
            });
        }
        Ok(())
    }

    fn combine(&mut self, other: Self) -> Result<()> {
        match (self.max_value.take(), other.max_value) {
            (Some(current), Some(other)) => {
                self.max_value = Some(if other > current { other } else { current });
            }
            (None, some) | (some, None) => self.max_value = some,
        }
        Ok(())
    }

    fn collect(&self) -> Result<Value> {
        match &self.max_value {
            Some(v) => v.cast().map_err(|e| e.into()),
            None => Ok(Value::Null),
        }
    }
}

impl<T: MinMaxValue> MakePTree for Max<T> {
    fn label(&self) -> String {
        format!("Max<{}>", std::any::type_name::<T>())
    }

    fn make_ptree(&self, builder: &mut TreeBuilder) {
        self.add_child(builder, self.expr.as_ref());
    }
}

pub type BoolMax = Max<bool>;
pub type IntMax = Max<i128>;
pub type FloatMax = Max<f64>;
pub type StringMax = Max<String>;

#[derive(Debug, Clone)]
pub struct JsonMin {
    expr: Arc<btql::binder::ast::Expr>,
    min_value: Option<String>,
}

impl JsonMin {
    pub fn new(expr: &btql::binder::ast::Expr) -> Self {
        Self {
            expr: Arc::new(expr.clone()),
            min_value: None,
        }
    }
}

impl Aggregator for JsonMin {
    fn aggregate<'a>(
        &mut self,
        ctx: &ExprContext,
        input: impl IntoIterator<Item = &'a Value>,
    ) -> Result<()> {
        let values: Vec<_> = input.into_iter().map(|v| Cow::Borrowed(v)).collect();
        let result = interpret_expr(ctx, &self.expr, &values)?;

        if let Some(min_val) = result
            .into_iter()
            .filter(|v| !v.is_null())
            .map(|v| serde_json::to_string(&v).unwrap())
            .min()
        {
            self.min_value = Some(match &self.min_value {
                None => min_val,
                Some(current) => current.min(&min_val).to_string(),
            });
        }
        Ok(())
    }

    fn combine(&mut self, other: Self) -> Result<()> {
        match (&self.min_value, other.min_value) {
            (Some(current), Some(other)) => self.min_value = Some(current.min(&other).to_string()),
            (None, other) => self.min_value = other,
            _ => {}
        }
        Ok(())
    }

    fn collect(&self) -> Result<Value> {
        match &self.min_value {
            None => Ok(Value::Null),
            Some(json_str) => serde_json::from_str(json_str)
                .map_err(|e| util::anyhow::anyhow!("Failed to deserialize JSON: {}", e).into()),
        }
    }
}

impl MakePTree for JsonMin {
    fn label(&self) -> String {
        "JSON min".to_string()
    }

    fn make_ptree(&self, builder: &mut TreeBuilder) {
        self.add_child(builder, self.expr.as_ref());
    }
}

#[derive(Debug, Clone)]
pub struct JsonMax {
    expr: Arc<btql::binder::ast::Expr>,
    max_value: Option<String>,
}

impl JsonMax {
    pub fn new(expr: &btql::binder::ast::Expr) -> Self {
        Self {
            expr: Arc::new(expr.clone()),
            max_value: None,
        }
    }
}

impl Aggregator for JsonMax {
    fn aggregate<'a>(
        &mut self,
        ctx: &ExprContext,
        input: impl IntoIterator<Item = &'a Value>,
    ) -> Result<()> {
        let values: Vec<_> = input.into_iter().map(|v| Cow::Borrowed(v)).collect();
        let result = interpret_expr(ctx, &self.expr, &values)?;

        if let Some(max_val) = result
            .into_iter()
            .filter(|v| !v.is_null())
            .map(|v| serde_json::to_string(&v).unwrap())
            .max()
        {
            self.max_value = Some(match &self.max_value {
                None => max_val,
                Some(current) => current.max(&max_val).to_string(),
            });
        }
        Ok(())
    }

    fn combine(&mut self, other: Self) -> Result<()> {
        match (&self.max_value, other.max_value) {
            (Some(current), Some(other)) => self.max_value = Some(current.max(&other).to_string()),
            (None, other) => self.max_value = other,
            _ => {}
        }
        Ok(())
    }

    fn collect(&self) -> Result<Value> {
        match &self.max_value {
            None => Ok(Value::Null),
            Some(json_str) => serde_json::from_str(json_str)
                .map_err(|e| util::anyhow::anyhow!("Failed to deserialize JSON: {}", e).into()),
        }
    }
}

impl MakePTree for JsonMax {
    fn label(&self) -> String {
        "JSON max".to_string()
    }

    fn make_ptree(&self, builder: &mut TreeBuilder) {
        self.add_child(builder, self.expr.as_ref());
    }
}

#[derive(Debug, Clone)]
pub struct UnknownMin {
    expr: Arc<btql::binder::ast::Expr>,
    min_boolean: Option<bool>,
    min_integer: Option<i64>,
    min_number: Option<f64>,
    min_string: Option<String>,
    min_json: Option<String>,
    weakest_type: ScalarType,
}

impl UnknownMin {
    pub fn new(expr: &btql::binder::ast::Expr) -> Self {
        Self {
            expr: Arc::new(expr.clone()),
            min_boolean: None,
            min_integer: None,
            min_number: None,
            min_string: None,
            min_json: None,
            weakest_type: ScalarType::Null,
        }
    }
}

impl Aggregator for UnknownMin {
    fn aggregate<'a>(
        &mut self,
        ctx: &ExprContext,
        input: impl IntoIterator<Item = &'a Value>,
    ) -> Result<()> {
        let values: Vec<_> = input.into_iter().map(|v| Cow::Borrowed(v)).collect();
        let result = interpret_expr(ctx, &self.expr, &values)?;

        for value in result {
            if value.is_null() {
                continue;
            }

            let (normalized, scalar_type) = normalize_to_scalar_type(Cow::Borrowed(&value))
                .map_err(|e| util::anyhow::anyhow!(e))?;

            let effective_type = match scalar_type {
                ScalarType::Interval | ScalarType::DateTime | ScalarType::Date => {
                    ScalarType::Unknown
                }
                _ => scalar_type,
            };
            if type_rank(effective_type) < type_rank(self.weakest_type) {
                self.weakest_type = effective_type;
            }

            // Update accumulated values for the weakest type we've seen so far
            // and all weaker types (since we could encounter a weaker type later).
            let weakest_type_rank = type_rank(self.weakest_type);

            if weakest_type_rank >= type_rank(ScalarType::Boolean) {
                if let Ok(cow) =
                    cast_to_scalar_type(Cow::Borrowed(&normalized), ScalarType::Boolean)
                {
                    if let Value::Bool(b) = cow.as_ref() {
                        self.min_boolean =
                            Some(self.min_boolean.map_or(*b, |current| current.min(*b)));
                    }
                }
            }

            if weakest_type_rank >= type_rank(ScalarType::Integer) {
                if let Ok(cow) =
                    cast_to_scalar_type(Cow::Borrowed(&normalized), ScalarType::Integer)
                {
                    if let Value::Number(n) = cow.as_ref() {
                        if let Some(i) = n.as_i64() {
                            self.min_integer =
                                Some(self.min_integer.map_or(i, |current| current.min(i)));
                        }
                    }
                }
            }

            if weakest_type_rank >= type_rank(ScalarType::Number) {
                if let Ok(cow) = cast_to_scalar_type(Cow::Borrowed(&normalized), ScalarType::Number)
                {
                    if let Value::Number(n) = cow.as_ref() {
                        if let Some(f) = n.as_f64() {
                            self.min_number =
                                Some(self.min_number.map_or(f, |current| current.min(f)));
                        }
                    }
                }
            }

            if weakest_type_rank >= type_rank(ScalarType::String) {
                if let Ok(cow) = cast_to_scalar_type(Cow::Borrowed(&normalized), ScalarType::String)
                {
                    if let Value::String(s) = cow.as_ref() {
                        self.min_string = Some(match &self.min_string {
                            None => s.clone(),
                            Some(current) => current.min(s).to_string(),
                        });
                    }
                }
            }

            // "Unknown"
            let json_str = serde_json::to_string(&value).unwrap();
            self.min_json = Some(match &self.min_json {
                None => json_str,
                Some(current) => current.min(&json_str).to_string(),
            });
        }
        Ok(())
    }

    fn combine(&mut self, other: Self) -> Result<()> {
        if type_rank(other.weakest_type) < type_rank(self.weakest_type) {
            self.weakest_type = other.weakest_type;
        }

        match (self.min_boolean, other.min_boolean) {
            (Some(current), Some(other)) => self.min_boolean = Some(current.min(other)),
            (None, some) => self.min_boolean = some,
            _ => {}
        }

        match (self.min_integer, other.min_integer) {
            (Some(current), Some(other)) => self.min_integer = Some(current.min(other)),
            (None, some) => self.min_integer = some,
            _ => {}
        }

        match (self.min_number, other.min_number) {
            (Some(current), Some(other)) => self.min_number = Some(current.min(other)),
            (None, some) => self.min_number = some,
            _ => {}
        }

        match (&self.min_string, other.min_string) {
            (Some(current), Some(other)) => self.min_string = Some(current.min(&other).to_string()),
            (None, some) => self.min_string = some,
            _ => {}
        }

        match (&self.min_json, other.min_json) {
            (Some(current), Some(other)) => self.min_json = Some(current.min(&other).to_string()),
            (None, some) => self.min_json = some,
            _ => {}
        }

        Ok(())
    }

    fn collect(&self) -> Result<Value> {
        match self.weakest_type {
            ScalarType::Null => Ok(Value::Null),
            ScalarType::Boolean => match self.min_boolean {
                Some(b) => Ok(Value::Bool(b)),
                None => Ok(Value::Null),
            },
            ScalarType::Integer => match self.min_integer {
                Some(i) => Ok(Value::Number(Number::from(i))),
                None => Ok(Value::Null),
            },
            ScalarType::Number => match self.min_number {
                Some(n) => Ok(Value::Number(Number::from_f64(n).unwrap())),
                None => Ok(Value::Null),
            },
            ScalarType::String => match &self.min_string {
                Some(s) => Ok(Value::String(s.clone())),
                None => Ok(Value::Null),
            },
            _ => match &self.min_json {
                Some(json_str) => serde_json::from_str(json_str)
                    .map_err(|e| util::anyhow::anyhow!("Failed to deserialize JSON: {}", e).into()),
                None => Ok(Value::Null),
            },
        }
    }
}

impl MakePTree for UnknownMin {
    fn label(&self) -> String {
        "Unknown min".to_string()
    }

    fn make_ptree(&self, builder: &mut TreeBuilder) {
        self.add_child(builder, self.expr.as_ref());
    }
}

#[derive(Debug, Clone)]
pub struct UnknownMax {
    expr: Arc<btql::binder::ast::Expr>,
    max_boolean: Option<bool>,
    max_integer: Option<i64>,
    max_number: Option<f64>,
    max_string: Option<String>,
    max_json: Option<String>,
    weakest_type: ScalarType,
}

impl UnknownMax {
    pub fn new(expr: &btql::binder::ast::Expr) -> Self {
        Self {
            expr: Arc::new(expr.clone()),
            max_boolean: None,
            max_integer: None,
            max_number: None,
            max_string: None,
            max_json: None,
            weakest_type: ScalarType::Null,
        }
    }
}

impl Aggregator for UnknownMax {
    fn aggregate<'a>(
        &mut self,
        ctx: &ExprContext,
        input: impl IntoIterator<Item = &'a Value>,
    ) -> Result<()> {
        let values: Vec<_> = input.into_iter().map(|v| Cow::Borrowed(v)).collect();
        let result = interpret_expr(ctx, &self.expr, &values)?;

        for value in result {
            if value.is_null() {
                continue;
            }

            let (normalized, scalar_type) = normalize_to_scalar_type(Cow::Borrowed(&value))
                .map_err(|e| util::anyhow::anyhow!(e))?;

            let effective_type = match scalar_type {
                ScalarType::Interval | ScalarType::DateTime | ScalarType::Date => {
                    ScalarType::Unknown
                }
                _ => scalar_type,
            };
            if type_rank(effective_type) < type_rank(self.weakest_type) {
                self.weakest_type = effective_type;
            }

            // Update accumulated values for the weakest type we've seen so far
            // and all weaker types (since we could encounter a weaker type later).
            let weakest_type_rank = type_rank(self.weakest_type);

            if weakest_type_rank >= type_rank(ScalarType::Boolean) {
                if let Ok(cow) =
                    cast_to_scalar_type(Cow::Borrowed(&normalized), ScalarType::Boolean)
                {
                    if let Value::Bool(b) = cow.as_ref() {
                        self.max_boolean =
                            Some(self.max_boolean.map_or(*b, |current| current.max(*b)));
                    }
                }
            }

            if weakest_type_rank >= type_rank(ScalarType::Integer) {
                if let Ok(cow) =
                    cast_to_scalar_type(Cow::Borrowed(&normalized), ScalarType::Integer)
                {
                    if let Value::Number(n) = cow.as_ref() {
                        if let Some(i) = n.as_i64() {
                            self.max_integer =
                                Some(self.max_integer.map_or(i, |current| current.max(i)));
                        }
                    }
                }
            }

            if weakest_type_rank >= type_rank(ScalarType::Number) {
                if let Ok(cow) = cast_to_scalar_type(Cow::Borrowed(&normalized), ScalarType::Number)
                {
                    if let Value::Number(n) = cow.as_ref() {
                        if let Some(f) = n.as_f64() {
                            self.max_number =
                                Some(self.max_number.map_or(f, |current| current.max(f)));
                        }
                    }
                }
            }

            if weakest_type_rank >= type_rank(ScalarType::String) {
                if let Ok(cow) = cast_to_scalar_type(Cow::Borrowed(&normalized), ScalarType::String)
                {
                    if let Value::String(s) = cow.as_ref() {
                        self.max_string = Some(match &self.max_string {
                            None => s.clone(),
                            Some(current) => current.max(s).to_string(),
                        });
                    }
                }
            }

            // "Unknown"
            let json_str = serde_json::to_string(&value).unwrap();
            self.max_json = Some(match &self.max_json {
                None => json_str,
                Some(current) => current.max(&json_str).to_string(),
            });
        }
        Ok(())
    }

    fn combine(&mut self, other: Self) -> Result<()> {
        if type_rank(other.weakest_type) < type_rank(self.weakest_type) {
            self.weakest_type = other.weakest_type;
        }

        match (self.max_boolean, other.max_boolean) {
            (Some(current), Some(other)) => self.max_boolean = Some(current.max(other)),
            (None, some) => self.max_boolean = some,
            _ => {}
        }

        match (self.max_integer, other.max_integer) {
            (Some(current), Some(other)) => self.max_integer = Some(current.max(other)),
            (None, some) => self.max_integer = some,
            _ => {}
        }

        match (self.max_number, other.max_number) {
            (Some(current), Some(other)) => self.max_number = Some(current.max(other)),
            (None, some) => self.max_number = some,
            _ => {}
        }

        match (self.max_string.take(), other.max_string) {
            (Some(current), Some(other)) => self.max_string = Some(current.max(other)),
            (None, some) => self.max_string = some,
            _ => {}
        }

        match (&self.max_json, other.max_json) {
            (Some(current), Some(other)) => self.max_json = Some(current.max(&other).to_string()),
            (None, some) => self.max_json = some,
            _ => {}
        }

        Ok(())
    }

    fn collect(&self) -> Result<Value> {
        match self.weakest_type {
            ScalarType::Null => Ok(Value::Null),
            ScalarType::Boolean => match self.max_boolean {
                Some(b) => Ok(Value::Bool(b)),
                None => Ok(Value::Null),
            },
            ScalarType::Integer => match self.max_integer {
                Some(i) => Ok(Value::Number(Number::from(i))),
                None => Ok(Value::Null),
            },
            ScalarType::Number => match self.max_number {
                Some(n) => Ok(Value::Number(Number::from_f64(n).unwrap())),
                None => Ok(Value::Null),
            },
            ScalarType::String => match &self.max_string {
                Some(s) => Ok(Value::String(s.clone())),
                None => Ok(Value::Null),
            },
            _ => match &self.max_json {
                Some(json_str) => serde_json::from_str(json_str)
                    .map_err(|e| util::anyhow::anyhow!("Failed to deserialize JSON: {}", e).into()),
                None => Ok(Value::Null),
            },
        }
    }
}

impl MakePTree for UnknownMax {
    fn label(&self) -> String {
        "Unknown max".to_string()
    }

    fn make_ptree(&self, builder: &mut TreeBuilder) {
        self.add_child(builder, self.expr.as_ref());
    }
}

#[derive(Clone)]
pub struct Percentile {
    expr: Arc<btql::binder::ast::Expr>,
    percentile: f64,
    sketch: DDSketch,
}

impl std::fmt::Debug for Percentile {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("Percentile")
            .field("expr", &self.expr)
            .field("percentile", &self.percentile)
            .field("sketch_len", &self.sketch.length())
            .field("count", &self.sketch.count())
            .field("min", &self.sketch.min())
            .field("max", &self.sketch.max())
            .field("sum", &self.sketch.sum())
            .finish()
    }
}

impl Percentile {
    pub fn new(expr: &btql::binder::ast::Expr, percentile: f64) -> Self {
        if !(0.0..=100.0).contains(&percentile) {
            panic!("Percentile must be between 0 and 100");
        }
        Self {
            expr: Arc::new(expr.clone()),
            percentile,
            // NOTE(austin): Technically this is a "low percentile" implementation.
            // If we want to mimic Clickhouse's `quantileExact` we could fork
            // DDSketch to modify the choice of index. We could also do some additional
            // fiddling if we wanted to mimic `percentile_cont` in Postgres.
            // Also see PercentileExact in percentile_test.rs
            sketch: DDSketch::new(Config::new(0.01, 2048, 1.0e-9)),
        }
    }
}

impl Aggregator for Percentile {
    fn aggregate<'a>(
        &mut self,
        ctx: &ExprContext,
        input: impl IntoIterator<Item = &'a Value>,
    ) -> Result<()> {
        let values: Vec<_> = input.into_iter().map(|v| Cow::Borrowed(v)).collect();
        let result = interpret_expr(ctx, &self.expr, &values)?;

        for value in result {
            let n = match value.as_ref() {
                Value::Number(n) => match n.as_f64() {
                    Some(f) if f.is_finite() => f,
                    _ => continue,
                },
                _ => continue,
            };

            self.sketch.add(n);
        }
        Ok(())
    }

    fn combine(&mut self, other: Self) -> Result<()> {
        self.sketch.merge(&other.sketch).map_err(|e| {
            crate::interpreter::error::InterpreterError::InternalError(format!(
                "Failed to merge DDSketches: {}",
                e
            ))
        })?;
        Ok(())
    }

    fn collect(&self) -> Result<Value> {
        if self.sketch.length() == 0 {
            return Ok(Value::Null);
        }

        let quantile = self.percentile / 100.0;
        match self.sketch.quantile(quantile) {
            Ok(Some(value)) => Ok(Value::Number(serde_json::Number::from_f64(value).unwrap())),
            Ok(None) => Ok(Value::Null),
            Err(e) => Err(crate::interpreter::error::InterpreterError::InternalError(
                format!("Failed to compute quantile: {}", e),
            )),
        }
    }
}

impl MakePTree for Percentile {
    fn label(&self) -> String {
        format!("Percentile {}", self.percentile)
    }

    fn make_ptree(&self, builder: &mut TreeBuilder) {
        self.add_child(builder, self.expr.as_ref());
    }
}
