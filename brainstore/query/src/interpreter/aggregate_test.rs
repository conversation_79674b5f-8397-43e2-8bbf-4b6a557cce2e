use crate::expect_tests::round_value;
use crate::interpreter::{aggregate::add_kahan, aggregate::Aggregator};
use btql::binder::ast::{Expr, Field};
use btql::typesystem::coerce::normalize_value_for_comparison;
use rand::rngs::StdRng;
use rand::SeedableRng;
use rand_distr::{Beta, ChiSquared, Distribution, Exp, Gamma, LogNormal, Normal};
use std::{borrow::Cow, sync::Arc};
use util::json::PathPiece;
use util::ptree::{MakePTree, TreeBuilder};
use util::Value;

#[cfg(test)]
mod kahan_tests {
    use super::*;

    #[test]
    fn test_kahan_summation_precision() {
        let mut sum = 0.0;
        let mut compensation = 0.0;

        let expected = 1_000_000_f64; // 1e7 * 0.1 = 1e6
        for _ in 0..10_000_000 {
            (sum, compensation) = add_kahan(sum, compensation, 0.1);
        }

        // Final sum should include the compensation
        let final_sum = sum + compensation;

        // The Kahan sum should be more accurate than simple addition.
        let naive_sum = (0..10_000_000).fold(0_f64, |acc, _| acc + 0.1);

        assert!((final_sum - expected).abs() < 0.5 * (naive_sum - expected).abs());
        assert!((final_sum - expected).abs() < 1e-10); // Very small error
    }

    #[test]
    fn test_kahan_summation_with_mixed_magnitudes() {
        let mut sum = 0.0;
        let mut compensation = 0.0;

        let expected = 1.0 + 1e-13; // 1000 * 1e-16 = 1e-13
        let inputs = vec![1.0f64]
            .into_iter()
            .chain((0..1000).map(|_| 1e-16))
            .collect::<Vec<_>>();

        for value in inputs.iter() {
            (sum, compensation) = add_kahan(sum, compensation, *value);
        }

        let final_sum = sum + compensation;

        // The Kahan sum should be more accurate than naive addition.
        let naive_sum = inputs.iter().sum::<f64>();

        assert!((final_sum - expected).abs() < 0.5 * (naive_sum - expected).abs());
        assert!((final_sum - expected).abs() < 1e-15);
    }
}

#[cfg(test)]
mod percentile_tests {
    use btql::interpreter::context::ExprContext;

    use super::*;
    use crate::interpreter::aggregate::Percentile;

    const SEED: u64 = 12345;
    const MAX_RELATIVE_ERROR: f64 = 0.01;
    const NUM_ROWS: [usize; 5] = [5, 10, 100, 1000, 10000];
    const TEST_PERCENTILES: [f64; 9] = [0.0, 1.0, 5.0, 10.0, 50.0, 90.0, 95.0, 99.0, 100.0];
    const BATCH_SIZES: [usize; 2] = [1, 50];

    fn make_number_expr() -> Expr {
        Expr::Field(Field::new(
            vec![PathPiece::Key("foo".to_string())],
            serde_json::json!({ "type": "number" }),
            None,
        ))
    }

    fn make_test_rows(values: &[Value]) -> Vec<Value> {
        values
            .iter()
            .map(|v| {
                let mut obj = serde_json::Map::new();
                obj.insert("foo".to_string(), v.clone());
                Value::Object(obj)
            })
            .collect()
    }

    fn assert_value_approx_eq(
        actual: Value,
        expected: Value,
        relative_error: Option<f64>,
    ) -> Result<(), String> {
        let actual_normalized = normalize_value_for_comparison(round_value(actual));
        let expected_normalized = normalize_value_for_comparison(round_value(expected));

        match (actual_normalized, expected_normalized) {
            (Value::Number(a), Value::Number(b)) => {
                let a = a.as_f64().unwrap();
                let b = b.as_f64().unwrap();

                if let Some(allowed_error) = relative_error {
                    let actual_error = (a - b).abs() / b.abs().max(1e-10);
                    if actual_error <= allowed_error {
                        Ok(())
                    } else {
                        Err(format!(
                            "Values differ by relative error {}: actual={}, expected={}",
                            actual_error, a, b
                        ))
                    }
                } else if a == b {
                    Ok(())
                } else {
                    Err(format!("Values differ: actual={}, expected={}", a, b))
                }
            }
            _ => Err("Values aren't numbers".to_string()),
        }
    }

    #[test]
    fn test_percentile_empty() {
        let mut p = Percentile::new(&make_number_expr(), 90.0);
        let ctx = ExprContext::new(None);

        // Non-numeric values are ignored
        let values = vec![
            Value::String("not a number".to_string()),
            Value::Array(vec![Value::Number(serde_json::Number::from(1))]),
            Value::Null,
        ];
        let rows = make_test_rows(&values);
        let input: Vec<&Value> = rows.iter().collect();

        p.aggregate(&ctx, input).unwrap();
        let result = p.collect().unwrap();
        assert_eq!(result, Value::Null);
    }

    #[test]
    fn test_median() {
        let mut p = Percentile::new(&make_number_expr(), 50.0);
        let ctx = ExprContext::new(None);

        let values = (1..=5)
            .map(|n| Value::Number(serde_json::Number::from(n)))
            .collect::<Vec<_>>();
        let rows = make_test_rows(&values);
        let input: Vec<&Value> = rows.iter().collect();

        p.aggregate(&ctx, input).unwrap();
        let result = p.collect().unwrap();

        assert_value_approx_eq(
            result,
            Value::Number(serde_json::Number::from(3)),
            Some(MAX_RELATIVE_ERROR),
        )
        .unwrap();
    }

    #[test]
    fn test_combine() {
        let mut p1 = Percentile::new(&make_number_expr(), 80.0);
        let mut p2 = Percentile::new(&make_number_expr(), 80.0);
        let ctx = ExprContext::new(None);

        // First aggregator gets [1,2,3]
        let values1 = (1..=3)
            .map(|n| Value::Number(serde_json::Number::from(n)))
            .collect::<Vec<_>>();
        let rows1 = make_test_rows(&values1);
        let input1: Vec<&Value> = rows1.iter().collect();
        p1.aggregate(&ctx, input1).unwrap();

        // Second aggregator gets [4,5,6]
        let values2 = (4..=6)
            .map(|n| Value::Number(serde_json::Number::from(n)))
            .collect::<Vec<_>>();
        let rows2 = make_test_rows(&values2);
        let input2: Vec<&Value> = rows2.iter().collect();
        p2.aggregate(&ctx, input2).unwrap();

        p1.combine(p2).unwrap();
        let result = p1.collect().unwrap();

        assert_value_approx_eq(
            result,
            Value::Number(serde_json::Number::from(5)),
            Some(MAX_RELATIVE_ERROR),
        )
        .unwrap();
    }

    #[test]
    fn test_percentile_distributions() {
        let mut rng = StdRng::seed_from_u64(SEED);

        test_distribution(&mut rng, Normal::new(35.0, 1.0).unwrap(), "Normal(35,1)");
        test_distribution(
            &mut rng,
            LogNormal::new(0.0, 2.0).unwrap(),
            "LogNormal(0,2)",
        );
        test_distribution(&mut rng, Exp::new(1.5).unwrap(), "Exp(1.5)");
        test_distribution(&mut rng, ChiSquared::new(2.0).unwrap(), "ChiSquared(2)");
        test_distribution(&mut rng, Beta::new(2.0, 5.0).unwrap(), "Beta(2,5)");
        test_distribution(&mut rng, Beta::new(5.0, 2.0).unwrap(), "Beta(5,2)");
        test_distribution(&mut rng, Gamma::new(1.0, 2.0).unwrap(), "Gamma(1,2)");
        test_distribution(&mut rng, Gamma::new(3.0, 2.0).unwrap(), "Gamma(3,2)");
    }

    fn test_distribution<D: Distribution<f64>>(mut rng: &mut StdRng, dist: D, dist_name: &str) {
        for num_rows in NUM_ROWS {
            // Generate and sort the sample data
            let mut raw_values: Vec<f64> = (0..num_rows).map(|_| dist.sample(&mut rng)).collect();
            raw_values.sort_by(|a, b| a.partial_cmp(b).unwrap());

            let values: Vec<_> = raw_values
                .iter()
                .map(|&v| Value::Number(serde_json::Number::from_f64(v).unwrap()))
                .collect();
            let rows = make_test_rows(&values);
            let input: Vec<&Value> = rows.iter().collect();

            for p in TEST_PERCENTILES {
                for batch_size in BATCH_SIZES {
                    let effective_batch_size = batch_size.min(num_rows);
                    let mut sketch = Percentile::new(&make_number_expr(), p);
                    let mut exact = PercentileExact::new(&make_number_expr(), p);
                    let ctx = ExprContext::new(None);

                    // Process input in batches
                    for chunk in input.chunks(effective_batch_size) {
                        sketch.aggregate(&ctx, chunk.iter().cloned()).unwrap();
                        exact.aggregate(&ctx, chunk.iter().cloned()).unwrap();
                    }

                    let sketch_result = sketch.collect().unwrap();
                    let exact_result = exact.collect().unwrap();

                    // Calculate the actual percentile from sorted data
                    let n = if p < 100.0 {
                        (p / 100.0 * (raw_values.len() - 1) as f64) as usize
                    } else {
                        raw_values.len() - 1
                    };
                    let actual_value = raw_values[n];
                    let actual_value_json =
                        Value::Number(serde_json::Number::from_f64(actual_value).unwrap());

                    // Display ~3 values around the percentile
                    let window_size = 1;
                    let start = n.saturating_sub(window_size);
                    let end = (n + window_size + 1).min(raw_values.len());
                    let relevant_values = raw_values[start..end].to_vec();

                    let exact_matches = assert_value_approx_eq(
                        exact_result.clone(),
                        actual_value_json.clone(),
                        Some(MAX_RELATIVE_ERROR),
                    );
                    let sketch_approx = assert_value_approx_eq(
                        sketch_result.clone(),
                        actual_value_json.clone(),
                        Some(MAX_RELATIVE_ERROR),
                    );

                    if exact_matches.is_err() || sketch_approx.is_err() {
                        println!(
                            "\ntest_distribution failure for {}! num_rows: {}, batch_size: {}, p{}\nRelevant values around p{} [{}..={}]: {:?}\nTarget index: {}\nActual p{}: {}\nSketch result: {:?} (within 1%: {})\nExact result: {:?} (matches actual: {})",
                            dist_name,
                            num_rows,
                            effective_batch_size,
                            p,
                            p,
                            start,
                            end - 1,
                            relevant_values,
                            n,
                            p,
                            actual_value,
                            sketch_result,
                            sketch_approx.is_ok(),
                            exact_result,
                            exact_matches.is_ok()
                        );
                    }

                    exact_matches.unwrap();
                    sketch_approx.unwrap();
                }
            }
        }
    }

    #[derive(Debug, Clone)]
    struct PercentileExact {
        expr: Arc<btql::binder::ast::Expr>,
        percentile: f64,
        values: Vec<f64>,
    }

    impl PercentileExact {
        fn new(expr: &btql::binder::ast::Expr, percentile: f64) -> Self {
            if !(0.0..=100.0).contains(&percentile) {
                panic!("Percentile must be between 0 and 100");
            }
            Self {
                expr: Arc::new(expr.clone()),
                percentile,
                values: Vec::new(),
            }
        }

        fn calculate_percentile(&self) -> Option<f64> {
            if self.values.is_empty() {
                return None;
            }

            let mut values = self.values.clone();
            let n = if self.percentile < 100.0 {
                let level = self.percentile / 100.0;
                // NOTE(austin): This more or less mirrors Clickhouse's `quantileExactLow`
                // and the DDSketch implementation used by `Percentile` and tantivy's
                // quantile aggregate.  If we wanted to mimic Clickhouse's `quantileExact`
                // instead, we could replace this with `level * values.len()`.
                (level * (values.len() - 1) as f64) as usize
            } else {
                values.len() - 1
            };

            Some(
                *values
                    .select_nth_unstable_by(n, |a, b| {
                        a.partial_cmp(b).unwrap_or(std::cmp::Ordering::Equal)
                    })
                    .1,
            )
        }
    }

    impl Aggregator for PercentileExact {
        fn aggregate<'a>(
            &mut self,
            ctx: &ExprContext,
            input: impl IntoIterator<Item = &'a Value>,
        ) -> crate::interpreter::error::Result<()> {
            let values: Vec<_> = input.into_iter().map(|v| Cow::Borrowed(v)).collect();
            let result = btql::interpreter::expr::interpret_expr(ctx, &self.expr, &values)?;

            for value in result {
                let n = match value.as_ref() {
                    Value::Number(n) => match n.as_f64() {
                        Some(f) if f.is_finite() => f,
                        _ => continue,
                    },
                    _ => continue,
                };
                self.values.push(n);
            }
            Ok(())
        }

        fn combine(&mut self, other: Self) -> crate::interpreter::error::Result<()> {
            self.values.extend(other.values);
            Ok(())
        }

        fn collect(&self) -> crate::interpreter::error::Result<Value> {
            match self.clone().calculate_percentile() {
                Some(p) => Ok(Value::Number(serde_json::Number::from_f64(p).unwrap())),
                None => Ok(Value::Null),
            }
        }
    }

    impl MakePTree for PercentileExact {
        fn label(&self) -> String {
            format!("PercentileExact {}", self.percentile)
        }

        fn make_ptree(&self, builder: &mut TreeBuilder) {
            self.add_child(builder, self.expr.as_ref());
        }
    }
}
