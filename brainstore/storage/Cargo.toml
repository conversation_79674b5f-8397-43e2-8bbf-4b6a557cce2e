[package]
name = "storage"
version = "0.1.0"
edition = "2021"

[dependencies]
async-stream = "0.3.6"
backoff = { version = "0.4", features = ["tokio"] }
base64 = { version = "0.21" }
bincode = "=2.0.0"
byte-unit = "5.1.4"
bytes = "1.7.2"
clap = { version = "4.5.20", features = ["derive", "env"] }
deadpool-postgres = "0.14.0"
downcast-rs = "1.2.1"
env_logger = "0.11.5"
flate2 = "1.0.34"
hashbrown = { version = "0.15.2", features = ["raw-entry"] }
lazy_static = "1.5.0"
log = "0.4.22"
once_cell = "1.20.2"
otel_common = { path = "../otel_common" }
rand = "0.8.5"
redis = { version = "0.29.1", features = ["tls-native-tls", "tokio-native-tls-comp", "tokio-comp"] }
rslock = { git = "https://github.com/ankrgyl/rslock.git", rev = "0e948ba0d04238ea60fb341fc076120e5d8d052e", features = ["tls-native-tls", "tokio-native-tls-comp", "tokio-comp"]}
serde = { version = "1.0.210", features = ["derive"] }
serde_json = "1.0.128"
sha2 = "0.10.8"
stable_deref_trait = "1.2.0"
tantivy = { path = "../tantivy" }
tempfile = "3.13.0"
thiserror = "1.0"
tokio = "1.40.0"
tokio-postgres = { version = "0.7.12", features = ["with-serde_json-1", "with-chrono-0_4", "with-uuid-1"] }
tokio-stream = "0.1.16"
tracing = { path = "../tracing" }
util = { path = "../util" }
postgres-native-tls = "0.5"
native-tls = "0.2"
gxhash = "3.4.1"
dateparser = "0.2.1"
rust_decimal = { version = "1.36.0", features = ["db-postgres"] }
object_store = { version = "0.12.1", features = ["aws", "gcp", "azure"] }
memmap2 = "0.9.5"
arc-swap = "1.7.1"
async-channel = "2.4.0"

[dev-dependencies]
testcontainers = "0.23.1"
testcontainers-modules = { version = "0.11.3", features = ["postgres", "redis"] }
criterion = "0.5"

[[bench]]
name = "wal_entry_bench"
harness = false
