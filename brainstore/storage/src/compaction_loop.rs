use std::{
    collections::{HashSet, VecDeque},
    sync::{Arc, Mutex},
};

use tokio::sync::Notify;
use util::{uuid::Uuid, Result};

// After this point, just reject compactions, which could be used to signal back pressure.
pub const MAX_QUEUED_COMPACTIONS: usize = 1000;

#[derive(Clone)]
pub struct CompactionLoop {
    inner: Arc<CompactionLoopInner>,
}

struct CompactionLoopInner {
    queue: Mutex<DedupedQueue<Uuid>>,
    pub ready: Notify,
}

impl Default for CompactionLoop {
    fn default() -> Self {
        let inner = Arc::new(CompactionLoopInner {
            queue: Mutex::new(DedupedQueue::new(MAX_QUEUED_COMPACTIONS)),
            ready: Notify::new(),
        });

        Self { inner }
    }
}

impl CompactionLoop {
    pub fn add_segments(
        &self,
        segments: impl IntoIterator<Item = Uuid>,
    ) -> Result<(), DedupedQueueError> {
        let mut queue = self.inner.queue.lock().unwrap();
        for segment in segments {
            queue.push_back(segment)?;
        }
        self.inner.ready.notify_one();
        Ok(())
    }

    pub fn pop_front(&self) -> Option<Uuid> {
        self.inner.queue.lock().unwrap().pop_front()
    }

    pub async fn notified(&self) {
        self.inner.ready.notified().await;
    }
}

struct DedupedQueue<T: std::hash::Hash + std::cmp::Eq + Clone> {
    queue: VecDeque<T>,
    set: HashSet<T>,
    max_size: usize,
}

pub enum DedupedQueueError {
    QueueFull,
}

impl<T: std::hash::Hash + std::cmp::Eq + Clone> DedupedQueue<T> {
    pub fn new(max_size: usize) -> Self {
        Self {
            queue: VecDeque::new(),
            set: HashSet::new(),
            max_size,
        }
    }

    pub fn push_back(&mut self, item: T) -> Result<(), DedupedQueueError> {
        if !self.set.contains(&item) {
            if self.queue.len() >= self.max_size {
                return Err(DedupedQueueError::QueueFull);
            }
            self.queue.push_back(item.clone());
            self.set.insert(item);
        }
        Ok(())
    }

    pub fn pop_front(&mut self) -> Option<T> {
        let item = self.queue.pop_front()?;
        self.set.remove(&item);
        Some(item)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_deduped_queue() {
        let mut queue = DedupedQueue::new(5);

        // Test empty queue
        assert!(queue.pop_front().is_none());

        // Test adding and removing items
        assert!(queue.push_back(1).is_ok());
        assert!(queue.push_back(2).is_ok());
        assert!(queue.push_back(3).is_ok());

        assert_eq!(queue.pop_front(), Some(1));
        assert_eq!(queue.pop_front(), Some(2));
        assert_eq!(queue.pop_front(), Some(3));
        assert!(queue.pop_front().is_none());

        // Test deduplication
        assert!(queue.push_back(1).is_ok());
        assert!(queue.push_back(1).is_ok()); // Duplicate, should be ignored
        assert!(queue.push_back(2).is_ok());
        assert!(queue.push_back(1).is_ok()); // Duplicate, should be ignored

        assert_eq!(queue.pop_front(), Some(1));
        assert_eq!(queue.pop_front(), Some(2));
        assert!(queue.pop_front().is_none());

        // Test that items can be re-added after being removed
        assert!(queue.push_back(1).is_ok());
        assert_eq!(queue.pop_front(), Some(1));
        assert!(queue.push_back(1).is_ok()); // Should be allowed now that 1 was removed
        assert_eq!(queue.pop_front(), Some(1));

        // Test max size behavior
        let mut queue = DedupedQueue::new(3);
        assert!(queue.push_back(1).is_ok());
        assert!(queue.push_back(2).is_ok());
        assert!(queue.push_back(3).is_ok());
        // Queue is now at max capacity
        assert!(queue.push_back(4).is_err()); // Should fail due to max size

        // After removing an item, we should be able to add again
        assert_eq!(queue.pop_front(), Some(1));
        assert!(queue.push_back(4).is_ok());

        // Check final queue state
        assert_eq!(queue.pop_front(), Some(2));
        assert_eq!(queue.pop_front(), Some(3));
        assert_eq!(queue.pop_front(), Some(4));
        assert!(queue.pop_front().is_none());
    }
}
