use once_cell::sync::Lazy;
use serde_json::json;
use std::{collections::HashMap, future::Future, ops::RangeInclusive, path::Path};
use tantivy::columnar::MonotonicallyMappableToU64;

use util::{
    anyhow::Result,
    await_spawn_blocking,
    chrono::{DateTime, Utc},
    schema::{BaseOptions, Field, Schema, TantivyField, TantivyType, TextOptions},
    system_types::{FullObjectId, FullObjectIdOwned, FullRowIdOwned, ObjectId, ObjectType},
    test_util::assert_hashmap_eq,
    uuid::Uuid,
    xact::{PaginationKey, TransactionId},
};

use crate::{
    basic_test_fixture::{BasicTestFixture, ValidateVacuumArgs},
    config_with_store::{url_to_global_store, ConfigWithStore},
    directory::cached_directory::FileCacheOpts,
    global_store::{
        DeleteFromSegmentStats, SegmentFieldStatistics, SegmentMetadataUpdate,
        SegmentWalEntriesXactIdStatistic,
    },
    global_store_test::{get_postgres_global_store_migration, POSTGRES_EXTERNAL_MIGRATION},
    index_document::{make_full_schema, IndexDocument},
    index_wal_reader::{IndexWalReader, IndexWalReaderInput},
    index_wal_reader_test_util::get_reader_full_docs,
    object_and_global_store_wal::ObjectAndGlobalStoreWal,
    process_wal::{
        compact_segment_wal, process_object_wal, CompactSegmentWalInput,
        CompactSegmentWalOptionalInput, FIELD_STATISTICS_FIELD_NAMES, XACT_ID_FIELD,
    },
    retention::{
        delete_from_segments_up_to_xact_id, DeleteFromSegmentsInput,
        DeleteFromSegmentsOptionalInput, DeleteFromSegmentsOptions,
    },
    tantivy_index_test_util::read_all_documents,
    tantivy_index_wrapper::ReadonlyTantivyIndexWrapper,
    tantivy_index_wrapper_test_util::make_readonly_tantivy_index_wrapper,
    test_util::{collect_wal_stream, make_compacted_wal_entries, PostgresContainer},
    vacuum_test_util::{
        default_vacuum_index_options_for_testing, force_vacuum_then_validate_index_wal,
        VacuumThenValidateIndexWalArgs,
    },
    wal::{wal_stream, WALScope, Wal},
    wal_entry::WalEntry,
};

pub struct TestFixture {
    pub basic_fixture: BasicTestFixture,
    pub _container: Option<PostgresContainer>,
}

impl TestFixture {
    pub async fn new(use_postgres_global_store: bool) -> Self {
        let mut basic_fixture = BasicTestFixture::new();

        let container = if use_postgres_global_store {
            let container = PostgresContainer::new().await;
            container
                .run_migration(POSTGRES_EXTERNAL_MIGRATION)
                .await
                .unwrap();
            container
                .run_migration(&get_postgres_global_store_migration())
                .await
                .unwrap();
            Some(container)
        } else {
            None
        };

        if use_postgres_global_store {
            let global_store = url_to_global_store(
                &container.as_ref().unwrap().connection_url,
                FileCacheOpts::default(),
                basic_fixture.tmp_dir_config.config.locks_manager.clone(),
            )
            .unwrap();
            basic_fixture.tmp_dir_config.config.global_store = global_store.0;
        }

        Self {
            basic_fixture,
            _container: container,
        }
    }

    pub fn make_full_schema(&self) -> Schema {
        make_full_schema(&basic_schema()).unwrap()
    }

    pub fn config(&self) -> &ConfigWithStore {
        &self.basic_fixture.tmp_dir_config.config
    }

    fn index_prefix(&self) -> &Path {
        self.config().index.prefix.as_path()
    }

    pub fn compact_wal_input(&self, segment_id: Uuid) -> CompactSegmentWalInput {
        CompactSegmentWalInput {
            segment_id,
            index_store: self.config().index.clone(),
            schema: self.make_full_schema(),
            global_store: self.config().global_store.clone(),
            locks_manager: &*self.config().locks_manager,
        }
    }

    pub fn make_segment_wal(&self) -> ObjectAndGlobalStoreWal {
        ObjectAndGlobalStoreWal {
            object_store: self.config().index.store.clone(),
            global_store: self.config().global_store.clone(),
            directory: self.config().index.directory.clone(),
            store_prefix: self.index_prefix().to_path_buf(),
            store_type: self.config().index.store_type,
        }
    }

    pub async fn read_segment_wal_entries(
        &self,
        segment_id: Uuid,
    ) -> Vec<(TransactionId, Vec<WalEntry>)> {
        let segment_wal = self.make_segment_wal();
        collect_wal_stream(wal_stream(
            segment_wal
                .wal_metadata_stream(WALScope::Segment(segment_id), Default::default())
                .await
                .unwrap(),
            Default::default(),
        ))
        .await
        .unwrap()
    }

    pub async fn write_wal_to_segment(&self, segment_id: Uuid, entries: Vec<WalEntry>) {
        let segment_wal = self.make_segment_wal();
        segment_wal
            .insert(WALScope::Segment(segment_id), entries)
            .await
            .unwrap();
    }

    pub async fn read_segment_docs(
        &self,
        segment_id: Uuid,
    ) -> HashMap<FullRowIdOwned, IndexDocument> {
        let readonly_tantivy_index_wrapper = make_readonly_tantivy_index_wrapper(
            self.config().global_store.clone(),
            &self.config().index,
            self.make_full_schema(),
            &[segment_id],
        )
        .await
        .unwrap();

        self.read_segment_docs_with_index_wrapper(readonly_tantivy_index_wrapper)
            .await
    }

    pub async fn read_segment_docs_with_index_wrapper(
        &self,
        tantivy_index_wrapper: ReadonlyTantivyIndexWrapper,
    ) -> HashMap<FullRowIdOwned, IndexDocument> {
        await_spawn_blocking!(move || {
            let reader = tantivy_index_wrapper.make_reader_blocking().unwrap();
            let tantivy_docs = read_all_documents(&reader);
            let writable_tantivy_schema = tantivy_index_wrapper.writable_schema;
            let schema_invert_fields = &writable_tantivy_schema.invert_fields;
            let mut docs: HashMap<FullRowIdOwned, IndexDocument> = HashMap::new();
            for (_, doc) in tantivy_docs {
                let index_doc = IndexDocument::from_tantivy_document(&doc, schema_invert_fields)
                    .unwrap()
                    .to_sanitized()
                    .unwrap();
                let row_id = index_doc.wal_entry.full_row_id().to_owned();
                if let Some(dup) = docs.insert(row_id, index_doc) {
                    panic!("Duplicate row id {:?}", dup.wal_entry.full_row_id());
                }
            }
            docs
        })
        .unwrap()
        .unwrap()
    }

    pub async fn initialize_segment_metadata(&self, segment_id: Uuid) {
        self.initialize_segment_metadata_in_object(FullObjectId::default(), segment_id)
            .await;
    }

    pub async fn initialize_segment_metadata_in_object<'a>(
        &self,
        object_id: FullObjectId<'a>,
        segment_id: Uuid,
    ) {
        self.config()
            .global_store
            .update_segment_ids(&[(object_id, &[segment_id], &[])])
            .await
            .unwrap();
        self.config()
            .global_store
            .upsert_segment_metadatas(
                vec![(segment_id, SegmentMetadataUpdate::default())]
                    .into_iter()
                    .collect(),
            )
            .await
            .unwrap();
    }

    pub async fn read_index_wal_entries(
        &self,
        object_ids: &[FullObjectIdOwned],
    ) -> HashMap<String, IndexDocument> {
        let reader = IndexWalReader::new(
            IndexWalReaderInput {
                config_with_store: &self.basic_fixture.tmp_dir_config.config,
                full_schema: self.make_full_schema(),
                object_ids,
                filters: &[],
                sort: &None,
            },
            Default::default(),
            Default::default(),
        )
        .await
        .unwrap();

        let entries = get_reader_full_docs(&reader)
            .await
            .into_iter()
            .map(|(key, doc)| (key, doc.to_sanitized().unwrap()))
            .collect();
        entries
    }
}

async fn delete_from_segments_for_testing<'a>(
    input: DeleteFromSegmentsInput<'a>,
) -> DeleteFromSegmentStats {
    if input.dry_run {
        return delete_from_segments_up_to_xact_id(
            input,
            DeleteFromSegmentsOptionalInput::default(),
            DeleteFromSegmentsOptions::default(),
        )
        .await
        .unwrap();
    }

    let mut dry_run_input = input.clone();
    dry_run_input.dry_run = true;
    let dry_run_output = delete_from_segments_up_to_xact_id(
        dry_run_input,
        DeleteFromSegmentsOptionalInput::default(),
        DeleteFromSegmentsOptions::default(),
    )
    .await
    .unwrap();

    let output = delete_from_segments_up_to_xact_id(
        input,
        DeleteFromSegmentsOptionalInput::default(),
        DeleteFromSegmentsOptions::default(),
    )
    .await
    .unwrap();

    assert_eq!(
        dry_run_output.wal_stats.planned_num_deleted_wal_entries,
        output.wal_stats.planned_num_deleted_wal_entries,
        "planned_num_deleted_wal_entries mismatch between dry run and actual run"
    );
    assert_eq!(
        dry_run_output.index_stats.planned_num_deleted_index_docs,
        output.index_stats.planned_num_deleted_index_docs,
        "planned_num_deleted_index_docs mismatch between dry run and actual run"
    );
    assert_eq!(
        dry_run_output.wal_stats.num_deleted_wal_entries, 0,
        "dry run's num_deleted_wal_entries should be 0"
    );
    assert_eq!(
        dry_run_output.index_stats.num_deleted_index_docs, 0,
        "dry run's num_deleted_index_docs should be 0"
    );
    assert_eq!(
        dry_run_output.index_stats.num_write_locks, 0,
        "dry run should not take any write locks"
    );
    assert_eq!(
        output.wal_stats.planned_num_deleted_wal_entries, output.wal_stats.num_deleted_wal_entries,
        "mismatch between planned and actual number of deleted WAL entries"
    );
    assert_eq!(
        output.index_stats.planned_num_deleted_index_docs,
        output.index_stats.num_deleted_index_docs,
        "mismatch between planned and actual number of deleted index docs"
    );

    output
}

fn basic_schema() -> Schema {
    let text_options = TextOptions {
        stored: true,
        fast: false,
        tokenize: false,
    };
    let base_options = BaseOptions {
        stored: true,
        fast: false,
    };

    Schema::new(
        "test".to_string(),
        vec![
            Field {
                name: "field1".to_string(),
                tantivy: vec![TantivyField {
                    name: "field1".to_string(),
                    field_ts: 1,
                    field_type: TantivyType::Str(text_options.clone()),
                    repeated: false,
                    lossy_fast_field: false,
                }],
            },
            Field {
                name: "field2".to_string(),
                tantivy: vec![TantivyField {
                    name: "field2".to_string(),
                    field_ts: 2,
                    field_type: TantivyType::I64(base_options.clone()),
                    repeated: false,
                    lossy_fast_field: false,
                }],
            },
            Field {
                name: "field3".to_string(),
                tantivy: vec![TantivyField {
                    name: "field3".to_string(),
                    field_ts: 3,
                    field_type: TantivyType::Json(text_options.clone()),
                    repeated: false,
                    lossy_fast_field: false,
                }],
            },
        ],
        Default::default(),
    )
    .unwrap()
}

pub async fn run_test_with_global_stores<F, Fut>(test_fn: F) -> Result<()>
where
    F: Fn(bool) -> Fut + Copy,
    Fut: Future<Output = Result<()>>,
{
    // NOTE(austin): We've temporarily disabled using the postgres global store
    // in the retention/vacuum tests due to flakiness. I suspect multiple tests
    // are contending for the same postgres connection, which is causing weird
    // issues like the migration file not running or even running multiple times.
    // (Or maybe there's just a dumb bug in this code somewhere.)
    //
    // Technically we don't really need to test the postgres global store here
    // here, because this code should be agnostic to the global store implementation
    // and there are postgres-specific tests in `global_store_test.rs` that
    // specifically test the postgres WAL entry deletion queries. However, it would be
    // nice to add these tests back as a sanity check before enabling retention in prod.
    //
    for use_postgres_global_store in [false, true] {
        // for use_postgres_global_store in [false] {
        test_fn(use_postgres_global_store).await?;
    }
    Ok(())
}

#[tokio::test]
async fn test_global_store_delete_from_segment_wal() -> Result<()> {
    run_test_with_global_stores(test_global_store_delete_from_segment_wal_inner).await
}

async fn test_global_store_delete_from_segment_wal_inner(
    use_postgres_global_store: bool,
) -> Result<()> {
    let fixture = TestFixture::new(use_postgres_global_store).await;

    let segment_id = Uuid::new_v4();
    fixture.initialize_segment_metadata(segment_id).await;

    let wal_entries = vec![
        WalEntry {
            _xact_id: TransactionId(0),
            id: "row1".to_string(),
            ..Default::default()
        },
        WalEntry {
            _xact_id: TransactionId(1),
            id: "row2".to_string(),
            ..Default::default()
        },
    ];
    fixture.write_wal_to_segment(segment_id, wal_entries).await;

    let global_store = fixture.config().global_store.clone();

    // Delete WAL entries prior to xact_id 1.
    global_store
        .delete_segment_wal_entries_up_to_xact_id(&[segment_id], TransactionId(1))
        .await?;

    // Verify only entries with xact_id >= 1 remain.
    let stats = global_store
        .query_segment_wal_entries_xact_id_statistic(
            &[segment_id],
            SegmentWalEntriesXactIdStatistic::Min,
            None,
        )
        .await?;

    assert_eq!(stats[0], Some(TransactionId(1)));

    fixture
        .basic_fixture
        .validate_vacuum(ValidateVacuumArgs {
            object_ids: Some(&[FullObjectId::default()]),
            schema: Some(fixture.make_full_schema()),
        })
        .await;

    Ok(())
}

#[tokio::test]
async fn test_delete_from_segment_wal() -> Result<()> {
    run_test_with_global_stores(test_delete_from_segment_wal_inner).await
}

async fn test_delete_from_segment_wal_inner(use_postgres_global_store: bool) -> Result<()> {
    let fixture = TestFixture::new(use_postgres_global_store).await;

    let segment_id = Uuid::new_v4();
    fixture.initialize_segment_metadata(segment_id).await;

    // Write some test WAL entries with different transaction IDs.
    let wal_entries = vec![
        WalEntry {
            _xact_id: TransactionId(0),
            id: "row1".to_string(),
            ..Default::default()
        },
        WalEntry {
            _xact_id: TransactionId(1),
            id: "row2".to_string(),
            ..Default::default()
        },
        WalEntry {
            _xact_id: TransactionId(2),
            id: "row3".to_string(),
            ..Default::default()
        },
    ];
    fixture.write_wal_to_segment(segment_id, wal_entries).await;

    // Delete entries with xact_id < 2.
    delete_from_segments_up_to_xact_id(
        DeleteFromSegmentsInput {
            segment_ids: &[segment_id],
            index_store: &fixture.config().index,
            schema: &fixture.make_full_schema(),
            global_store: fixture.config().global_store.clone(),
            locks_manager: &*fixture.config().locks_manager,
            min_retained_xact_id: TransactionId(2),
            dry_run: false,
        },
        DeleteFromSegmentsOptionalInput {
            testing_skip_field_statistics: false,
            testing_skip_index_deletion: true,
            ..Default::default()
        },
        DeleteFromSegmentsOptions::default(),
    )
    .await?;

    // Verify that only entries with xact_id >= 2 remain.
    let remaining_entries = fixture.read_segment_wal_entries(segment_id).await;
    assert_eq!(remaining_entries.len(), 1);
    assert_eq!(remaining_entries[0].0, TransactionId(2));

    fixture
        .basic_fixture
        .validate_vacuum(ValidateVacuumArgs {
            object_ids: Some(&[FullObjectId::default()]),
            schema: Some(fixture.make_full_schema()),
        })
        .await;

    Ok(())
}

#[tokio::test]
async fn test_delete_from_global_store() -> Result<()> {
    run_test_with_global_stores(test_delete_from_global_store_inner).await
}

async fn test_delete_from_global_store_inner(use_postgres_global_store: bool) -> Result<()> {
    let fixture = TestFixture::new(use_postgres_global_store).await;

    let segment_id = Uuid::new_v4();
    fixture.initialize_segment_metadata(segment_id).await;

    let wal_entries = vec![
        WalEntry {
            _xact_id: TransactionId(0),
            id: "row1".to_string(),
            ..Default::default()
        },
        WalEntry {
            _xact_id: TransactionId(1),
            id: "row2".to_string(),
            ..Default::default()
        },
    ];
    fixture.write_wal_to_segment(segment_id, wal_entries).await;

    let global_store = fixture.config().global_store.clone();

    // Delete entries prior to xact_id 1.
    global_store
        .delete_segment_wal_entries_up_to_xact_id(&[segment_id], TransactionId(1))
        .await?;

    // Verify only entries with xact_id >= 1 remain.
    let stats = global_store
        .query_segment_wal_entries_xact_id_statistic(
            &[segment_id],
            SegmentWalEntriesXactIdStatistic::Min,
            None,
        )
        .await?;

    assert_eq!(stats[0], Some(TransactionId(1)));

    fixture
        .basic_fixture
        .validate_vacuum(ValidateVacuumArgs {
            object_ids: Some(&[FullObjectId::default()]),
            schema: Some(fixture.make_full_schema()),
        })
        .await;

    Ok(())
}

#[tokio::test]
async fn test_delete_from_segments_basic() -> Result<()> {
    run_test_with_global_stores(test_delete_from_segments_basic_inner).await
}

async fn test_delete_from_segments_basic_inner(use_postgres_global_store: bool) -> Result<()> {
    let fixture = TestFixture::new(use_postgres_global_store).await;

    let segment_id = Uuid::new_v4();
    fixture.initialize_segment_metadata(segment_id).await;

    let wal_entries = vec![
        WalEntry {
            _xact_id: TransactionId(0),
            id: "row1".to_string(),
            data: [("field1".to_string(), json!("a"))].into_iter().collect(),
            ..Default::default()
        },
        WalEntry {
            _xact_id: TransactionId(1),
            id: "row2".to_string(),
            data: [("field3".to_string(), json!("b"))].into_iter().collect(),
            ..Default::default()
        },
        WalEntry {
            _xact_id: TransactionId(2),
            id: "row1".to_string(),
            data: [("field1".to_string(), json!("c"))].into_iter().collect(),
            ..Default::default()
        },
    ];
    fixture.write_wal_to_segment(segment_id, wal_entries).await;

    process_object_wal(
        fixture.basic_fixture.process_wal_input(),
        Default::default(),
        Default::default(),
    )
    .await?;
    compact_segment_wal(
        fixture.compact_wal_input(segment_id),
        Default::default(),
        Default::default(),
    )
    .await?;

    let expected_docs = make_compacted_wal_entries(vec![
        WalEntry {
            _pagination_key: PaginationKey(0),
            _xact_id: TransactionId(1),
            id: "row2".to_string(),
            data: [("field3".to_string(), json!("b"))].into_iter().collect(),
            ..Default::default()
        },
        WalEntry {
            _pagination_key: PaginationKey(0),
            _xact_id: TransactionId(2),
            id: "row1".to_string(),
            data: [("field1".to_string(), json!("c"))].into_iter().collect(),
            ..Default::default()
        },
    ]);

    let docs = fixture.read_segment_docs(segment_id).await;
    assert_eq!(docs.len(), 2);
    assert_hashmap_eq(&docs, &expected_docs);

    // Delete entries with xact_id < 1.
    delete_from_segments_for_testing(DeleteFromSegmentsInput {
        segment_ids: &[segment_id],
        index_store: &fixture.config().index,
        schema: &fixture.make_full_schema(),
        global_store: fixture.config().global_store.clone(),
        locks_manager: &*fixture.config().locks_manager,
        min_retained_xact_id: TransactionId(1),
        dry_run: false,
    })
    .await;

    // Verify WAL deletion.
    let remaining_entries = fixture.read_segment_wal_entries(segment_id).await;
    assert_eq!(remaining_entries.len(), 2);
    assert_eq!(remaining_entries[0].0, TransactionId(1));
    assert_eq!(remaining_entries[0].1.len(), 1);
    assert_eq!(remaining_entries[0].1[0].id, "row2");
    assert_eq!(remaining_entries[0].1[0].data["field3"], json!("b"));
    assert_eq!(remaining_entries[1].0, TransactionId(2));
    assert_eq!(remaining_entries[1].1.len(), 1);
    assert_eq!(remaining_entries[1].1[0].id, "row1");
    assert_eq!(remaining_entries[1].1[0].data["field1"], json!("c"));

    // Verify index deletion.
    let docs = fixture.read_segment_docs(segment_id).await;
    assert_eq!(docs.len(), 2);
    assert_hashmap_eq(&docs, &expected_docs);

    fixture
        .basic_fixture
        .validate_vacuum(ValidateVacuumArgs {
            schema: Some(fixture.make_full_schema()),
            ..Default::default()
        })
        .await;

    Ok(())
}

#[tokio::test]
async fn test_delete_from_segments_with_dry_run() -> Result<()> {
    run_test_with_global_stores(test_delete_from_segments_with_dry_run_inner).await
}

async fn test_delete_from_segments_with_dry_run_inner(
    use_postgres_global_store: bool,
) -> Result<()> {
    let fixture = TestFixture::new(use_postgres_global_store).await;

    let segment_id = Uuid::new_v4();
    fixture.initialize_segment_metadata(segment_id).await;

    let wal_entries = vec![
        WalEntry {
            _xact_id: TransactionId(0),
            id: "row1".to_string(),
            data: [("field1".to_string(), json!("a"))].into_iter().collect(),
            ..Default::default()
        },
        WalEntry {
            _xact_id: TransactionId(1),
            id: "row2".to_string(),
            data: [("field1".to_string(), json!("b"))].into_iter().collect(),
            ..Default::default()
        },
    ];
    fixture.write_wal_to_segment(segment_id, wal_entries).await;

    process_object_wal(
        fixture.basic_fixture.process_wal_input(),
        Default::default(),
        Default::default(),
    )
    .await?;
    compact_segment_wal(
        fixture.compact_wal_input(segment_id),
        Default::default(),
        Default::default(),
    )
    .await?;

    let expected_docs = make_compacted_wal_entries(vec![
        WalEntry {
            _xact_id: TransactionId(0),
            id: "row1".to_string(),
            data: [("field1".to_string(), json!("a"))].into_iter().collect(),
            ..Default::default()
        },
        WalEntry {
            _xact_id: TransactionId(1),
            id: "row2".to_string(),
            data: [("field1".to_string(), json!("b"))].into_iter().collect(),
            ..Default::default()
        },
    ]);

    // Verify that nothing is deleted if we do a dry run.
    delete_from_segments_for_testing(DeleteFromSegmentsInput {
        segment_ids: &[segment_id],
        index_store: &fixture.config().index,
        schema: &fixture.make_full_schema(),
        global_store: fixture.config().global_store.clone(),
        locks_manager: &*fixture.config().locks_manager,
        min_retained_xact_id: TransactionId(1),
        dry_run: true,
    })
    .await;
    let remaining_entries = fixture.read_segment_wal_entries(segment_id).await;
    assert_eq!(remaining_entries.len(), 2);
    assert_eq!(remaining_entries[0].0, TransactionId(0));
    assert_eq!(remaining_entries[0].1[0].id, "row1");
    assert_eq!(remaining_entries[1].0, TransactionId(1));
    assert_eq!(remaining_entries[1].1[0].id, "row2");
    let docs = fixture.read_segment_docs(segment_id).await;
    assert_eq!(docs.len(), 2);
    assert_hashmap_eq(&docs, &expected_docs);

    // Delete entries with xact_id < 1.
    delete_from_segments_for_testing(DeleteFromSegmentsInput {
        segment_ids: &[segment_id],
        index_store: &fixture.config().index,
        schema: &fixture.make_full_schema(),
        global_store: fixture.config().global_store.clone(),
        locks_manager: &*fixture.config().locks_manager,
        min_retained_xact_id: TransactionId(1),
        dry_run: false,
    })
    .await;

    // Verify only entries with xact_id >= 1 remain in both WAL and index.
    let remaining_entries = fixture.read_segment_wal_entries(segment_id).await;
    assert_eq!(remaining_entries.len(), 1);
    assert_eq!(remaining_entries[0].0, TransactionId(1));
    assert_eq!(remaining_entries[0].1.len(), 1);
    assert_eq!(remaining_entries[0].1[0].id, "row2");
    assert_eq!(remaining_entries[0].1[0].data["field1"], json!("b"));
    let docs = fixture.read_segment_docs(segment_id).await;
    assert_eq!(docs.len(), 1);
    assert_eq!(
        docs.values().next().unwrap().wal_entry._xact_id,
        TransactionId(1)
    );

    // Run another compaction immediately after deletion and check again.
    compact_segment_wal(
        fixture.compact_wal_input(segment_id),
        Default::default(),
        Default::default(),
    )
    .await?;

    let remaining_entries = fixture.read_segment_wal_entries(segment_id).await;
    assert_eq!(remaining_entries.len(), 1);
    assert_eq!(remaining_entries[0].0, TransactionId(1));
    assert_eq!(remaining_entries[0].1.len(), 1);
    assert_eq!(remaining_entries[0].1[0].id, "row2");
    assert_eq!(remaining_entries[0].1[0].data["field1"], json!("b"));
    let docs = fixture.read_segment_docs(segment_id).await;
    assert_eq!(docs.len(), 1);
    assert_eq!(
        docs.values().next().unwrap().wal_entry._xact_id,
        TransactionId(1)
    );

    fixture
        .basic_fixture
        .validate_vacuum(ValidateVacuumArgs {
            object_ids: None,
            schema: Some(fixture.make_full_schema()),
        })
        .await;
    fixture
        .basic_fixture
        .validate_vacuum(ValidateVacuumArgs {
            object_ids: Some(&[FullObjectId::default()]),
            schema: Some(fixture.make_full_schema()),
        })
        .await;

    Ok(())
}

#[tokio::test]
async fn test_delete_from_segments_with_interleaved_updates() -> Result<()> {
    run_test_with_global_stores(test_delete_from_segments_with_interleaved_updates_inner).await
}

async fn test_delete_from_segments_with_interleaved_updates_inner(
    use_postgres_global_store: bool,
) -> Result<()> {
    let fixture = TestFixture::new(use_postgres_global_store).await;

    let segment_id = Uuid::new_v4();
    fixture.initialize_segment_metadata(segment_id).await;

    let wal_entries = vec![WalEntry {
        _xact_id: TransactionId(0),
        id: "row1".to_string(),
        data: [("field1".to_string(), json!("v1"))].into_iter().collect(),
        ..Default::default()
    }];
    fixture.write_wal_to_segment(segment_id, wal_entries).await;

    process_object_wal(
        fixture.basic_fixture.process_wal_input(),
        Default::default(),
        Default::default(),
    )
    .await?;
    compact_segment_wal(
        fixture.compact_wal_input(segment_id),
        Default::default(),
        Default::default(),
    )
    .await?;

    let wal_entries = vec![
        WalEntry {
            _xact_id: TransactionId(1),
            id: "row1".to_string(),
            data: [("field1".to_string(), json!("v2"))].into_iter().collect(),
            ..Default::default()
        },
        WalEntry {
            _xact_id: TransactionId(1),
            id: "row2".to_string(),
            data: [("field1".to_string(), json!("v1"))].into_iter().collect(),
            ..Default::default()
        },
    ];
    fixture.write_wal_to_segment(segment_id, wal_entries).await;

    // Delete entries with xact_id < 1.
    delete_from_segments_for_testing(DeleteFromSegmentsInput {
        segment_ids: &[segment_id],
        index_store: &fixture.config().index,
        schema: &fixture.make_full_schema(),
        global_store: fixture.config().global_store.clone(),
        locks_manager: &*fixture.config().locks_manager,
        min_retained_xact_id: TransactionId(1),
        dry_run: false,
    })
    .await;

    // Run compaction again.
    compact_segment_wal(
        fixture.compact_wal_input(segment_id),
        Default::default(),
        Default::default(),
    )
    .await?;

    // Verify that the first row is deleted entirely since it was created
    // in a deleted transaction. The second row should persist.
    let remaining_entries = fixture.read_segment_wal_entries(segment_id).await;
    assert_eq!(remaining_entries.len(), 1);
    assert_eq!(remaining_entries[0].0, TransactionId(1));
    assert_eq!(remaining_entries[0].1.len(), 2);
    assert_eq!(remaining_entries[0].1[0].id, "row1");
    assert_eq!(remaining_entries[0].1[0].data["field1"], json!("v2"));
    assert_eq!(remaining_entries[0].1[1].id, "row2");
    assert_eq!(remaining_entries[0].1[1].data["field1"], json!("v1"));

    let docs = fixture.read_segment_docs(segment_id).await;
    assert_eq!(docs.len(), 2);
    let expected_docs = make_compacted_wal_entries(vec![
        WalEntry {
            _xact_id: TransactionId(1),
            id: "row1".to_string(),
            data: [("field1".to_string(), json!("v2"))].into_iter().collect(),
            ..Default::default()
        },
        WalEntry {
            _xact_id: TransactionId(1),
            id: "row2".to_string(),
            data: [("field1".to_string(), json!("v1"))].into_iter().collect(),
            ..Default::default()
        },
    ]);
    assert_hashmap_eq(&docs, &expected_docs);

    fixture
        .basic_fixture
        .validate_vacuum(ValidateVacuumArgs {
            schema: Some(fixture.make_full_schema()),
            ..Default::default()
        })
        .await;

    Ok(())
}

#[tokio::test]
async fn test_delete_from_segments_with_multiple_inserts() -> Result<()> {
    run_test_with_global_stores(test_delete_from_segments_with_multiple_inserts_inner).await
}

async fn test_delete_from_segments_with_multiple_inserts_inner(
    use_postgres_global_store: bool,
) -> Result<()> {
    let fixture = TestFixture::new(use_postgres_global_store).await;

    let segment_id = Uuid::new_v4();
    fixture.initialize_segment_metadata(segment_id).await;

    // Write row1 and row2 in the first insert.
    let wal_entries = vec![
        WalEntry {
            _xact_id: TransactionId(0),
            id: "row1".to_string(),
            // data: [("value".to_string(), json!("v1"))].into_iter().collect(),
            data: json!({
                "field1": "foo",
                "field2": 42,
                "field3": json!({ "input": "kiwi" }),
            })
            .as_object()
            .unwrap()
            .clone(),
            ..Default::default()
        },
        WalEntry {
            _xact_id: TransactionId(0),
            id: "row2".to_string(),
            // data: [("value".to_string(), json!("v1"))].into_iter().collect(),
            data: json!({ "field1": "b" }).as_object().unwrap().clone(),
            ..Default::default()
        },
    ];
    fixture.write_wal_to_segment(segment_id, wal_entries).await;

    // Update row1 and add row3 in the second insert.
    let wal_entries = vec![
        WalEntry {
            _xact_id: TransactionId(1),
            id: "row1".to_string(),
            _is_merge: Some(true),
            data: json!({
                "field1": "bar",
                "field3": json!({ "output": "orange" }),
            })
            .as_object()
            .unwrap()
            .clone(),
            ..Default::default()
        },
        WalEntry {
            _xact_id: TransactionId(1),
            id: "row3".to_string(),
            _is_merge: Some(true),
            data: json!({ "field1": "d" }).as_object().unwrap().clone(),
            ..Default::default()
        },
    ];
    fixture.write_wal_to_segment(segment_id, wal_entries).await;

    process_object_wal(
        fixture.basic_fixture.process_wal_input(),
        Default::default(),
        Default::default(),
    )
    .await?;

    let wal_entries = fixture.read_segment_wal_entries(segment_id).await;
    assert_eq!(wal_entries.len(), 2);
    assert_eq!(wal_entries[0].0, TransactionId(0));
    assert_eq!(wal_entries[0].1.len(), 2);
    assert_eq!(wal_entries[0].1[0].id, "row1");
    assert_eq!(wal_entries[0].1[1].id, "row2");
    assert_eq!(wal_entries[1].0, TransactionId(1));
    assert_eq!(wal_entries[1].1.len(), 2);
    assert_eq!(wal_entries[1].1[0].id, "row1");
    assert_eq!(wal_entries[1].1[1].id, "row3");

    compact_segment_wal(
        fixture.compact_wal_input(segment_id),
        Default::default(),
        Default::default(),
    )
    .await?;

    let docs = fixture.read_segment_docs(segment_id).await;
    assert_eq!(docs.len(), 3);
    let expected_docs = make_compacted_wal_entries(vec![
        WalEntry {
            _xact_id: TransactionId(1),
            id: "row1".to_string(),
            data: [
                ("field1".to_string(), json!("bar")),
                ("field2".to_string(), json!(42)),
                (
                    "field3".to_string(),
                    json!({ "input": "kiwi", "output": "orange" }),
                ),
            ]
            .into_iter()
            .collect(),
            ..Default::default()
        },
        WalEntry {
            _xact_id: TransactionId(0),
            id: "row2".to_string(),
            data: [("field1".to_string(), json!("b"))].into_iter().collect(),
            ..Default::default()
        },
        WalEntry {
            _xact_id: TransactionId(1),
            id: "row3".to_string(),
            data: [("field1".to_string(), json!("d"))].into_iter().collect(),
            ..Default::default()
        },
    ]);
    assert_hashmap_eq(&docs, &expected_docs);

    // Delete entries with xact_id < 1.
    delete_from_segments_for_testing(DeleteFromSegmentsInput {
        segment_ids: &[segment_id],
        index_store: &fixture.config().index,
        schema: &fixture.make_full_schema(),
        global_store: fixture.config().global_store.clone(),
        locks_manager: &*fixture.config().locks_manager,
        min_retained_xact_id: TransactionId(1),
        dry_run: false,
    })
    .await;

    fixture
        .basic_fixture
        .validate_vacuum(ValidateVacuumArgs {
            schema: Some(fixture.make_full_schema()),
            ..Default::default()
        })
        .await;

    // Verify only row1 remains, since row2 only existed in xact_id 0.
    let wal_entries = fixture.read_segment_wal_entries(segment_id).await;
    assert_eq!(wal_entries.len(), 1);
    assert_eq!(wal_entries[0].0, TransactionId(1));
    assert_eq!(wal_entries[0].1.len(), 2);
    assert_eq!(wal_entries[0].1[0].id, "row1");
    assert_eq!(wal_entries[0].1[1].id, "row3");

    // row2 should be gone, but row1 was compacted after the deletion point
    // so it should still exist in the index.
    let docs = fixture.read_segment_docs(segment_id).await;
    assert_eq!(docs.len(), 2);
    let expected_docs = make_compacted_wal_entries(vec![
        WalEntry {
            _xact_id: TransactionId(1),
            id: "row1".to_string(),
            data: [
                ("field1".to_string(), json!("bar")),
                ("field2".to_string(), json!(42)),
                (
                    "field3".to_string(),
                    json!({ "input": "kiwi", "output": "orange" }),
                ),
            ]
            .into_iter()
            .collect(),
            ..Default::default()
        },
        WalEntry {
            _xact_id: TransactionId(1),
            id: "row3".to_string(),
            data: [("field1".to_string(), json!("d"))].into_iter().collect(),
            ..Default::default()
        },
    ]);
    assert_hashmap_eq(&docs, &expected_docs);

    // Rerun compaction and verify row1 is still gone.
    compact_segment_wal(
        fixture.compact_wal_input(segment_id),
        Default::default(),
        Default::default(),
    )
    .await?;

    fixture
        .basic_fixture
        .validate_vacuum(ValidateVacuumArgs {
            schema: Some(fixture.make_full_schema()),
            ..Default::default()
        })
        .await;

    let docs = fixture.read_segment_docs(segment_id).await;
    assert_eq!(docs.len(), 2);
    assert_hashmap_eq(&docs, &expected_docs);

    // Delete WAL entries up to xact_id 2.
    delete_from_segments_for_testing(DeleteFromSegmentsInput {
        segment_ids: &[segment_id],
        index_store: &fixture.config().index,
        schema: &fixture.make_full_schema(),
        global_store: fixture.config().global_store.clone(),
        locks_manager: &*fixture.config().locks_manager,
        min_retained_xact_id: TransactionId(2),
        dry_run: false,
    })
    .await;

    // Verify that the WAL and index are both empty.
    let wal_entries = fixture.read_segment_wal_entries(segment_id).await;
    assert_eq!(wal_entries.len(), 0);
    let docs = fixture.read_segment_docs(segment_id).await;
    assert_eq!(docs.len(), 0);

    // Rerun compaction and verify that the index is still empty.
    compact_segment_wal(
        fixture.compact_wal_input(segment_id),
        Default::default(),
        Default::default(),
    )
    .await?;
    let docs = fixture.read_segment_docs(segment_id).await;
    assert_eq!(docs.len(), 0);

    Ok(())
}

#[tokio::test]
async fn test_delete_from_segments_then_reprocess_object_wal() -> Result<()> {
    run_test_with_global_stores(test_delete_from_segments_then_reprocess_object_wal_inner).await
}

async fn test_delete_from_segments_then_reprocess_object_wal_inner(
    use_postgres_global_store: bool,
) -> Result<()> {
    let fixture = TestFixture::new(use_postgres_global_store).await;

    let segment_id = Uuid::new_v4();
    fixture.initialize_segment_metadata(segment_id).await;

    let wal_entries = vec![
        WalEntry {
            _xact_id: TransactionId(0),
            id: "row1".to_string(),
            data: [("field1".to_string(), json!("zoo"))].into_iter().collect(),
            ..Default::default()
        },
        WalEntry {
            _xact_id: TransactionId(1),
            id: "row2".to_string(),
            data: [("field1".to_string(), json!("pie"))].into_iter().collect(),
            ..Default::default()
        },
    ];
    fixture.write_wal_to_segment(segment_id, wal_entries).await;

    process_object_wal(
        fixture.basic_fixture.process_wal_input(),
        Default::default(),
        Default::default(),
    )
    .await?;
    compact_segment_wal(
        fixture.compact_wal_input(segment_id),
        Default::default(),
        Default::default(),
    )
    .await?;

    // Verify both rows are present in WAL and index before deletion.
    let wal_entries = fixture.read_segment_wal_entries(segment_id).await;
    assert_eq!(wal_entries.len(), 2);
    assert_eq!(wal_entries[0].1[0].id, "row1");
    assert_eq!(wal_entries[0].1[0].data["field1"], json!("zoo"));
    assert_eq!(wal_entries[1].1[0].id, "row2");
    assert_eq!(wal_entries[1].1[0].data["field1"], json!("pie"));

    let docs = fixture.read_segment_docs(segment_id).await;
    assert_eq!(docs.len(), 2);
    assert!(docs.values().any(|doc| doc.wal_entry.id == "row1"));
    assert!(docs.values().any(|doc| doc.wal_entry.id == "row2"));

    // Run deletes up to xact_id 1.
    delete_from_segments_for_testing(DeleteFromSegmentsInput {
        segment_ids: &[segment_id],
        index_store: &fixture.config().index,
        schema: &fixture.make_full_schema(),
        global_store: fixture.config().global_store.clone(),
        locks_manager: &*fixture.config().locks_manager,
        min_retained_xact_id: TransactionId(1),
        dry_run: false,
    })
    .await;

    // Verify only row2 remains after deletion and compaction.
    let wal_entries = fixture.read_segment_wal_entries(segment_id).await;
    assert_eq!(wal_entries.len(), 1);
    assert_eq!(wal_entries[0].1[0].id, "row2");

    let docs = fixture.read_segment_docs(segment_id).await;
    assert_eq!(docs.len(), 1);
    assert_eq!(docs.values().next().unwrap().wal_entry.id, "row2");

    // Reprocess the WAL. Since processing starts at last_processed_xact_id by
    // default, we won't reintroduce the entries we just deleted.
    process_object_wal(
        fixture.basic_fixture.process_wal_input(),
        Default::default(),
        Default::default(),
    )
    .await?;

    // Re-compact and verify row1 is still gone.
    // We are testing here that the tantivy meta was updated correctly during the
    // delete operation, since if it wasn't, re-compaction would flash the index
    // to the previous snapshot, clobbering the delete we just ran.
    compact_segment_wal(
        fixture.compact_wal_input(segment_id),
        CompactSegmentWalOptionalInput {
            start_xact_id: Some(TransactionId(0)),
            ..Default::default()
        },
        Default::default(),
    )
    .await?;

    let wal_entries = fixture.read_segment_wal_entries(segment_id).await;
    assert_eq!(wal_entries.len(), 1);
    assert_eq!(wal_entries[0].1[0].id, "row2");

    let docs = fixture.read_segment_docs(segment_id).await;
    assert_eq!(docs.len(), 1);
    assert_eq!(docs.values().next().unwrap().wal_entry.id, "row2");

    Ok(())
}

static FULL_OBJECT_ID0: Lazy<FullObjectId<'static>> = Lazy::new(|| FullObjectId {
    object_type: ObjectType::Experiment,
    object_id: ObjectId::new("obj0").unwrap(),
});

static FULL_OBJECT_ID1: Lazy<FullObjectId<'static>> = Lazy::new(|| FullObjectId {
    object_type: ObjectType::Experiment,
    object_id: ObjectId::new("obj1").unwrap(),
});

static FULL_OBJECT_ID2: Lazy<FullObjectId<'static>> = Lazy::new(|| FullObjectId {
    object_type: ObjectType::Experiment,
    object_id: ObjectId::new("obj2").unwrap(),
});

fn basic_wal_entries(full_object_id: FullObjectId) -> Vec<WalEntry> {
    vec![
        WalEntry {
            _pagination_key: PaginationKey(0),
            _xact_id: TransactionId(0),
            _object_type: full_object_id.object_type,
            _object_id: full_object_id.object_id.to_owned(),
            created: DateTime::<Utc>::from_timestamp_nanos(1000),
            _is_merge: Some(true),
            id: "row0".to_string(),
            data: json!({
                "field1": "gop",
                "field2": 42,
            })
            .as_object()
            .unwrap()
            .clone(),
            ..Default::default()
        },
        WalEntry {
            _pagination_key: PaginationKey(1),
            _xact_id: TransactionId(0),
            _object_type: full_object_id.object_type,
            _object_id: full_object_id.object_id.to_owned(),
            created: DateTime::<Utc>::from_timestamp_nanos(2000),
            id: "row1".to_string(),
            data: json!({
                "field1": "foo",
                "field3": json!({ "input": "bar" }),
            })
            .as_object()
            .unwrap()
            .clone(),
            ..Default::default()
        },
        WalEntry {
            _pagination_key: PaginationKey(2),
            _xact_id: TransactionId(1),
            _object_type: full_object_id.object_type,
            _object_id: full_object_id.object_id.to_owned(),
            created: DateTime::<Utc>::from_timestamp_nanos(3000),
            _is_merge: Some(true),
            id: "row1".to_string(),
            data: json!({
                "field1": "grue",
                "field2": 99,
                "field3": json!({ "output": "baz" }),
            })
            .as_object()
            .unwrap()
            .clone(),
            ..Default::default()
        },
        WalEntry {
            _pagination_key: PaginationKey(3),
            _xact_id: TransactionId(1),
            _object_type: full_object_id.object_type,
            _object_id: full_object_id.object_id.to_owned(),
            created: DateTime::<Utc>::from_timestamp_nanos(4000),
            _is_merge: Some(true),
            id: "row2".to_string(),
            root_span_id: "r2".to_string(),
            data: json!({
                "field3": json!({ "metadata": "yes" }),
            })
            .as_object()
            .unwrap()
            .clone(),
            ..Default::default()
        },
    ]
}

fn basic_wal_entries_compacted(
    object_id: FullObjectId,
    deletion_state: &DeletionState,
) -> HashMap<FullRowIdOwned, IndexDocument> {
    let docs = match deletion_state {
        DeletionState::NotDeleted => vec![
            WalEntry {
                _pagination_key: PaginationKey(0),
                _xact_id: TransactionId(0),
                _object_type: object_id.object_type,
                _object_id: object_id.object_id.to_owned(),
                created: DateTime::<Utc>::from_timestamp_nanos(1000),
                id: "row0".to_string(),
                data: json!({
                    "field1": "gop",
                    "field2": 42,
                })
                .as_object()
                .unwrap()
                .clone(),
                ..Default::default()
            },
            WalEntry {
                _pagination_key: PaginationKey(1),
                _xact_id: TransactionId(1),
                _object_type: object_id.object_type,
                _object_id: object_id.object_id.to_owned(),
                created: DateTime::<Utc>::from_timestamp_nanos(2000),
                id: "row1".to_string(),
                data: json!({
                    "field1": "grue",
                    "field2": 99,
                    "field3": json!({ "input": "bar", "output": "baz" }),
                })
                .as_object()
                .unwrap()
                .clone(),
                ..Default::default()
            },
            WalEntry {
                _pagination_key: PaginationKey(3),
                _xact_id: TransactionId(1),
                _object_type: object_id.object_type,
                _object_id: object_id.object_id.to_owned(),
                created: DateTime::<Utc>::from_timestamp_nanos(4000),
                id: "row2".to_string(),
                root_span_id: "r2".to_string(),
                data: json!({
                    "field3": json!({ "metadata": "yes" }),
                })
                .as_object()
                .unwrap()
                .clone(),
                ..Default::default()
            },
        ],
        DeletionState::DeletedBeforeCompaction => vec![
            WalEntry {
                _pagination_key: PaginationKey(2),
                _xact_id: TransactionId(1),
                _object_type: object_id.object_type,
                _object_id: object_id.object_id.to_owned(),
                created: DateTime::<Utc>::from_timestamp_nanos(3000),
                id: "row1".to_string(),
                data: json!({
                    "field1": "grue",
                    "field2": 99,
                    "field3": json!({ "output": "baz" }),
                })
                .as_object()
                .unwrap()
                .clone(),
                ..Default::default()
            },
            WalEntry {
                _pagination_key: PaginationKey(3),
                _xact_id: TransactionId(1),
                _object_type: object_id.object_type,
                _object_id: object_id.object_id.to_owned(),
                created: DateTime::<Utc>::from_timestamp_nanos(4000),
                id: "row2".to_string(),
                root_span_id: "r2".to_string(),
                data: json!({
                    "field3": json!({ "metadata": "yes" }),
                })
                .as_object()
                .unwrap()
                .clone(),
                ..Default::default()
            },
        ],
        DeletionState::DeletedAfterCompaction => vec![
            WalEntry {
                _pagination_key: PaginationKey(1),
                _xact_id: TransactionId(1),
                _object_type: object_id.object_type,
                _object_id: object_id.object_id.to_owned(),
                created: DateTime::<Utc>::from_timestamp_nanos(2000),
                id: "row1".to_string(),
                data: json!({
                    "field1": "grue",
                    "field2": 99,
                    "field3": json!({ "input": "bar", "output": "baz" }),
                })
                .as_object()
                .unwrap()
                .clone(),
                ..Default::default()
            },
            WalEntry {
                _pagination_key: PaginationKey(3),
                _xact_id: TransactionId(1),
                _object_type: object_id.object_type,
                _object_id: object_id.object_id.to_owned(),
                created: DateTime::<Utc>::from_timestamp_nanos(4000),
                id: "row2".to_string(),
                root_span_id: "r2".to_string(),
                data: json!({
                    "field3": json!({ "metadata": "yes" }),
                })
                .as_object()
                .unwrap()
                .clone(),
                ..Default::default()
            },
        ],
    };
    make_compacted_wal_entries(docs)
}

#[derive(Clone)]
struct SegmentArgs<'a> {
    fixture: &'a TestFixture,
    object_id: FullObjectId<'a>,
    segment_id: Uuid,
}

#[derive(Clone)]
struct CheckSegmentWalArgs<'a> {
    segment_args: &'a SegmentArgs<'a>,
    deleted: bool,
    compacted: bool,
}

enum DeletionState {
    NotDeleted,
    DeletedBeforeCompaction,
    DeletedAfterCompaction,
}

#[derive(Clone)]
struct CheckSegmentIndexArgs<'a> {
    segment_args: &'a SegmentArgs<'a>,
    deletion_state: &'a DeletionState,
    xact_id_range: RangeInclusive<TransactionId>,
}

async fn check_segment_wal(args: &CheckSegmentWalArgs<'_>) {
    let fixture = args.segment_args.fixture;
    let segment_id = args.segment_args.segment_id;

    let wal_entries = fixture.read_segment_wal_entries(segment_id).await;

    if !args.deleted {
        assert_eq!(wal_entries.len(), 2);

        assert_eq!(wal_entries[0].0, TransactionId(0));
        assert_eq!(wal_entries[0].1.len(), 2);
        assert_eq!(wal_entries[0].1[0].id, "row0");
        assert_eq!(wal_entries[0].1[0].data["field1"], json!("gop"));
        assert_eq!(wal_entries[0].1[0].data["field2"], json!(42));
        assert_eq!(wal_entries[0].1[1].id, "row1");
        assert_eq!(wal_entries[0].1[1].data["field1"], json!("foo"));
        assert_eq!(wal_entries[0].1[1].data["field3"], json!({"input": "bar"}));

        assert_eq!(wal_entries[1].0, TransactionId(1));
        assert_eq!(wal_entries[1].1.len(), 2);
        assert_eq!(wal_entries[1].1[0].id, "row1");
        assert_eq!(wal_entries[1].1[0].data["field1"], json!("grue"));
        assert_eq!(wal_entries[1].1[0].data["field2"], json!(99));
        assert_eq!(wal_entries[1].1[0].data["field3"], json!({"output": "baz"}));
        assert_eq!(wal_entries[1].1[1].id, "row2");
        assert_eq!(
            wal_entries[1].1[1].data["field3"],
            json!({"metadata": "yes"})
        );
    } else {
        assert_eq!(wal_entries.len(), 1);

        // The WAL entries for xact_id 0 should be gone.
        assert_eq!(wal_entries[0].0, TransactionId(1));
        assert_eq!(wal_entries[0].1.len(), 2);
        assert_eq!(wal_entries[0].1[0].id, "row1");
        assert_eq!(wal_entries[0].1[0].data["field1"], json!("grue"));
        assert_eq!(wal_entries[0].1[0].data["field2"], json!(99));
        assert_eq!(wal_entries[0].1[0].data["field3"], json!({"output": "baz"}));
        assert_eq!(wal_entries[0].1[1].id, "row2");
        assert_eq!(
            wal_entries[0].1[1].data["field3"],
            json!({"metadata": "yes"})
        );
    }

    assert_eq!(
        fixture
            .config()
            .global_store
            .query_segment_wal_entries_xact_id_statistic(
                &[segment_id],
                SegmentWalEntriesXactIdStatistic::Min,
                Some(args.compacted)
            )
            .await
            .unwrap()
            .remove(0),
        Some(TransactionId(if args.deleted { 1 } else { 0 }))
    );
    assert_eq!(
        fixture
            .config()
            .global_store
            .query_segment_wal_entries_xact_id_statistic(
                &[segment_id],
                SegmentWalEntriesXactIdStatistic::Min,
                Some(!args.compacted)
            )
            .await
            .unwrap()
            .remove(0),
        None,
    );
    assert_eq!(
        fixture
            .config()
            .global_store
            .query_segment_wal_entries_xact_id_statistic(
                &[segment_id],
                SegmentWalEntriesXactIdStatistic::Max,
                Some(args.compacted)
            )
            .await
            .unwrap()
            .remove(0),
        Some(TransactionId(1))
    );
    assert_eq!(
        fixture
            .config()
            .global_store
            .query_segment_wal_entries_xact_id_statistic(
                &[segment_id],
                SegmentWalEntriesXactIdStatistic::Max,
                Some(!args.compacted)
            )
            .await
            .unwrap()
            .remove(0),
        None,
    );
}

async fn check_segment_index(args: &CheckSegmentIndexArgs<'_>) {
    let fixture = args.segment_args.fixture;
    let object_id = args.segment_args.object_id;
    let segment_id = args.segment_args.segment_id;

    let actual_docs = fixture.read_segment_docs(segment_id).await;
    let expected_docs = basic_wal_entries_compacted(object_id, args.deletion_state);
    assert_hashmap_eq(&actual_docs, &expected_docs);

    // Check the index WAL entries, which should also match the expected
    // compacted entries.
    let object_ids_owned = &[object_id.to_owned()];
    let actual_index_wal_entries = fixture.read_index_wal_entries(object_ids_owned).await;

    let expected_index_wal_entries = basic_wal_entries_compacted(object_id, args.deletion_state);

    // Convert to a map keyed by row ID.
    let expected_index_wal_entries_by_id: HashMap<String, IndexDocument> =
        expected_index_wal_entries
            .into_iter()
            .map(|(full_row_id, doc)| (full_row_id.id, doc))
            .collect();

    assert_hashmap_eq(&actual_index_wal_entries, &expected_index_wal_entries_by_id);

    let min_pagination_key = match args.deletion_state {
        DeletionState::NotDeleted => 0,
        DeletionState::DeletedBeforeCompaction => 2,
        DeletionState::DeletedAfterCompaction => 1,
    };
    let min_created = match args.deletion_state {
        DeletionState::NotDeleted => 1000_i64,
        DeletionState::DeletedBeforeCompaction => 3000_i64,
        DeletionState::DeletedAfterCompaction => 2000_i64,
    };

    if matches!(args.deletion_state, DeletionState::DeletedAfterCompaction) {
        // NOTE(austin): For some reason, the tantivy index does not always keep
        // exact min/max field statistics after a delete. In particular, there is
        // a discrepancy between the tantivy docs and the field statistics for
        // the "delete after compaction" case.
        // Re-enable this check once we fix this issue.
        log::warn!("Skipping field statistics check for DeletedAfterCompaction case");
        return;
    }

    let field_statistics = fixture
        .config()
        .global_store
        .query_field_statistics(&[segment_id], &FIELD_STATISTICS_FIELD_NAMES)
        .await
        .unwrap();
    let expected_field_statistics = [
        (
            "_pagination_key".to_string(),
            SegmentFieldStatistics::new(min_pagination_key, 3).unwrap(),
        ),
        (
            "created".to_string(),
            SegmentFieldStatistics::new(
                MonotonicallyMappableToU64::to_u64(min_created),
                MonotonicallyMappableToU64::to_u64(4000_i64),
            )
            .unwrap(),
        ),
        (
            XACT_ID_FIELD.to_string(),
            SegmentFieldStatistics::new(
                MonotonicallyMappableToU64::to_u64(args.xact_id_range.start().0),
                MonotonicallyMappableToU64::to_u64(args.xact_id_range.end().0),
            )
            .unwrap(),
        ),
    ]
    .into_iter()
    .collect();
    assert_eq!(field_statistics[&segment_id], expected_field_statistics);
}

#[tokio::test]
async fn test_delete_from_multiple_segments() -> Result<()> {
    run_test_with_global_stores(test_delete_from_multiple_segments_inner).await
}

async fn test_delete_from_multiple_segments_inner(use_postgres_global_store: bool) -> Result<()> {
    let fixture = TestFixture::new(use_postgres_global_store).await;

    // Put each segment into a separate object so we can retrieve entries
    // by object_id later.
    let segment_id0 = Uuid::new_v4();
    let segment_id1 = Uuid::new_v4();
    let segment_id2 = Uuid::new_v4();
    fixture
        .initialize_segment_metadata_in_object(*FULL_OBJECT_ID0, segment_id0)
        .await;
    fixture
        .initialize_segment_metadata_in_object(*FULL_OBJECT_ID1, segment_id1)
        .await;
    fixture
        .initialize_segment_metadata_in_object(*FULL_OBJECT_ID2, segment_id2)
        .await;
    fixture
        .write_wal_to_segment(segment_id0, basic_wal_entries(*FULL_OBJECT_ID0))
        .await;
    fixture
        .write_wal_to_segment(segment_id1, basic_wal_entries(*FULL_OBJECT_ID1))
        .await;
    fixture
        .write_wal_to_segment(segment_id2, basic_wal_entries(*FULL_OBJECT_ID2))
        .await;

    let segment_id_to_segment_args = HashMap::from([
        (
            segment_id0,
            SegmentArgs {
                fixture: &fixture,
                object_id: *FULL_OBJECT_ID0,
                segment_id: segment_id0,
            },
        ),
        (
            segment_id1,
            SegmentArgs {
                fixture: &fixture,
                object_id: *FULL_OBJECT_ID1,
                segment_id: segment_id1,
            },
        ),
        (
            segment_id2,
            SegmentArgs {
                fixture: &fixture,
                object_id: *FULL_OBJECT_ID2,
                segment_id: segment_id2,
            },
        ),
    ]);

    check_segment_wal(&CheckSegmentWalArgs {
        segment_args: &segment_id_to_segment_args[&segment_id0],
        deleted: false,
        compacted: false,
    })
    .await;
    check_segment_wal(&CheckSegmentWalArgs {
        segment_args: &segment_id_to_segment_args[&segment_id1],
        deleted: false,
        compacted: false,
    })
    .await;
    check_segment_wal(&CheckSegmentWalArgs {
        segment_args: &segment_id_to_segment_args[&segment_id2],
        deleted: false,
        compacted: false,
    })
    .await;

    // Before compacting, delete from segment_id0 and verify the result.
    delete_from_segments_for_testing(DeleteFromSegmentsInput {
        segment_ids: &[segment_id0],
        index_store: &fixture.config().index,
        schema: &fixture.make_full_schema(),
        global_store: fixture.config().global_store.clone(),
        locks_manager: &*fixture.config().locks_manager,
        min_retained_xact_id: TransactionId(1),
        dry_run: false,
    })
    .await;

    check_segment_wal(&CheckSegmentWalArgs {
        segment_args: &segment_id_to_segment_args[&segment_id0],
        deleted: true,
        compacted: false,
    })
    .await;
    check_segment_wal(&CheckSegmentWalArgs {
        segment_args: &segment_id_to_segment_args[&segment_id1],
        deleted: false,
        compacted: false,
    })
    .await;
    check_segment_wal(&CheckSegmentWalArgs {
        segment_args: &segment_id_to_segment_args[&segment_id2],
        deleted: false,
        compacted: false,
    })
    .await;

    // Compact everything and verify that the deleted entries are still gone.
    for &segment_id in &[segment_id0, segment_id1, segment_id2] {
        compact_segment_wal(
            fixture.compact_wal_input(segment_id),
            Default::default(),
            Default::default(),
        )
        .await?;
    }

    check_segment_wal(&CheckSegmentWalArgs {
        segment_args: &segment_id_to_segment_args[&segment_id0],
        deleted: true,
        compacted: true,
    })
    .await;
    check_segment_wal(&CheckSegmentWalArgs {
        segment_args: &segment_id_to_segment_args[&segment_id1],
        deleted: false,
        compacted: true,
    })
    .await;
    check_segment_wal(&CheckSegmentWalArgs {
        segment_args: &segment_id_to_segment_args[&segment_id2],
        deleted: false,
        compacted: true,
    })
    .await;

    // Now we can verify that the segment index is in the correct state.
    check_segment_index(&CheckSegmentIndexArgs {
        segment_args: &segment_id_to_segment_args[&segment_id0],
        deletion_state: &DeletionState::DeletedBeforeCompaction,
        xact_id_range: TransactionId(1)..=TransactionId(1),
    })
    .await;
    check_segment_index(&CheckSegmentIndexArgs {
        segment_args: &segment_id_to_segment_args[&segment_id1],
        deletion_state: &DeletionState::NotDeleted,
        xact_id_range: TransactionId(0)..=TransactionId(1),
    })
    .await;
    check_segment_index(&CheckSegmentIndexArgs {
        segment_args: &segment_id_to_segment_args[&segment_id2],
        deletion_state: &DeletionState::NotDeleted,
        xact_id_range: TransactionId(0)..=TransactionId(1),
    })
    .await;

    // Delete from segment_id1 and verify that row0 is gone.
    delete_from_segments_for_testing(DeleteFromSegmentsInput {
        segment_ids: &[segment_id1],
        index_store: &fixture.config().index,
        schema: &fixture.make_full_schema(),
        global_store: fixture.config().global_store.clone(),
        locks_manager: &*fixture.config().locks_manager,
        min_retained_xact_id: TransactionId(1),
        dry_run: false,
    })
    .await;

    // NOTE(austin): I encountered an issue where the tantivy field statistics
    // don't update after running a delete. I tried to solve this by adding the
    // following code that runs garbage collection and a tantivy merge prior to
    // recomputing the statistics, but it didn't seem to work. I'm leaving this
    // code here in case it proves useful for an eventual fix.
    //
    // let merge_opts = MergeOpts {
    //     // target_num_segments: default_target_num_segments(),
    //     target_num_segments: 1,
    //     total_merges: None,
    //     garbage_collect: true,
    //     use_exact_num_merge_policy: true,
    // };
    // let full_schema = make_full_schema(&fixture.schema).unwrap();
    //
    // for segment_id in [segment_id0, segment_id1, segment_id2] {
    //     let index_wrapper = fixture.make_tantivy_index_wrapper(segment_id).await;
    //     let index_writer = index_wrapper.make_writer(&writer_opts).await.unwrap();
    //
    //     index_writer.garbage_collect_files().await.unwrap();
    //
    //     merge_tantivy_segments(
    //         MergeTantivySegmentsInput {
    //             segment_id,
    //             config: fixture.config.clone(),
    //             schema: &full_schema,
    //             dry_run: false,
    //             try_acquire: false,
    //         },
    //         MergeTantivySegmentsOptionalInput::default(),
    //         MergeTantivySegmentsOptions {
    //             merge_opts: merge_opts.clone(),
    //             writer_opts: TantivyIndexWriterOpts::default(),
    //             process_wal_opts: ProcessObjectWalOptions::default(),
    //         },
    //     )
    //     .await?;
    // }

    for _ in 0..2 {
        check_segment_wal(&CheckSegmentWalArgs {
            segment_args: &segment_id_to_segment_args[&segment_id0],
            deleted: true,
            compacted: true,
        })
        .await;
        check_segment_wal(&CheckSegmentWalArgs {
            segment_args: &segment_id_to_segment_args[&segment_id1],
            deleted: true,
            compacted: true,
        })
        .await;
        check_segment_wal(&CheckSegmentWalArgs {
            segment_args: &segment_id_to_segment_args[&segment_id2],
            deleted: false,
            compacted: true,
        })
        .await;

        check_segment_index(&CheckSegmentIndexArgs {
            segment_args: &segment_id_to_segment_args[&segment_id0],
            deletion_state: &DeletionState::DeletedBeforeCompaction,
            xact_id_range: TransactionId(1)..=TransactionId(1),
        })
        .await;
        check_segment_index(&CheckSegmentIndexArgs {
            segment_args: &segment_id_to_segment_args[&segment_id1],
            deletion_state: &DeletionState::DeletedAfterCompaction,
            xact_id_range: TransactionId(1)..=TransactionId(1),
        })
        .await;
        check_segment_index(&CheckSegmentIndexArgs {
            segment_args: &segment_id_to_segment_args[&segment_id2],
            deletion_state: &DeletionState::NotDeleted,
            xact_id_range: TransactionId(0)..=TransactionId(1),
        })
        .await;

        // Recompact and re-check everything just as a sanity check.
        for &segment_id in &[segment_id0, segment_id1, segment_id2] {
            compact_segment_wal(
                fixture.compact_wal_input(segment_id),
                Default::default(),
                Default::default(),
            )
            .await?;
        }
    }

    fixture
        .basic_fixture
        .validate_vacuum(ValidateVacuumArgs {
            object_ids: None,
            schema: Some(fixture.make_full_schema()),
        })
        .await;

    // Now run deletes all the way up to xact_id 2. This should soft-delete the
    // remaining rows.
    for &segment_id in &[segment_id0, segment_id1, segment_id2] {
        delete_from_segments_for_testing(DeleteFromSegmentsInput {
            segment_ids: &[segment_id],
            index_store: &fixture.config().index,
            schema: &fixture.make_full_schema(),
            global_store: fixture.config().global_store.clone(),
            locks_manager: &*fixture.config().locks_manager,
            min_retained_xact_id: TransactionId(2),
            dry_run: false,
        })
        .await;
    }

    let wal_entries = fixture.read_segment_wal_entries(segment_id0).await;
    assert!(wal_entries.is_empty());
    let wal_entries = fixture.read_segment_wal_entries(segment_id1).await;
    assert!(wal_entries.is_empty());
    let wal_entries = fixture.read_segment_wal_entries(segment_id2).await;
    assert!(wal_entries.is_empty());

    assert_eq!(
        fixture
            .config()
            .global_store
            .query_segment_wal_entries_xact_id_statistic(
                &[segment_id0, segment_id1, segment_id2],
                SegmentWalEntriesXactIdStatistic::Min,
                Some(false)
            )
            .await
            .unwrap()
            .remove(0),
        None,
    );
    assert_eq!(
        fixture
            .config()
            .global_store
            .query_segment_wal_entries_xact_id_statistic(
                &[segment_id0, segment_id1, segment_id2],
                SegmentWalEntriesXactIdStatistic::Min,
                Some(true)
            )
            .await
            .unwrap()
            .remove(0),
        None,
    );
    assert_eq!(
        fixture
            .config()
            .global_store
            .query_segment_wal_entries_xact_id_statistic(
                &[segment_id0, segment_id1, segment_id2],
                SegmentWalEntriesXactIdStatistic::Max,
                Some(false)
            )
            .await
            .unwrap()
            .remove(0),
        None,
    );
    assert_eq!(
        fixture
            .config()
            .global_store
            .query_segment_wal_entries_xact_id_statistic(
                &[segment_id0, segment_id1, segment_id2],
                SegmentWalEntriesXactIdStatistic::Max,
                Some(true)
            )
            .await
            .unwrap()
            .remove(0),
        None,
    );

    for &segment_id in &[segment_id0, segment_id1, segment_id2] {
        assert_hashmap_eq(
            &fixture.read_segment_docs(segment_id).await,
            &HashMap::new(),
        );
        let object_ids_owned = &[segment_id_to_segment_args[&segment_id].object_id.to_owned()];
        assert_hashmap_eq(
            &fixture.read_index_wal_entries(object_ids_owned).await,
            &HashMap::new(),
        );
    }

    for stateless in [true, false] {
        force_vacuum_then_validate_index_wal(VacuumThenValidateIndexWalArgs {
            config_with_store: &fixture.config(),
            full_schema: fixture.make_full_schema(),
            object_ids: Some(&[*FULL_OBJECT_ID0, *FULL_OBJECT_ID1, *FULL_OBJECT_ID2]),
            stateless,
            options: default_vacuum_index_options_for_testing(),
            expected_segment_id_cursor: if stateless {
                Some(Some(
                    *[segment_id0, segment_id1, segment_id2]
                        .iter()
                        .max()
                        .unwrap(),
                ))
            } else {
                None
            },
            dry_run: false,
        })
        .await;
    }

    Ok(())
}
