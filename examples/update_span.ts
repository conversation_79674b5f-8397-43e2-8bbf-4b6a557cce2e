import { initExperiment } from "braintrust";

const experiment = initExperiment("comparison_mismatched_traces", {
  experiment: "comparison",
  update: true,
});
const date = new Date().toISOString();
console.log("date", date);
experiment.updateSpan({
  id: "ee5638b7-d94b-48ad-ba2b-27b00e2edf7f",
  span_attributes: {
    type: "llm",
  },
  //input: `${new Date().toISOString()} yeah`,
  //expected: `${new Date().toISOString()} yeah`,
  input: [
    {
      content:
        "You are a helpful assistant that can solve math problems. Use the calculator tool when you need to perform calculations.",
      role: "system",
    },
    {
      content: `${date} yeah`,
      role: "user",
    },
  ],
  output: [
    {
      finish_reason: "tool_calls",
      index: 0,
      logprobs: null,
      message: {
        role: "assistant",
        tool_calls: [
          {
            function: {
              arguments: '{"a":9876,"b":4567,"op":"subtract"}',
              name: "calculator",
            },
            id: "call_hRKR9sayjDEaBHavwI4Sfe1I",
            index: 0,
            type: "function",
          },
        ],
      },
    },
  ],
});

(async function () {
  await experiment.flush();
  console.log("done");
})();
