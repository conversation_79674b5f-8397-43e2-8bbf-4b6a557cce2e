import { z } from "zod";
import { ObjectIdFields } from "../api-schema/index";
import { IdField, TransactionIdField, CreatedField } from "../query/index";
import { DiscriminatedProjectScore, OriginField } from "../query/index";
import type { TransactionId } from "@braintrust/core";
import { type ObjectReference } from "@braintrust/core/typespecs";

type ObjectIdField = (typeof ObjectIdFields)[number];
type OriginObjectId = {
  [name in ObjectIdField]?: string | null;
};
export type IdentifiableRow = OriginObjectId & {
  [IdField]: string;
  [TransactionIdField]: TransactionId;
};

export function extractIdentifiableRow(
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  fullRow: any,
): Omit<IdentifiableRow, "_xact_id"> {
  return {
    id: fullRow.id,
    ...Object.fromEntries(
      ObjectIdFields.filter((k) => fullRow[k]).map((k) => [k, fullRow[k]]),
    ),
  };
}

export type BaseRow = IdentifiableRow & {
  [CreatedField]: string;
};

const commonMetrics = [
  "start",
  "tokens",
  "time_to_first_token",
  "prompt_tokens",
  "prompt_cached_tokens",
  "prompt_cache_creation_tokens",
  "completion_tokens",
  "estimated_cost",
  "cached",
  "retries",
] as const;

export const spanMetricNames = ["end", ...commonMetrics] as const;

export const systemMetricNames = [
  "offset",
  "duration",
  ...commonMetrics,
] as const;

export type SpanMetrics = Record<
  (typeof spanMetricNames)[number],
  number | undefined
>;

export type SystemMetricName = (typeof systemMetricNames)[number];
export type SystemMetrics = Record<SystemMetricName, number | undefined>;

export const spanAttributesSchema = z.object({
  name: z.string(),
  type: z.string().optional(),
  purpose: z.string().nullish(),
  exec_counter: z.number().optional(),
  remote: z.boolean().optional(),
});

export type SpanData = IdentifiableRow & {
  span_id: string;
  span_attributes: z.infer<typeof spanAttributesSchema>;
  scores: Record<string, number | null>;
  metrics?: SpanMetrics & Record<string, number>;
  tags?: string[];
  [OriginField]?: ObjectReference;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
} & Record<string, any>;

// This is an expanded version of a score that is easier to work with to:
// a) support diffs (if left and right are both present, it's a diff, otherwise right is the current value).
// b) support averages (each array is a list of scores, and for a given span, we display the average).
// c) support writes (spanId, if set, refers to the span that the "right" score is associated with).
export interface SpanScore {
  left: number[];
  right: number[];
  isDiff: boolean;
  spanId?: string;
}
export type ConfiguredScore = Omit<SpanScore, "spanId"> & {
  spanId: string;
  expected?: string[];
  freeForm?: string;
  config: DiscriminatedProjectScore;
};

export interface Span {
  id: string;
  span_id: string;
  root_span_id: string;
  data: SpanData;
  scores: Record<string, SpanScore>;
  parent_span_id: string | null;
  children: Span[];
  span_parents?: string[];
}

export function isRootSpan(span: Span | Record<string, unknown> | undefined) {
  if (!span) {
    return false;
  }
  return span.span_parents && Array.isArray(span.span_parents)
    ? span.span_parents.length === 0
    : span.root_span_id === span.span_id;
}
