from unittest.mock import ANY

from tests.helpers.datetime import iso8601
from tests.helpers.number import number
from tests.helpers.telemetry import csv, ctx, idempotency, prop, unset
from tests.helpers.uuid import uuid
from tests.helpers.xact_id import xact_id


def task():
    return {
        "individual": [
            {
                "event_name": "LogInsertedEvent",
                "external_customer_id": ctx("org_id"),
                "idempotency_key": idempotency(
                    prop("row_id"), prop("object_type"), prop("object_id"), prop("xact_id")
                ),
                "properties": {
                    "log_bytes": number(),
                    "object_id": uuid(),
                    "object_type": "experiment",
                    "org_id": ctx("org_id"),
                    "project_id": uuid(),
                    "root_span_id": uuid(),
                    "row_id": uuid(),
                    "span_attributes_type": "eval",
                    "span_id": uuid(),
                    "span_parents_csv": unset(),
                    "user_id": ctx("user_id"),
                    "xact_id": xact_id(),
                },
                "timestamp": iso8601(),
            },
            {
                "event_name": "LogInsertedEvent",
                "external_customer_id": ctx("org_id"),
                "idempotency_key": idempotency(
                    prop("row_id"), prop("object_type"), prop("object_id"), prop("xact_id")
                ),
                "properties": {
                    "log_bytes": number(),
                    "object_id": uuid(),
                    "object_type": "experiment",
                    "org_id": ctx("org_id"),
                    "project_id": uuid(),
                    "root_span_id": uuid(),
                    "row_id": uuid(),
                    "span_attributes_type": "llm",
                    "span_id": uuid(),
                    "span_parents_csv": csv(uuid()),
                    "user_id": ctx("user_id"),
                    "xact_id": xact_id(),
                },
                "timestamp": iso8601(),
            },
            {
                "event_name": "LogInsertedEvent",
                "external_customer_id": ctx("org_id"),
                "idempotency_key": idempotency(
                    prop("row_id"), prop("object_type"), prop("object_id"), prop("xact_id")
                ),
                "properties": {
                    "log_bytes": number(),
                    "object_id": uuid(),
                    "object_type": "experiment",
                    "org_id": ctx("org_id"),
                    "project_id": uuid(),
                    "root_span_id": uuid(),
                    "row_id": uuid(),
                    "span_attributes_type": "eval",
                    "span_id": uuid(),
                    "span_parents_csv": unset(),
                    "user_id": ctx("user_id"),
                    "xact_id": xact_id(),
                },
                "timestamp": iso8601(),
            },
            {
                "event_name": "LogInsertedEvent",
                "external_customer_id": ctx("org_id"),
                "idempotency_key": idempotency(
                    prop("row_id"), prop("object_type"), prop("object_id"), prop("xact_id")
                ),
                "properties": {
                    "log_bytes": number(),
                    "object_id": uuid(),
                    "object_type": "experiment",
                    "org_id": ctx("org_id"),
                    "project_id": uuid(),
                    "root_span_id": uuid(),
                    "row_id": uuid(),
                    "span_attributes_type": "llm",
                    "span_id": uuid(),
                    "span_parents_csv": csv(uuid()),
                    "user_id": ctx("user_id"),
                    "xact_id": xact_id(),
                },
                "timestamp": iso8601(),
            },
            {
                "event_name": "LogInsertedEvent",
                "external_customer_id": ctx("org_id"),
                "idempotency_key": idempotency(
                    prop("row_id"), prop("object_type"), prop("object_id"), prop("xact_id")
                ),
                "properties": {
                    "log_bytes": number(),
                    "object_id": uuid(),
                    "object_type": "experiment",
                    "org_id": ctx("org_id"),
                    "project_id": uuid(),
                    "root_span_id": uuid(),
                    "row_id": uuid(),
                    "span_attributes_type": "llm",
                    "span_id": uuid(),
                    "span_parents_csv": unset(),
                    "user_id": ctx("user_id"),
                    "xact_id": xact_id(),
                },
                "timestamp": iso8601(),
            },
            {
                "event_name": "LogInsertedEvent",
                "external_customer_id": ctx("org_id"),
                "idempotency_key": idempotency(
                    prop("row_id"), prop("object_type"), prop("object_id"), prop("xact_id")
                ),
                "properties": {
                    "log_bytes": number(),
                    "object_id": uuid(),
                    "object_type": "experiment",
                    "org_id": ctx("org_id"),
                    "project_id": uuid(),
                    "root_span_id": uuid(),
                    "row_id": uuid(),
                    "span_attributes_type": "eval",
                    "span_id": uuid(),
                    "span_parents_csv": unset(),
                    "user_id": ctx("user_id"),
                    "xact_id": xact_id(),
                },
                "timestamp": iso8601(),
            },
            {
                "event_name": "LogInsertedEvent",
                "external_customer_id": ctx("org_id"),
                "idempotency_key": idempotency(
                    prop("row_id"), prop("object_type"), prop("object_id"), prop("xact_id")
                ),
                "properties": {
                    "log_bytes": number(),
                    "object_id": uuid(),
                    "object_type": "experiment",
                    "org_id": ctx("org_id"),
                    "project_id": uuid(),
                    "root_span_id": uuid(),
                    "row_id": uuid(),
                    "span_attributes_type": "llm",
                    "span_id": uuid(),
                    "span_parents_csv": csv(uuid()),
                    "user_id": ctx("user_id"),
                    "xact_id": xact_id(),
                },
                "timestamp": iso8601(),
            },
            {
                "event_name": "LogInsertedEvent",
                "external_customer_id": ctx("org_id"),
                "idempotency_key": idempotency(
                    prop("row_id"), prop("object_type"), prop("object_id"), prop("xact_id")
                ),
                "properties": {
                    "log_bytes": number(),
                    "object_id": uuid(),
                    "object_type": "experiment",
                    "org_id": ctx("org_id"),
                    "project_id": uuid(),
                    "root_span_id": uuid(),
                    "row_id": uuid(),
                    "span_attributes_type": "eval",
                    "span_id": uuid(),
                    "span_parents_csv": unset(),
                    "user_id": ctx("user_id"),
                    "xact_id": xact_id(),
                },
                "timestamp": iso8601(),
            },
            {
                "event_name": "LogInsertedEvent",
                "external_customer_id": ctx("org_id"),
                "idempotency_key": idempotency(
                    prop("row_id"), prop("object_type"), prop("object_id"), prop("xact_id")
                ),
                "properties": {
                    "log_bytes": number(),
                    "object_id": uuid(),
                    "object_type": "experiment",
                    "org_id": ctx("org_id"),
                    "project_id": uuid(),
                    "root_span_id": uuid(),
                    "row_id": uuid(),
                    "span_attributes_type": "llm",
                    "span_id": uuid(),
                    "span_parents_csv": csv(uuid()),
                    "user_id": ctx("user_id"),
                    "xact_id": xact_id(),
                },
                "timestamp": iso8601(),
            },
            {
                "event_name": "LogInsertedEvent",
                "external_customer_id": ctx("org_id"),
                "idempotency_key": idempotency(
                    prop("row_id"), prop("object_type"), prop("object_id"), prop("xact_id")
                ),
                "properties": {
                    "log_bytes": number(),
                    "object_id": uuid(),
                    "object_type": "experiment",
                    "org_id": ctx("org_id"),
                    "project_id": uuid(),
                    "root_span_id": uuid(),
                    "row_id": uuid(),
                    "span_attributes_type": "eval",
                    "span_id": uuid(),
                    "span_parents_csv": unset(),
                    "user_id": ctx("user_id"),
                    "xact_id": xact_id(),
                },
                "timestamp": iso8601(),
            },
            {
                "event_name": "LogInsertedEvent",
                "external_customer_id": ctx("org_id"),
                "idempotency_key": idempotency(
                    prop("row_id"), prop("object_type"), prop("object_id"), prop("xact_id")
                ),
                "properties": {
                    "log_bytes": number(),
                    "object_id": uuid(),
                    "object_type": "experiment",
                    "org_id": ctx("org_id"),
                    "project_id": uuid(),
                    "root_span_id": uuid(),
                    "row_id": uuid(),
                    "span_attributes_type": "llm",
                    "span_id": uuid(),
                    "span_parents_csv": csv(uuid()),
                    "user_id": ctx("user_id"),
                    "xact_id": xact_id(),
                },
                "timestamp": iso8601(),
            },
            {
                "event_name": "LogInsertedEvent",
                "external_customer_id": ctx("org_id"),
                "idempotency_key": idempotency(
                    prop("row_id"), prop("object_type"), prop("object_id"), prop("xact_id")
                ),
                "properties": {
                    "log_bytes": number(),
                    "object_id": uuid(),
                    "object_type": "experiment",
                    "org_id": ctx("org_id"),
                    "project_id": uuid(),
                    "root_span_id": uuid(),
                    "row_id": uuid(),
                    "span_attributes_type": "eval",
                    "span_id": uuid(),
                    "span_parents_csv": unset(),
                    "user_id": ctx("user_id"),
                    "xact_id": xact_id(),
                },
                "timestamp": iso8601(),
            },
            {
                "event_name": "LogInsertedEvent",
                "external_customer_id": ctx("org_id"),
                "idempotency_key": idempotency(
                    prop("row_id"), prop("object_type"), prop("object_id"), prop("xact_id")
                ),
                "properties": {
                    "log_bytes": number(),
                    "object_id": uuid(),
                    "object_type": "experiment",
                    "org_id": ctx("org_id"),
                    "project_id": uuid(),
                    "root_span_id": uuid(),
                    "row_id": uuid(),
                    "span_attributes_type": "llm",
                    "span_id": uuid(),
                    "span_parents_csv": csv(uuid()),
                    "user_id": ctx("user_id"),
                    "xact_id": xact_id(),
                },
                "timestamp": iso8601(),
            },
            {
                "event_name": "LogInsertedEvent",
                "external_customer_id": ctx("org_id"),
                "idempotency_key": idempotency(
                    prop("row_id"), prop("object_type"), prop("object_id"), prop("xact_id")
                ),
                "properties": {
                    "log_bytes": number(),
                    "object_id": uuid(),
                    "object_type": "experiment",
                    "org_id": ctx("org_id"),
                    "project_id": uuid(),
                    "root_span_id": uuid(),
                    "row_id": uuid(),
                    "span_attributes_type": "llm",
                    "span_id": uuid(),
                    "span_parents_csv": unset(),
                    "user_id": ctx("user_id"),
                    "xact_id": xact_id(),
                },
                "timestamp": iso8601(),
            },
        ],
        "aggregated": [
            {
                "event_name": "LogInsertedAggregatedEvent",
                "idempotency_key": ANY,
                "external_customer_id": ctx("org_id"),
                "properties": {
                    "log_bytes": number(gte=1),
                    "metrics_count": number(eq=0),
                    "scores_count": number(eq=0),
                    "count": number(gte=1),
                },
                "timestamp": iso8601(),
            },
        ],
    }
