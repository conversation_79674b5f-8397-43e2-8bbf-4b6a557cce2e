from unittest.mock import ANY

from tests.helpers.datetime import iso8601
from tests.helpers.number import number
from tests.helpers.telemetry import csv, ctx, idempotency, prop, unset
from tests.helpers.uuid import uuid
from tests.helpers.xact_id import xact_id


def task():
    return {
        "individual": [
            {
                "event_name": "LogInsertedEvent",
                "external_customer_id": ctx("org_id"),
                "idempotency_key": idempotency(
                    prop("row_id"), prop("object_type"), prop("object_id"), prop("xact_id")
                ),
                "properties": {
                    "log_bytes": number(),
                    "object_id": uuid(),
                    "object_type": "project_prompts",
                    "org_id": ctx("org_id"),
                    "project_id": uuid(),
                    "root_span_id": uuid(),
                    "row_id": uuid(),
                    "span_attributes_type": unset(),
                    "span_id": uuid(),
                    "span_parents_csv": unset(),
                    "user_id": ctx("user_id"),
                    "xact_id": xact_id(),
                },
                "timestamp": iso8601(),
            },
            {
                "event_name": "LogInsertedEvent",
                "external_customer_id": ctx("org_id"),
                "idempotency_key": idempotency(
                    prop("row_id"), prop("object_type"), prop("object_id"), prop("xact_id")
                ),
                "properties": {
                    "log_bytes": number(),
                    "object_id": uuid(),
                    "object_type": "project_logs",
                    "org_id": ctx("org_id"),
                    "project_id": uuid(),
                    "root_span_id": uuid(),
                    "row_id": uuid(),
                    "span_attributes_type": "task",
                    "span_id": uuid(),
                    "span_parents_csv": unset(),
                    "user_id": ctx("user_id"),
                    "xact_id": xact_id(),
                },
                "timestamp": iso8601(),
            },
            {
                "event_name": "LogInsertedEvent",
                "external_customer_id": ctx("org_id"),
                "idempotency_key": idempotency(
                    prop("row_id"), prop("object_type"), prop("object_id"), prop("xact_id")
                ),
                "properties": {
                    "log_bytes": number(),
                    "object_id": uuid(),
                    "object_type": "project_logs",
                    "org_id": ctx("org_id"),
                    "project_id": uuid(),
                    "root_span_id": uuid(),
                    "row_id": uuid(),
                    "span_attributes_type": "function",
                    "span_id": uuid(),
                    "span_parents_csv": csv(uuid()),
                    "user_id": ctx("user_id"),
                    "xact_id": xact_id(),
                },
                "timestamp": iso8601(),
            },
            {
                "event_name": "LogInsertedEvent",
                "external_customer_id": ctx("org_id"),
                "idempotency_key": idempotency(
                    prop("row_id"), prop("object_type"), prop("object_id"), prop("xact_id")
                ),
                "properties": {
                    "log_bytes": number(),
                    "object_id": uuid(),
                    "object_type": "project_logs",
                    "org_id": ctx("org_id"),
                    "project_id": uuid(),
                    "root_span_id": uuid(),
                    "row_id": uuid(),
                    "span_attributes_type": "llm",
                    "span_id": uuid(),
                    "span_parents_csv": csv(uuid()),
                    "user_id": ctx("user_id"),
                    "xact_id": xact_id(),
                },
                "timestamp": iso8601(),
            },
            {
                "event_name": "FunctionInvokedEvent",
                "external_customer_id": ctx("org_id"),
                "idempotency_key": uuid(),
                "properties": {
                    "duration_ms": number(),
                    "function_data_bytes": number(),
                    "function_data_type": "prompt",
                    "function_id": uuid(),
                    "function_type": unset(),
                    "input_bytes": number(),
                    "invoke_method_type": unset(),
                    "messages_bytes": number(),
                    "mode": unset(),
                    "org_id": ctx("org_id"),
                    "project_id": uuid(),
                    "prompt_data_bytes": number(),
                    "prompt_data_prompt_type": "chat",
                    "root_span_id": uuid(),
                    "row_id": uuid(),
                    "span_id": uuid(),
                    "span_parents_csv": csv(uuid()),
                    "timeout_ms": unset(),
                    "user_id": ctx("user_id"),
                    "xact_id": xact_id(),
                },
                "timestamp": iso8601(),
            },
            {
                "event_name": "LogInsertedEvent",
                "external_customer_id": ctx("org_id"),
                "idempotency_key": idempotency(
                    prop("row_id"), prop("object_type"), prop("object_id"), prop("xact_id")
                ),
                "properties": {
                    "log_bytes": number(),
                    "object_id": uuid(),
                    "object_type": "project_prompts",
                    "org_id": ctx("org_id"),
                    "project_id": uuid(),
                    "root_span_id": uuid(),
                    "row_id": uuid(),
                    "span_attributes_type": unset(),
                    "span_id": uuid(),
                    "span_parents_csv": unset(),
                    "user_id": ctx("user_id"),
                    "xact_id": xact_id(),
                },
                "timestamp": iso8601(),
            },
            {
                "event_name": "FunctionInvokedEvent",
                "external_customer_id": ctx("org_id"),
                "idempotency_key": uuid(),
                "properties": {
                    "duration_ms": number(),
                    "function_data_bytes": number(),
                    "function_data_type": "prompt",
                    "function_id": uuid(),
                    "function_type": unset(),
                    "input_bytes": number(),
                    "invoke_method_type": unset(),
                    "messages_bytes": number(),
                    "mode": unset(),
                    "org_id": ctx("org_id"),
                    "project_id": uuid(),
                    "prompt_data_bytes": number(),
                    "prompt_data_prompt_type": "chat",
                    "root_span_id": uuid(),
                    "row_id": uuid(),
                    "span_id": uuid(),
                    "span_parents_csv": unset(),
                    "timeout_ms": unset(),
                    "user_id": ctx("user_id"),
                    "xact_id": xact_id(),
                },
                "timestamp": iso8601(),
            },
        ],
        "aggregated": [
            {
                "event_name": "LogInsertedAggregatedEvent",
                "idempotency_key": ANY,
                "external_customer_id": ctx("org_id"),
                "properties": {
                    "log_bytes": number(gte=1),
                    "metrics_count": number(eq=0),
                    "scores_count": number(eq=0),
                    "count": number(gte=1),
                },
                "timestamp": iso8601(),
            },
            {
                "event_name": "FunctionInvokedAggregatedEvent",
                "external_customer_id": ctx("org_id"),
                "idempotency_key": ANY,
                "properties": {"count": number(gte=1)},
                "timestamp": iso8601(),
            },
        ],
    }
