import json
import logging
import os
import re
from contextlib import contextmanager
from contextvars import Context<PERSON><PERSON>
from dataclasses import dataclass
from pprint import pformat
from typing import Any, NamedTuple, Optional, Protocol, TypedDict, cast
from urllib.parse import urlparse, urlunparse

import requests
from black import FileMode, format_str

from tests.braintrust_app_test_base import get_test_proxy_base_url
from tests.helpers.number import number
from tests.helpers.uuid import uuid


class Properties(TypedDict):
    span_id: str
    span_parents_csv: str


class Event(TypedDict):
    event_name: str
    timestamp: str
    properties: Properties


class StringCompare(Protocol):
    def __eq__(self, other: Any) -> bool:
        ...


def _normalize_proxy_url(url: str) -> str:
    parsed = urlparse(url)
    netloc = parsed.netloc.replace("0.0.0.0", "localhost")
    path = "/v1"
    return urlunparse((parsed.scheme, netloc, path, "", "", ""))


@dataclass
class Context:
    org_id: Optional[str] = None
    user_id: Optional[str] = None
    app_url: Optional[str] = None
    proxy_url: Optional[str] = None
    event: Optional[Event] = None

    def __post_init__(self):
        # api-ts uses localhost:8000/v1, but the proxy server config may use 0.0.0.0:8000
        if self.proxy_url:
            self.proxy_url = _normalize_proxy_url(self.proxy_url)


_current_context: ContextVar[Optional[Context]] = ContextVar("current_context", default=None)


@contextmanager
def current_context(context: Context):
    token = _current_context.set(context)
    try:
        yield
    finally:
        _current_context.reset(token)


class ctx(StringCompare):
    name: str

    def __init__(self, name: str):
        self.name = name

    def __eq__(self, value: str) -> bool:
        current = _current_context.get()
        assert current is not None
        return getattr(current, self.name) == value

    def __repr__(self):
        return f"ctx('{self.name}')"


class prop(StringCompare):
    name: str

    def __init__(self, name: str):
        self.name = name

    def __eq__(self, value: str) -> bool:
        current = _current_context.get()
        assert current and current.event is not None
        return cast(str, current.event["properties"][self.name]) == value

    def __repr__(self):
        return f"prop('{self.name}')"


class idempotency(StringCompare):
    args: list[str | uuid | prop | number]

    def __init__(self, *args: str | uuid | prop | number):
        self.args = list(args)

    def __eq__(self, other: str):
        return list(self.args) == json.loads(other)

    def __repr__(self):
        return f"idempotency({', '.join([repr(arg) for arg in self.args])})"


class unset(StringCompare):
    def __eq__(self, other: str):
        return other == "__UNSET__"

    def __repr__(self):
        return "unset()"


class csv(StringCompare):
    args: list[str | uuid | prop]

    def __init__(self, *args: str | uuid | prop):
        self.args = list(args)

    def __eq__(self, other: str):
        return list(self.args) == other.split(",")

    def __repr__(self):
        return f"csv({', '.join([repr(arg) for arg in self.args])})"


def dedupe_events(events: list[Event]) -> list[Event]:
    event_name_to_span_id: dict[str, Event] = {}

    # build a map of all spans and their latest event (due to async nature of sdk we may get multiple events for the same span)
    # we need to be careful with events sharing the same span_id, as they may have different event_name
    for event in events:
        event_name = event["event_name"]
        props = event["properties"]
        span_id = props["span_id"]
        event_name_span_id = f"{event_name}:{span_id}"

        if (
            event_name_span_id not in event_name_to_span_id
            or event["timestamp"] > event_name_to_span_id[event_name_span_id]["timestamp"]
        ):
            event_name_to_span_id[event_name_span_id] = event

    return list(event_name_to_span_id.values())


def to_expected_from_events(events: list[Event]):
    content = format_str(pformat(dedupe_events(events)), mode=FileMode())

    global migrations
    for m in migrations:
        content = re.sub(m.pattern, m.replacement, content)

    return content


class Migration(NamedTuple):
    pattern: str
    replacement: str


# as of 2025-01-29
migrations: list[Migration] = [
    Migration(
        pattern=r'[\'"]external_customer_id[\'"]: .+,',
        replacement=r'"external_customer_id": ctx("org_id"),',
    ),
    Migration(
        pattern=r'[\'"]idempotency_key[\'"]: [\'"]?\[(?:[\'"]([^\'",]+)[\'"],){3}[\'"]([^\'",]+)[\'"]?\][\'"]?,',
        replacement=r'"idempotency_key": idempotency(prop("row_id"), prop("object_type"), prop("object_id"), prop("xact_id")),',
    ),
    Migration(
        pattern=r'[\'"]idempotency_key[\'"]: [\'"]?\[(?:[\'"]([^\'",]+)[\'"],)(\d+)?\][\'"]?,',
        replacement=r'"idempotency_key": uuid(),',
    ),
    # remove app_origin and proxy_url from properties. may be different in CI docker tests!
    Migration(
        pattern=r'[\'"](app_origin|proxy_url)[\'"]: .+,\s+',
        replacement=r"",
    ),
    Migration(
        pattern=r'[\'"](log_bytes|duration_ms|function_data_bytes|input_bytes|messages_bytes|prompt_data_bytes)[\'"]: (\d+),',
        replacement=r'"\1": number(),',
    ),
    Migration(
        pattern=r'[\'"]timestamp[\'"]: .+,',
        replacement=r'"timestamp": iso8601(),',
    ),
    Migration(
        pattern=r'[\'"]__UNSET__[\'"],',
        replacement=r"unset(),",
    ),
    Migration(
        pattern=r'[\'"](object_id|function_id|project_id|root_span_id|row_id|span_id)[\'"]: ["\']\b[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-5][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}\b["\'],',
        replacement=r'"\1": uuid(),',
    ),
    Migration(
        pattern=r'[\'"](org_id|user_id)[\'"]: .+,',
        replacement=r'"\1": ctx("\1"),',
    ),
    Migration(
        pattern=r'[\'"]xact_id[\'"]: .+,',
        replacement=r'"xact_id": xact_id(),',
    ),
    Migration(
        pattern=r'[\'"]span_parents_csv[\'"]: ([\'"].+),',
        replacement=r'"span_parents_csv": csv(uuid()),',
    ),
]


def get_events(org_id: str):
    return requests.get(f"{get_test_proxy_base_url()}/events/{org_id}").json()


def clear_events(org_id: str):
    return requests.delete(f"{get_test_proxy_base_url()}/events/{org_id}").raise_for_status()


def aggregate_events(org_id: str, events: Optional[list[Event]] = None):
    with telemetry_event_aggregation(org_id, enabled=True):
        result = requests.post(
            f"{get_test_proxy_base_url()}/events/{org_id}/aggregate", json={"events": events}
        ).json()

    if len(result["original"]) > 0:
        logging.warn("May unexpectedly not have aggregated all events")

    return result["aggregated"]


@contextmanager
def telemetry_event_aggregation(org_id, *, enabled=False):
    original = os.getenv("TELEMETRY_ENABLE_AGGREGATION")

    requests.post(
        f"{get_test_proxy_base_url()}/events/{org_id}/env",
        json={"TELEMETRY_ENABLE_AGGREGATION": "true" if enabled else ""},
    ).json()

    try:
        yield
    finally:
        requests.delete(
            f"{get_test_proxy_base_url()}/events/{org_id}/env",
        ).json()
